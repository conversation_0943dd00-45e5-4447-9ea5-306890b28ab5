name: Open VSX Deploy

on:
  release:
    types: [published]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js environment
        uses: actions/setup-node@v3
        with:
          node-version: 22.5.1
          registry-url: https://registry.npmjs.org/

      - name: Install dependencies
        run: yarn --immutable

      - name: Build & deploy to Open VSX
        run: yarn deploy:ovsx -p ${{ secrets.OVSX_ACCESS_TOKEN }}
        env:
          NODE_OPTIONS: "--max_old_space_size=8192"
