name: Tests

on:
  push:
  pull_request:

jobs:
  run-tests:
    # Use MacOS so we can run VS Code (eg. https://github.com/microsoft/vscode-test/issues/230)
    runs-on: macos-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js environment
        uses: actions/setup-node@v3
        with:
          node-version: 22.5.1
          registry-url: https://registry.npmjs.org/

      - name: Install dependencies
        run: yarn --immutable

      - name: Run CI Tests
        run: yarn test:ci
