// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
    "out": false // set this to true to hide the "out" folder with the compiled JS files
  },
  "search.exclude": {
    "out": true // set this to false to include "out" folder in search results
  },
  // Turn off tsc task auto detection since we have the necessary tasks as npm scripts
  "typescript.tsc.autoDetect": "off",
  "peacock.color": "#34223A",
  "workbench.colorCustomizations": {
    "activityBar.background": "#51355a",
    "activityBar.activeBorder": "#000000",
    "activityBar.foreground": "#e7e7e7",
    "activityBar.inactiveForeground": "#e7e7e799",
    "activityBarBadge.background": "#000000",
    "activityBarBadge.foreground": "#e7e7e7",
    "titleBar.activeBackground": "#34223a",
    "titleBar.inactiveBackground": "#34223a99",
    "titleBar.activeForeground": "#e7e7e7",
    "titleBar.inactiveForeground": "#e7e7e799",
    "statusBar.background": "#34223a",
    "statusBarItem.hoverBackground": "#51355a",
    "statusBar.foreground": "#e7e7e7",
    "activityBar.activeBackground": "#51355a",
    "sash.hoverBorder": "#51355a",
    "statusBarItem.remoteBackground": "#34223a",
    "statusBarItem.remoteForeground": "#e7e7e7",
    "commandCenter.border": "#e7e7e799"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
