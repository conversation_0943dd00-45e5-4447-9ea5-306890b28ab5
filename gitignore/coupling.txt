entity,coupled,degree,average-revs
src/refactorings/move-statement-down.test.ts,src/refactorings/move-statement-up.test.ts,82,15
src/refactorings/move-statement-down.ts,src/refactorings/move-statement-up.ts,76,13
src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts,src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts,74,14
src/refactorings/inline-function/inline-function.test.ts,src/refactorings/inline-function/inline-function.ts,73,21
src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts,src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts,71,7
src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts,src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts,70,10
src/refactorings/move-statement-down/move-statement-down.test.ts,src/refactorings/move-statement-down/move-statement-down.ts,69,23
src/refactorings/extract-generic-type/extract-generic-type.test.ts,src/refactorings/extract-generic-type/extract-generic-type.ts,68,25
src/refactorings/remove-dead-code/remove-dead-code.test.ts,src/refactorings/remove-dead-code/remove-dead-code.ts,68,18
src/refactorings/negate-expression.test.ts,src/refactorings/negate-expression.ts,66,21
src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts,src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts,66,17
src/editor/path.test.ts,src/editor/path.ts,66,14
src/refactorings/rename-symbol/rename-symbol.test.ts,src/refactorings/rename-symbol/rename-symbol.ts,66,8
src/editor/adapters/attempting-editor.ts,src/editor/editor.ts,64,36
src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts,src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts,64,14
src/refactorings/split-if-statement/split-if-statement.test.ts,src/refactorings/split-if-statement/split-if-statement.ts,63,11
src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts,src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts,63,10
src/refactorings/move-statement-down.test.ts,src/refactorings/move-statement-down.ts,62,15
src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts,src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts,62,15
src/refactorings/flip-ternary/flip-ternary.test.ts,src/refactorings/flip-ternary/flip-ternary.ts,62,8
src/refactorings/add-braces-to-arrow-function/index.ts,src/refactorings/remove-braces-from-arrow-function/index.ts,62,8
src/editor/source-change.ts,src/highlights/toggle-highlight/toggle-highlight.test.ts,61,13
src/editor/adapters/in-memory-editor.ts,src/editor/editor.ts,60,53
src/refactorings/inline-variable-or-function/inline-function.test.ts,src/refactorings/inline-variable-or-function/inline-function.ts,60,15
src/refactorings/convert-if-else-to-ternary.test.ts,src/refactorings/convert-if-else-to-ternary.ts,58,9
src/refactorings/extract-variable.test.ts,src/refactorings/extract-variable.ts,57,61
src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts,src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts,56,29
src/refactorings/toggle-braces/toggle-braces.test.ts,src/refactorings/toggle-braces/toggle-braces.ts,56,20
src/refactorings/flip-if-else/flip-if-else.test.ts,src/refactorings/flip-if-else/flip-if-else.ts,56,20
src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts,src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts,56,13
src/refactorings/remove-redundant-else.test.ts,src/refactorings/remove-redundant-else.ts,55,15
src/refactorings/extract/index.ts,src/refactorings/inline/index.ts,55,9
src/refactorings/inline/inline-variable/find-inlinable-code.ts,src/refactorings/inline/inline-variable/inline-variable.test.ts,53,23
src/editor/adapters/change-signature-webview/change-signature.html,src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts,53,15
src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts,src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts,53,13
src/refactorings/move-statement-up.test.ts,src/refactorings/move-statement-up.ts,53,13
src/refactorings/merge-if-statements/index.ts,src/refactorings/replace-binary-with-assignment/index.ts,52,12
src/refactorings/remove-redundant-else/remove-redundant-else.test.ts,src/refactorings/remove-redundant-else/remove-redundant-else.ts,52,12
src/editor/adapters/vscode-editor.ts,src/editor/editor.ts,51,70
src/refactorings/negate-expression/negate-expression.test.ts,src/refactorings/negate-expression/negate-expression.ts,50,18
src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts,src/refactorings/extract/extract-generic-type/extract-generic-type.ts,50,12
src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts,src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts,50,12
src/refactorings/convert-ternary-to-if-else.test.ts,src/refactorings/convert-ternary-to-if-else.ts,50,10
src/refactorings/move-to-existing-file/move-to-existing-file.test.ts,src/refactorings/move-to-existing-file/move-to-existing-file.ts,49,45
src/editor/adapters/attempting-editor.ts,src/editor/adapters/in-memory-editor.ts,49,43
src/refactorings/negate-expression/index.ts,src/refactorings/replace-binary-with-assignment/index.ts,48,13
src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts,src/refactorings/inline-variable-or-function/find-inlinable-code.ts,47,17
src/editor/adapters/in-memory-editor.ts,src/editor/adapters/vscode-editor.ts,46,77
src/refactorings/merge-if-statements/index.ts,src/refactorings/negate-expression/index.ts,46,13
src/refactorings/change-signature/change-signature.test.ts,src/refactorings/change-signature/change-signature.ts,43,37
src/refactorings/move-statement-down/move-statement-down.ts,src/refactorings/move-statement-up/move-statement-up.ts,42,26
src/refactorings/move-statement-down.ts,src/refactorings/move-statement-up.test.ts,42,14
src/commands.ts,src/refactoring-command.ts,41,31
src/refactorings/inline-variable-or-function/inline-variable.test.ts,src/refactorings/inline-variable-or-function/inline-variable.ts,39,23
src/refactorings/extract-variable/extract-variable.extractable.test.ts,src/refactorings/extract-variable/extract-variable.ts,38,42
src/refactorings/move-statement-down/move-statement-down.test.ts,src/refactorings/move-statement-up/move-statement-up.test.ts,38,21
src/refactorings/negate-expression/index.ts,src/refactorings/negate-expression/negate-expression.ts,38,18
src/refactorings/inline-variable-or-function/find-inlinable-code.ts,src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts,38,18
src/refactorings/inline-function/find-param-matching-id.ts,src/refactorings/inline-function/inline-function.test.ts,37,16
src/refactorings/move-statement-down.test.ts,src/refactorings/move-statement-up.ts,37,14
src/editor/adapters/attempting-editor.ts,src/editor/adapters/vscode-editor.ts,36,60
src/refactorings/extract-variable.test.ts,src/refactorings/selection.ts,35,48
src/refactorings/merge-if-statements/merge-if-statements.test.ts,src/refactorings/merge-if-statements/merge-if-statements.ts,35,37
src/refactorings/position.ts,src/refactorings/selection.ts,35,17
src/refactorings/ast.ts,src/refactorings/extract-variable.ts,33,39
src/action-providers.ts,src/commands.ts,32,62
src/refactorings/merge-if-statements/index.ts,src/types.ts,32,19
src/refactorings/extract/extract-variable/extract-variable.ts,src/refactorings/extract/extract-variable/occurrence.ts,31,48
src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts,src/refactorings/extract/extract-variable/occurrence.ts,31,41
src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts,src/refactorings/extract/extract-variable/extract-variable.ts,31,29
src/refactorings/extract-variable/extract-variable.test.ts,src/refactorings/extract-variable/extract-variable.ts,30,40
src/commands.ts,src/refactoring.ts,30,30
src/refactorings/move-statement-down/move-statement-down.test.ts,src/refactorings/move-statement-up/move-statement-up.ts,30,23
src/refactorings/flip-ternary/flip-ternary.ts,src/refactorings/negate-expression/negate-expression.ts,30,17
