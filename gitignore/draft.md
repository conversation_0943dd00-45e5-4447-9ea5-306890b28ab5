Hello {{ subscriber.first_name | strip | default: "there" }} 👋
I hope you and your loved ones are doing well.
This month, …

--

I practiced on the Lift Pass Pricing refactoring kata (https://github.com/martinsson/Refactoring-Kata-Lift-Pass-Pricing). It's available in different languages and seasonally on point ❄️ 🎿

You have a simple, but tangled and not re-usable piece of code. You need to implement a new feature.

The recommended approach is:

1. put high-level, integrated tests (with a DB running)
2. refactor the code so you can unit test it and re-use it
3. pull down tests to be unit instead of integrated
4. implement the new feature, guided by a test (TDD).

It's a good, realistic exercise. I'm trying a slightly different approach: 1. start with safe refactoring to make the code testable in isolation 2. write high-level, isolated tests 3. implement the new feature.

End result isn't as perfect (I have some blind spots) but implementation is faster. I'd say I get 80% of the benefits in half of the time. It requires a few things to do properly (tooling, mastering some refactorings… static typings help).

TK: similar to previous month with <PERSON><PERSON>'s approach
TK: have a blog post to share these notes?

--

TK: Tweet about using the Builder pattern, end of Jan (https://twitter.com/nicoespeon/status/1486020141380952068)

--

TK: Things you can do when inheriting a legacy codebase:

💠 Ask others what you don't understand
💠 Capture what you learn in a document
💠 Build a glossary of the jargon used by the team
💠 Rename variables/functions that confused you at first glance (don't get confused again)

Others' advice (https://twitter.com/nicoespeon/status/1491447831613296647):

- When you get started on a legacy codebase, don't put yourself under pressure to understand how it all works before making changes. Your codebase knowledge will grow over time, and a partial understanding is often enough to make first improvements and enhancements.
- Read: "Working Effectively with Legacy Code" by @mfeathers
- Learn how to find or create seams for testing.
- Do not change things without establishing tests for the code you are about to change.
- If you get overwhelmed, step away, have a longish break. You need fresh eyes.
- Approval based with https://approvaltests.com might also be a valuable tool under your belt to get the first Tests going
- Scratch refactoring: before writing a new feature/fix a bug, create a branch and try to refactor the code until you understand it. You can throw away the branch afterwards.
- Write characterization tests before you touch anything. Unless you like production support on a system that you don't understand.
- Focus on making it better, not making it perfect

--

TK: talking about Twitter, there is a new community dedicated to tweeting about Software Maintenance: https://twitter.com/i/communities/1493270844834988035

--

TK: Different, useful katas to master: https://twitter.com/oracle2025/status/1490703400127901699
