--1e4be3d6--2024-05-17--<PERSON>
4	1	CHANGELOG.md
1	1	package.json

--9c448104--2024-05-17--<PERSON>
--49c18c9e--2024-05-17--<PERSON>
--5d65f7d0--2024-05-17--dependabot[bot]
1	1	package.json
42	36	yarn.lock

--7de599dd--2024-05-16--<PERSON>
--47fc51a2--2024-05-16--<PERSON>
7	6	README.md
4	0	REFACTORINGS.md
5	0	package.json
7	1	src/extension.ts

--4af15b3f--2024-05-16--<PERSON>
20	7	src/editor/adapters/vscode-editor.ts
6	0	src/editor/editor.ts
4	4	src/extension.ts

--2533e836--2024-05-08--<PERSON>
23	13	src/editor/adapters/vscode-editor.ts

--318f0887--2024-05-08--<PERSON>
2	1	package.json
8	28	yarn.lock

--5203002f--2024-05-08--<PERSON>
1	1	src/editor/adapters/vscode-editor.ts
20	1	src/extension.ts
57	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
23	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--1ea053cc--2024-05-16--Nicolas Carlo
1	0	CHANGELOG.md
26	2	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
18	4	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--906dd9c5--2024-05-07--Nicolas Carlo
3	3	src/extension.ts

--90db112c--2024-05-16--Nicolas Carlo
--cddd1a3c--2024-05-16--allcontributors[bot]
9	0	.all-contributorsrc

--4caefc70--2024-05-16--allcontributors[bot]
2	1	README.md

--cf089689--2024-05-16--Nicolas Carlo
--f46552a8--2024-05-16--allcontributors[bot]
9	0	.all-contributorsrc

--9cb95763--2024-05-16--allcontributors[bot]
2	1	README.md

--84b84215--2024-05-16--Nicolas Carlo
4	0	CHANGELOG.md
6	3	src/action-providers.ts
3	2	src/commands.ts

--a9243323--2024-05-07--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--34ade897--2024-05-07--Nicolas Carlo
4	0	src/editor/adapters/vscode-editor.ts
23	6	src/extension.ts
2	0	src/playground/maths/calculator.ts
0	48	src/ts-server-plugin.ts
145	0	src/ts-server-plugin/index.js
176	0	src/ts-server-plugin/index.ts
1	1	tsconfig.json
6	28	yarn.lock

--5c284154--2024-05-06--Nicolas Carlo
6	2	.vscode/launch.json
9	1	package.json
25	1	src/extension.ts
48	0	src/ts-server-plugin.ts
8	0	tsconfig.json

--d78eaaf4--2024-05-06--Nicolas Carlo
0	3	.husky/pre-commit
1	1	package.json

--ab1e5c7b--2024-05-06--Nicolas Carlo
--34eca3d5--2024-05-07--dependabot[bot]
1	1	package.json
56	101	yarn.lock

--c3a60d8c--2024-05-06--Nicolas Carlo
--4c8960a4--2024-05-06--Nicolas Carlo
0	1	package.json
290	0	src/refactorings/extract/extract-variable/changeCase.ts
1	2	src/refactorings/extract/extract-variable/variable.ts
8	158	yarn.lock

--57d6d174--2024-05-06--Nicolas Carlo
4	0	CHANGELOG.md
1	1	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--335571d2--2024-05-01--Nicolas Carlo
--edc7aebd--2024-05-01--dependabot[bot]
1	1	package.json
151	218	yarn.lock

--9b4712ae--2024-05-01--dependabot[bot]
3	3	yarn.lock

--5ef33ce2--2024-05-01--Nicolas Carlo
--f2b5adb9--2024-05-01--Nicolas Carlo
--55e22bba--2024-05-01--dependabot[bot]
1	1	package.json
32	9	yarn.lock

--7dcabbb0--2024-03-19--Nicolas Carlo
--f5d80a86--2024-03-16--dependabot[bot]
3	3	yarn.lock

--b0aead0f--2024-03-01--dependabot[bot]
1	1	package.json
171	182	yarn.lock

--4a965f67--2024-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--95aec1ad--2024-03-01--dependabot[bot]
1	1	package.json
32	38	yarn.lock

--9700e8a8--2024-03-01--dependabot[bot]
1	1	package.json
72	84	yarn.lock

--96b44c83--2024-02-26--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--5c51abdd--2024-02-26--Nicolas Carlo
4	0	CHANGELOG.md

--d9eacb56--2024-02-26--Nicolas Carlo
--b11f3232--2024-02-26--allcontributors[bot]
9	0	.all-contributorsrc

--f840d532--2024-02-26--allcontributors[bot]
2	1	README.md

--4b3561a0--2024-02-26--Nicolas Carlo
11	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
27	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--2de29048--2024-02-26--Nicolas Carlo
9	6	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--1943de9e--2024-02-01--dependabot[bot]
2	2	package.json
23	23	yarn.lock

--aaf5dd79--2024-02-01--Nicolas Carlo
--a35a6c41--2024-02-01--Nicolas Carlo
--ba5fe69a--2024-02-01--Nicolas Carlo
--d5167992--2024-02-01--dependabot[bot]
1	1	package.json
48	74	yarn.lock

--564c3fcb--2024-02-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--2709962b--2024-02-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--02dbbd9b--2024-01-09--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--8cde51f6--2024-01-09--Nicolas Carlo
1	1	package.json

--a501a131--2024-01-08--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--ff1004ae--2024-01-08--Nicolas Carlo
--834da28b--2024-01-08--Nicolas Carlo
4	0	CHANGELOG.md
1	0	README.md
5	0	package.json
5	2	src/action-providers.ts
15	0	src/vscode-configuration.ts

--c0ee1fb3--2024-01-08--Nicolas Carlo
4	0	CHANGELOG.md

--761ab6a3--2024-01-09--dependabot[bot]
3	3	yarn.lock

--dc41ab78--2024-01-08--Nicolas Carlo
0	1	.github/workflows/tests.yml

--57a72795--2024-01-08--Nicolas Carlo
--dc31bacb--2024-01-01--Nicolas Carlo
--3b17d4b4--2024-01-01--Nicolas Carlo
--3af2a3a5--2024-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--05b7aa1c--2024-01-01--dependabot[bot]
1	1	package.json
38	23	yarn.lock

--51c00031--2024-01-01--Nicolas Carlo
--e2cec6c5--2024-01-01--Nicolas Carlo
--c4d6383d--2024-01-01--Nicolas Carlo
--752cdd72--2024-01-01--Nicolas Carlo
--e3c0d1dd--2024-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--9f4f0a91--2024-01-01--dependabot[bot]
1	1	package.json
96	50	yarn.lock

--950f9868--2024-01-01--dependabot[bot]
1	1	package.json
77	51	yarn.lock

--28975d12--2024-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--38abcb11--2023-12-29--j4k0xb
5	11	src/action-providers.ts

--3f07a13b--2023-12-29--Nicolas Carlo
--e1473518--2023-12-29--dependabot[bot]
1	1	package.json
44	9	yarn.lock

--89548b2b--2023-12-29--Nicolas Carlo
--61ed5a08--2023-12-29--Nicolas Carlo
--a4e0ab4c--2023-12-29--Nicolas Carlo
--96743e51--2023-12-29--Nicolas Carlo
--8b5f32d7--2023-12-29--Nicolas Carlo
--993ee68e--2023-12-29--dependabot[bot]
1	1	package.json
5	10	yarn.lock

--43289247--2023-12-29--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--f0185f1e--2023-12-29--dependabot[bot]
2	2	package.json
8	8	yarn.lock

--2d9edf8e--2023-12-29--dependabot[bot]
1	1	package.json
2	119	yarn.lock

--a5665b70--2023-12-29--Nicolas Carlo
--70258f65--2023-12-29--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--1f99c42c--2023-12-29--Nicolas Carlo
--1e34929d--2023-12-29--Nicolas Carlo
--cffeb862--2023-12-29--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--345ec7ff--2023-12-29--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--101da271--2023-12-29--dependabot[bot]
1	1	package.json
1	139	yarn.lock

--62d26dfe--2023-12-29--Nicolas Carlo
--d3a770b8--2023-12-29--Nicolas Carlo
--dfc6d561--2023-12-29--allcontributors[bot]
2	1	.all-contributorsrc

--79a27b50--2023-12-29--allcontributors[bot]
1	1	README.md

--7412ac47--2023-12-29--Nicolas Carlo
3	1	.github/workflows/tests.yml

--c9aeaf26--2023-12-29--Nicolas Carlo
1	1	docs/adr/0011-use-babel-jest-instead-of-ts-jest.md
48	0	docs/adr/0015-use-esbuild-to-build-instead-of-webpack.md

--6a82f597--2023-12-29--Nicolas Carlo
7	184	yarn.lock

--dbb5da1d--2023-12-29--Nicolas Carlo
9	0	CONTRIBUTING.md

--9aa7c4c1--2023-12-27--j4k0xb
3	1	tsconfig.json

--9cdd62e9--2023-12-27--j4k0xb
0	1	esbuild.config.js
2	2	package.json

--e5a4f7ee--2023-12-26--j4k0xb
1	1	esbuild.config.js

--c1549c74--2023-12-26--j4k0xb
0	3	.github/dependabot.yml
0	1	.gitignore
1	1	.vscode/tasks.json
37	25	esbuild.config.js
6	6	package.json
388	5	yarn.lock

--4596004e--2023-12-15--j4k0xb
0	6	package.json
6	87	yarn.lock

--f8a2a315--2023-12-14--j4k0xb
1	0	.gitignore
49	0	esbuild.config.js
6	9	package.json
0	109	webpack.config.js
632	620	yarn.lock

--b10f3b51--2023-12-06--Nicolas Carlo
--a58755cd--2023-12-06--Nicolas Carlo
1	1	src/refactorings/move-statement-down/move-statement-down.ts

--ef66551f--2023-12-06--Nicolas Carlo
2	2	package.json
99	124	yarn.lock

--26b92b2e--2023-12-06--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--724ab42c--2023-12-05--Nicolas Carlo
--27a92dd4--2023-12-05--Nicolas Carlo
1	4	package.json
54	46	yarn.lock

--ee9916d8--2023-12-05--Nicolas Carlo
0	2	src/ast/scope.ts
7	4	src/refactorings/move-statement-down/move-statement-down.ts
2	1	src/refactorings/move-statement-up/move-statement-up.ts

--6906c40c--2023-12-05--dependabot[bot]
2	2	package.json
38	130	yarn.lock

--e3721ea2--2023-12-05--Nicolas Carlo
--b245f9a4--2023-12-05--Nicolas Carlo
--421f7565--2023-12-05--dependabot[bot]
1	1	package.json
7	92	yarn.lock

--83049ec3--2023-12-05--dependabot[bot]
1	1	package.json
4	14	yarn.lock

--65d68b86--2023-12-05--Nicolas Carlo
--8125ed67--2023-12-05--Nicolas Carlo
--e41eabfb--2023-12-05--Nicolas Carlo
--142995e3--2023-12-05--Nicolas Carlo
--ba2acb93--2023-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--86469544--2023-12-01--dependabot[bot]
1	1	package.json
339	356	yarn.lock

--cdbb7237--2023-12-01--dependabot[bot]
1	1	package.json
11	4	yarn.lock

--4b9d880d--2023-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--d9d9657c--2023-11-01--Nicolas Carlo
--bb169f25--2023-11-01--Nicolas Carlo
--9920d6ac--2023-11-02--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--ffbe8f3c--2023-11-01--Nicolas Carlo
--7394e302--2023-11-01--Nicolas Carlo
--447a4aaf--2023-11-02--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--35bb1071--2023-11-01--dependabot[bot]
1	1	package.json
20	81	yarn.lock

--ba674afd--2023-11-01--Nicolas Carlo
--ed76973d--2023-11-01--Nicolas Carlo
--bd16061a--2023-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--8c92fa89--2023-11-01--Nicolas Carlo
--fbf363d5--2023-11-01--Nicolas Carlo
--6ca5c797--2023-11-01--Nicolas Carlo
--8f0d5e53--2023-11-01--Nicolas Carlo
--97b30c97--2023-11-01--dependabot[bot]
1	1	package.json
13	13	yarn.lock

--24821e97--2023-11-01--dependabot[bot]
1	1	package.json
55	23	yarn.lock

--ce3f19e6--2023-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ea3e0b90--2023-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e3b48231--2023-11-01--dependabot[bot]
1	1	package.json
266	199	yarn.lock

--ce1ce9b5--2023-11-01--dependabot[bot]
1	1	package.json
18	11	yarn.lock

--a2e18926--2023-10-28--Nicolas Carlo
0	7	src/type-checker/adapters/console-logger.ts
0	5	src/type-checker/adapters/noop-logger.ts
0	3	src/type-checker/index.ts
0	3	src/type-checker/logger.ts
0	55	src/type-checker/ts-position.test.ts
0	32	src/type-checker/ts-position.ts

--40a1267d--2023-10-28--Nicolas Carlo
8	4	CHANGELOG.md
1	1	package.json

--d85fe135--2023-10-28--Nicolas Carlo
--b9efebb5--2023-10-28--Nicolas Carlo
4	4	package.json
548	362	yarn.lock

--209a0b7b--2023-10-28--Nicolas Carlo
--ac458520--2023-10-28--Nicolas Carlo
2	1	.github/workflows/tests.yml

--b64fdf35--2023-10-28--Nicolas Carlo
1	1	.github/workflows/tests.yml

--a2df9b21--2023-10-28--Nicolas Carlo
30	0	docs/adr/0014-use-github-actions-instead-of-travis.md

--2d442020--2023-10-28--Nicolas Carlo
0	20	.travis.yml
0	1	README.md

--86b81972--2023-10-28--Nicolas Carlo
24	0	.github/workflows/tests.yml

--4445795b--2023-10-28--Nicolas Carlo
7	3	.github/workflows/ovsx-deploy.yml
7	3	.github/workflows/vscode-deploy.yml

--e6bc6f55--2023-10-28--Nicolas Carlo
--be3af6bb--2023-10-28--Nicolas Carlo
1	1	webpack.config.js

--d1812c4b--2023-10-28--Nicolas Carlo
0	8	src/editor/error-reason.ts

--cd440147--2023-10-28--Nicolas Carlo
1	9	webpack.config.js

--e36c00f8--2023-10-28--Nicolas Carlo
4	7	package.json
2	29	yarn.lock

--58e68428--2023-10-28--Nicolas Carlo
1	0	CHANGELOG.md
2	16	REFACTORINGS.md
-	-	docs/demo/extract-use-callback.gif
1	6	src/extension.ts
0	125	src/refactorings/react/extract-use-callback/extract-use-callback.test.ts
0	177	src/refactorings/react/extract-use-callback/extract-use-callback.ts
0	16	src/refactorings/react/extract-use-callback/index.ts

--e8669c6f--2023-10-28--Nicolas Carlo
1	3	src/action-providers.ts
0	1	src/type-checker/index.ts
0	197	src/type-checker/type-checker.test.ts
0	161	src/type-checker/type-checker.ts
1	3	src/types.ts

--58d06027--2023-10-28--Nicolas Carlo
4	0	CHANGELOG.md
0	16	REFACTORINGS.md
-	-	docs/demo/destructure-object.gif
0	34	package.json
0	4	src/editor/error-reason.ts
0	2	src/extension.ts
0	257	src/refactorings/destructure-object/destructure-object.test.ts
0	91	src/refactorings/destructure-object/destructure-object.ts
0	17	src/refactorings/destructure-object/index.ts

--8e880372--2023-10-28--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--63c27254--2023-10-28--Nicolas Carlo
1	1	package.json

--1b4003cf--2023-10-28--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--4bdb878f--2023-10-28--Nicolas Carlo
--daf52d62--2023-10-28--Nicolas Carlo
4	0	CHANGELOG.md
5	4	README.md
5	0	package.json
12	6	src/action-providers.ts
15	0	src/vscode-configuration.ts

--31cdad52--2023-10-20--Nicolas Carlo
4	0	CHANGELOG.md
1	1	README.md
0	18	REFACTORINGS.md
-	-	docs/demo/move-to-existing-file.gif
0	26	package.json
0	11	src/extension.ts
0	58	src/refactorings/move-to-existing-file/export-declaration.ts
0	17	src/refactorings/move-to-existing-file/index.ts
0	196	src/refactorings/move-to-existing-file/movable-node.ts
0	632	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
0	179	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--4a999e2e--2023-10-20--Nicolas Carlo
2	2	package.json
0	15	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
97	185	yarn.lock

--39c93124--2023-10-11--Nicolas Carlo
--05b95a87--2023-10-11--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--eb30b679--2023-10-11--Nicolas Carlo
--7f617f3f--2023-10-11--Nicolas Carlo
--df3cd2ef--2023-10-11--Nicolas Carlo
--1d064c34--2023-10-11--Nicolas Carlo
--6ded1599--2023-10-11--Nicolas Carlo
--f186172a--2023-10-11--Nicolas Carlo
--91185e0e--2023-10-01--dependabot[bot]
1	1	package.json
84	69	yarn.lock

--10e98c8c--2023-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--df525469--2023-09-27--dependabot[bot]
3	3	yarn.lock

--094011ed--2023-09-01--dependabot[bot]
1	1	package.json
19	19	yarn.lock

--1fcf2272--2023-09-01--dependabot[bot]
1	1	package.json
47	42	yarn.lock

--8b77b75a--2023-09-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--1bc0898a--2023-08-03--Nicolas Carlo
--2cc4c2d1--2023-08-03--Nicolas Carlo
--e27824de--2023-08-03--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--14178462--2023-08-03--Nicolas Carlo
--bc1221ac--2023-08-03--dependabot[bot]
1	1	package.json
715	521	yarn.lock

--de599948--2023-08-03--dependabot[bot]
1	1	package.json
6	6	yarn.lock

--d716ebdd--2023-08-03--Nicolas Carlo
--020dbca2--2023-08-03--Nicolas Carlo
--458e1003--2023-08-03--Nicolas Carlo
--f671e614--2023-08-01--dependabot[bot]
1	1	package.json
5	12	yarn.lock

--1df36791--2023-08-01--dependabot[bot]
2	2	package.json
391	284	yarn.lock

--957e01e3--2023-08-01--dependabot[bot]
1	1	package.json
16	23	yarn.lock

--93e82d46--2023-07-28--Nicolas Carlo
4	0	CHANGELOG.md
14	4	src/refactorings/inline/inline-variable/find-inlinable-code.ts
91	8	src/refactorings/inline/inline-variable/inline-variable.test.ts

--8bbb0bec--2023-07-26--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--ca5e27c9--2023-07-26--Nicolas Carlo
--e615318d--2023-07-26--allcontributors[bot]
9	0	.all-contributorsrc

--c5ffbb73--2023-07-26--allcontributors[bot]
2	1	README.md

--b85a115b--2023-07-26--Nicolas Carlo
15	0	CHANGELOG.md
26	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
24	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--90a0907f--2023-07-24--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--b0366412--2023-07-24--Nicolas Carlo
1	1	package.json

--05f17fee--2023-07-24--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--cbe50662--2023-07-24--Nicolas Carlo
--7e227305--2023-07-24--Nicolas Carlo
--bb2fc66a--2023-07-24--Nicolas Carlo
5	14	src/editor/adapters/vscode-editor.contract.test.ts
9	21	src/test/mocha-test-runner.ts

--e2e04b49--2023-07-24--Nicolas Carlo
3	3	src/action-providers.ts

--fd0eb8b3--2023-07-24--Nicolas Carlo
1	1	src/action-providers.ts

--e8973031--2023-07-24--Nicolas Carlo
--88fe5558--2023-07-24--Nicolas Carlo
--e266bd7a--2023-07-24--Nicolas Carlo
4	0	CHANGELOG.md

--71e17807--2023-07-24--allcontributors[bot]
11	1	.all-contributorsrc

--460ad238--2023-07-24--allcontributors[bot]
4	1	README.md

--b3f2aeca--2023-07-23--Nicolas Carlo
29	0	src/refactorings/flip-if-else/flip-if-else.test.ts
3	3	src/refactorings/flip-if-else/flip-if-else.ts

--39e97814--2023-07-18--dependabot[bot]
3	3	yarn.lock

--edc6fc83--2023-07-17--Nicolas Carlo
--e0384384--2023-07-17--Nicolas Carlo
--d364694e--2023-07-11--dependabot[bot]
9	9	yarn.lock

--92e703d6--2023-07-09--dependabot[bot]
4	18	yarn.lock

--866d5108--2023-07-03--dependabot[bot]
1	1	package.json
128	34	yarn.lock

--fc4ed3f0--2023-07-03--Nicolas Carlo
--706f171c--2023-07-03--Nicolas Carlo
--e325578f--2023-07-03--Nicolas Carlo
--97cbd132--2023-07-03--Nicolas Carlo
--4b4af647--2023-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--8fe045c9--2023-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--6a584383--2023-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--acbed9de--2023-07-01--dependabot[bot]
1	1	package.json
5	4	yarn.lock

--56198434--2023-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--12eb62a5--2023-06-02--Nicolas Carlo
--fbe3833e--2023-06-02--Nicolas Carlo
--98854844--2023-06-02--Nicolas Carlo
--2e15b055--2023-06-02--Nicolas Carlo
--63360dd7--2023-06-01--dependabot[bot]
1	1	package.json
10	10	yarn.lock

--16f89ab0--2023-06-01--dependabot[bot]
1	1	package.json
11	4	yarn.lock

--1d04f020--2023-06-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--b45a66f6--2023-06-01--dependabot[bot]
1	1	package.json
5	10	yarn.lock

--b0399212--2023-05-31--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--76a25aef--2023-05-31--Nicolas Carlo
13	0	CHANGELOG.md
5	2	src/refactorings/lift-up-conditional/lift-up-conditional.test.ts
3	3	src/refactorings/lift-up-conditional/lift-up-conditional.ts

--d9a4eba3--2023-05-31--Nicolas Carlo
17	0	CHANGELOG.md
24	1	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
1	3	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--5c14ee6e--2023-05-19--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--60850192--2023-05-19--Nicolas Carlo
4	0	CHANGELOG.md

--8bd205ad--2023-05-19--Nicolas Carlo
--a7d670e7--2023-05-19--Nicolas Carlo
--db63caea--2023-05-19--allcontributors[bot]
9	0	.all-contributorsrc

--2ed13994--2023-05-19--allcontributors[bot]
2	1	README.md

--451250bd--2023-05-19--Nicolas Carlo
2	2	src/refactorings/change-signature/change-signature.ts

--047672a0--2023-05-19--Nicolas Carlo
2	1	src/refactorings/change-signature/change-signature.test.ts

--34b02897--2023-05-19--Nicolas Carlo
--54ca1c51--2023-05-19--Nicolas Carlo
--1dee86b2--2023-05-19--Nicolas Carlo
--b08aeb68--2023-05-19--dependabot[bot]
1	1	package.json
121	131	yarn.lock

--c115b73a--2023-05-19--dependabot[bot]
1	1	package.json
20	25	yarn.lock

--3a3c2492--2023-05-19--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--21f15b81--2023-05-19--Nicolas Carlo
--8361075b--2023-05-19--Nicolas Carlo
--1ef282a9--2023-05-19--Nicolas Carlo
--f85b9435--2023-05-19--Nicolas Carlo
4	0	CHANGELOG.md

--918ba659--2023-05-19--Nicolas Carlo
14	0	REFACTORINGS.md
-	-	docs/demo/remove-jsx-fragment.gif

--0ba81b9f--2023-05-15--Romain Guerin
14	0	src/refactorings/change-signature/change-signature.test.ts

--7991e8cc--2023-05-01--dependabot[bot]
1	1	package.json
44	117	yarn.lock

--a7fea484--2023-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--33513061--2023-04-26--Nicolas Carlo
--889fc6df--2023-04-25--dependabot[bot]
3	3	yarn.lock

--f151c484--2023-04-21--Nicolas Carlo
4	8	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.ts

--792691ec--2023-04-21--Nicolas Carlo
11	11	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--d24e4594--2023-04-21--Nicolas Carlo
15	0	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts
32	5	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.ts

--d3caec08--2023-04-21--Nicolas Carlo
12	3	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts

--4563f21d--2023-04-21--Nicolas Carlo
2	2	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts

--86f53fbe--2023-04-21--Nicolas Carlo
6	2	src/extension.ts
9	4	src/refactorings/remove-jsx-fragment/index.ts

--4de63bc8--2023-04-21--Nicolas Carlo
18	12	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts

--a249776b--2023-04-15--Timon Jurschitsch
18	0	package.json
4	0	src/editor/error-reason.ts
2	1	src/extension.ts
12	0	src/refactorings/remove-jsx-fragment/index.ts
47	0	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts
44	0	src/refactorings/remove-jsx-fragment/remove-jsx-fragment.ts

--773b9d7e--2023-04-13--Nicolas Carlo
--6e605ad5--2023-04-13--Nicolas Carlo
--ad3f8cac--2023-04-14--dependabot[bot]
1	1	package.json
46	31	yarn.lock

--6af7a0e0--2023-04-14--dependabot[bot]
1	1	package.json
11	69	yarn.lock

--9b6b989f--2023-04-13--Nicolas Carlo
--941bf83f--2023-04-13--Nicolas Carlo
--efa60b5f--2023-04-13--Nicolas Carlo
--56c77178--2023-04-14--dependabot[bot]
1	1	package.json
43	33	yarn.lock

--506b3dd4--2023-04-14--dependabot[bot]
1	1	package.json
44	61	yarn.lock

--e9b21f57--2023-04-13--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--6994ce5e--2023-04-13--Nicolas Carlo
0	1	package.json
0	396	yarn.lock

--715d966d--2023-04-13--Nicolas Carlo
14	1	CHANGELOG.md
1	1	package.json

--3923dae8--2023-04-14--dependabot[bot]
1	1	package.json
4	90	yarn.lock

--a345934d--2023-04-13--Nicolas Carlo
--d7caec07--2023-04-13--Nicolas Carlo
2	3	.github/workflows/ovsx-deploy.yml
2	3	.github/workflows/vscode-deploy.yml

--77fdb43a--2023-04-13--Nicolas Carlo
2	0	docs/adr/0008-don-t-propose-quick-fix-for-react-convert-to-pure-component.md
56	0	docs/adr/0013-remove-extract-class-and-convert-to-pure-component-refactorings.md
-	-	docs/adr/13-bundle-after.png
-	-	docs/adr/13-bundle-before-size.png
-	-	docs/adr/13-bundle-before.png

--3b50ed9a--2023-04-13--Nicolas Carlo
1	0	package.json
13	0	yarn.lock

--fa6a75e8--2023-04-13--Nicolas Carlo
40	49	.github/dependabot.yml

--91030150--2023-04-13--Nicolas Carlo
2	4	package.json
5	1	webpack.config.js
506	1451	yarn.lock

--f3a780ef--2023-04-13--Nicolas Carlo
0	15	REFACTORINGS.md
-	-	docs/demo/convert-to-pure-component.gif
0	21	package.json
1	2	src/extension.ts
0	123	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
0	62	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
0	12	src/refactorings/react/convert-to-pure-component/index.ts

--2b30caa2--2023-04-13--Nicolas Carlo
0	17	REFACTORINGS.md
-	-	docs/demo/extract-class.gif
0	34	package.json
0	19	src/extension.ts
0	2	src/refactorings/extract-class/EXTRACT_CLASS_COMMAND.ts
0	3	src/refactorings/extract-class/class-name-matcher.ts
0	17	src/refactorings/extract-class/class-node.ts
0	447	src/refactorings/extract-class/class-refactor.test.ts
0	143	src/refactorings/extract-class/class-refactor.ts
0	69	src/refactorings/extract-class/extract-class-action-provider.ts
0	155	src/refactorings/extract-class/extract-class-command.ts
0	31	src/refactorings/extract-class/extract-class.test.ts
0	34	src/refactorings/extract-class/extract-class.ts
0	8	src/refactorings/extract-class/format-ts.ts
0	17	src/refactorings/extract-class/index.ts
0	6	src/refactorings/extract-class/parse-class-declaration.ts
0	9	src/refactorings/extract-class/text-matcher.test.ts
0	19	src/refactorings/extract-class/text-matcher.ts
0	16	src/refactorings/extract-class/typescript-class-node.test.ts
0	161	src/refactorings/extract-class/typescript-class-node.ts
0	96	src/refactorings/extract-class/uml-notation.test.ts
0	98	src/refactorings/extract-class/uml-notation.ts

--936393f9--2023-04-13--Nicolas Carlo
1	1	package.json
18	13	yarn.lock

--7d8ed220--2023-04-13--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml
1	1	package.json
13	18	yarn.lock

--58deefe5--2023-04-13--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml
0	2	webpack.config.js

--00f1cc34--2023-04-13--Nicolas Carlo
1	1	package.json
4	2	webpack.config.js

--98e6a6dc--2023-04-13--Nicolas Carlo
1	1	.github/workflows/vscode-deploy.yml
1	1	package.json

--b68765c2--2023-04-13--Nicolas Carlo
3	2	.github/workflows/ovsx-deploy.yml
4	3	.github/workflows/vscode-deploy.yml

--1f9fcf15--2023-04-13--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--3fcc1253--2023-04-13--Nicolas Carlo
1	1	package.json
51	32	yarn.lock

--a3216c85--2023-04-13--Nicolas Carlo
--eff06be1--2023-04-13--allcontributors[bot]
9	0	.all-contributorsrc

--7a0a152d--2023-04-13--allcontributors[bot]
2	1	README.md

--562fc711--2023-04-13--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--e4ba9911--2023-04-13--Nicolas Carlo
12	0	REFACTORINGS.md
-	-	docs/demo/wrap-in-jsx-fragment.gif

--a618ab14--2023-04-13--Nicolas Carlo
4	0	CHANGELOG.md

--5032acb0--2023-04-13--Nicolas Carlo
7	2	package.json

--dc7b4c0b--2023-04-13--Nicolas Carlo
--2fce5a4a--2023-04-13--allcontributors[bot]
9	0	.all-contributorsrc

--76b990f3--2023-04-13--allcontributors[bot]
2	1	README.md

--ab0dca01--2023-04-13--Nicolas Carlo
--20d8b947--2023-04-13--Nicolas Carlo
9	7	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts

--1df9b727--2023-04-13--Timon Jurschitsch
11	1	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts

--dfc4c680--2023-04-11--Nicolas Carlo
20	0	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts
31	37	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--6761ad24--2023-04-11--Nicolas Carlo
0	1	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--30a920f7--2023-04-11--Nicolas Carlo
8	4	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts

--9856510c--2023-04-11--Nicolas Carlo
0	2	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--4c7f40fc--2023-04-11--Nicolas Carlo
2	2	src/extension.ts
9	4	src/refactorings/wrap-in-jsx-fragment/index.ts

--a3bffba7--2023-04-11--Nicolas Carlo
1	1	src/editor/error-reason.ts

--fe0c403f--2023-04-05--Nicolas Carlo
--6bb32a9f--2023-04-05--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3459d3a4--2023-04-04--Nicolas Carlo
--ba0f708d--2023-04-04--Nicolas Carlo
--406a1dcf--2023-04-04--Nicolas Carlo
--bc31937b--2023-04-04--Nicolas Carlo
--b91e6ae5--2023-04-04--Nicolas Carlo
--a1914d36--2023-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--7326c828--2023-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--4b208eb4--2023-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--1fd12687--2023-04-01--dependabot[bot]
1	1	package.json
50	32	yarn.lock

--8e7f8929--2023-04-01--dependabot[bot]
1	1	package.json
28	16	yarn.lock

--b878d284--2023-04-01--Timon Jurschitsch
37	8	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--327762f6--2023-04-01--Nicolas Carlo
1	0	CHANGELOG.md
12	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
45	6	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--6cedb4a4--2023-03-24--Timon Jurschitsch
13	0	package.json
4	0	src/editor/error-reason.ts
4	3	src/extension.ts
11	0	src/refactorings/wrap-in-jsx-fragment/index.ts
33	0	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts
46	0	src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts

--50fc9696--2023-03-24--Nicolas Carlo
4	0	CHANGELOG.md

--d11d1627--2023-03-24--Nicolas Carlo
--3f29015f--2023-03-24--Nicolas Carlo
--90c7654d--2023-03-24--allcontributors[bot]
9	0	.all-contributorsrc

--bf7a7dcd--2023-03-24--allcontributors[bot]
2	1	README.md

--dfc20434--2023-03-24--Nicolas Carlo
2	2	src/editor/editor-contract-test.ts

--5e3e2e3f--2023-03-23--Nicolas Carlo
2	2	src/editor/adapters/in-memory-editor.ts
1	8	src/editor/adapters/vscode-editor.ts
70	11	src/refactorings/change-signature/change-signature.ts

--22532f57--2023-03-17--Nicolas Carlo
--df0aee52--2023-03-17--Nicolas Carlo
--0def578c--2023-03-17--Samuel Bronson
17	0	src/refactorings/change-signature/change-signature.test.ts

--c75bfb82--2023-03-15--dependabot[bot]
1	1	package.json
6	42	yarn.lock

--58ebf389--2023-03-06--Nicolas Carlo
--e7ac34d6--2023-03-06--allcontributors[bot]
2	1	.all-contributorsrc

--5bb7a3dc--2023-03-06--allcontributors[bot]
1	1	README.md

--fada6f16--2023-03-06--Nicolas Carlo
--8109c151--2023-03-06--Nicolas Carlo
--96d7088d--2023-03-06--Nicolas Carlo
--e0eedfd3--2023-03-06--Nicolas Carlo
--9b75c963--2023-03-06--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e09aacae--2023-03-06--dependabot[bot]
2	2	package.json
440	327	yarn.lock

--f717e9e1--2023-03-06--Nicolas Carlo
--9fe9a005--2023-03-06--Nicolas Carlo
--735d9421--2023-03-06--Nicolas Carlo
--8a841220--2023-03-06--changfeng
6	1	package.json

--55baa51f--2023-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b3f92652--2023-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--237207fa--2023-03-01--dependabot[bot]
1	1	package.json
55	54	yarn.lock

--a0befd44--2023-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--10ac0535--2023-03-01--dependabot[bot]
1	1	package.json
27	20	yarn.lock

--142f38d8--2023-02-15--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--d703bf29--2023-02-15--Nicolas Carlo
--a28d9933--2023-02-16--allcontributors[bot]
9	0	.all-contributorsrc

--559158ed--2023-02-16--allcontributors[bot]
2	1	README.md

--cb0a8642--2023-02-15--Nicolas Carlo
4	0	CHANGELOG.md
9	9	README.md
6	3	package.json

--93d640ce--2023-02-09--Nicolas Carlo
4	0	CHANGELOG.md
2	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
14	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--f8499d29--2023-02-06--Nicolas Carlo
--802bf721--2023-02-06--allcontributors[bot]
9	0	.all-contributorsrc

--39a18d24--2023-02-06--allcontributors[bot]
52	51	README.md

--6d0f394a--2023-02-06--Nicolas Carlo
--ad1b63a9--2023-02-06--Jeroen van Warmerdam
1	1	README.md

--00b77806--2023-02-06--Jeroen van Warmerdam
1	1	README.md

--6c20f557--2023-02-03--Nicolas Carlo
2	2	.github/workflows/ovsx-deploy.yml
2	2	.github/workflows/vscode-deploy.yml

--6b9cd5b4--2023-02-03--Nicolas Carlo
--e5b8d69b--2023-02-03--Nicolas Carlo
1	1	CHANGELOG.md

--8f6acf0b--2023-02-03--Nicolas Carlo
1	0	.gitignore

--5ed0ab83--2023-02-03--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--e3c4b3e5--2023-02-03--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--41505749--2023-02-02--Nicolas Carlo
1	0	CHANGELOG.md
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-jsx.test.ts
1	0	src/refactorings/extract/extract-variable/extract-variable.ts

--80588a5a--2023-02-02--Nicolas Carlo
1	0	CHANGELOG.md
12	2	src/refactorings/inline/inline-variable/find-inlinable-code.ts
10	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--921ab1f8--2023-01-31--Nicolas Carlo
13	0	README.md
10	0	package.json
4	0	src/editor/adapters/attempting-editor.ts
4	0	src/editor/adapters/in-memory-editor.ts
4	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts
2	1	src/extension.ts
11	0	src/highlights/refresh-highlights/index.ts
20	0	src/highlights/refresh-highlights/refresh-highlights.test.ts
33	0	src/highlights/refresh-highlights/refresh-highlights.ts
0	24	src/highlights/toggle-highlight/toggle-highlight.test.ts

--1f53f15c--2023-01-31--Nicolas Carlo
15	20	src/editor/source-change.ts
13	0	src/highlights/toggle-highlight/toggle-highlight.test.ts

--af9af44a--2023-01-27--Nicolas Carlo
68	1	src/ast/scope.test.ts
19	3	src/ast/scope.ts
29	1	src/highlights/toggle-highlight/toggle-highlight.test.ts

--122d5094--2023-01-27--Nicolas Carlo
1	1	.eslintrc.js

--514a8d8b--2023-01-27--Nicolas Carlo
11	0	src/ast/debug.ts

--84886695--2023-02-02--Nicolas Carlo
--791da4d8--2023-02-02--dependabot[bot]
1	1	package.json
42	8	yarn.lock

--b1a8c1a2--2023-02-02--Nicolas Carlo
--7c4ee51c--2023-02-02--Nicolas Carlo
--8d976c13--2023-02-02--dependabot[bot]
1	1	package.json
32	32	yarn.lock

--e798efef--2023-02-02--dependabot[bot]
1	1	package.json
32	24	yarn.lock

--086e68c3--2023-02-02--Nicolas Carlo
--cb709c93--2023-02-02--Nicolas Carlo
--970e5251--2023-02-02--Nicolas Carlo
--fcc6b8c5--2023-02-02--Nicolas Carlo
--74a40cee--2023-02-02--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--4eb745c5--2023-02-02--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--9e320397--2023-02-02--dependabot[bot]
1	1	package.json
77	60	yarn.lock

--461d5cd2--2023-02-02--dependabot[bot]
1	1	package.json
6	16	yarn.lock

--80d1f5d2--2023-02-02--Nicolas Carlo
--cb5bca4d--2023-02-02--dependabot[bot]
1	1	package.json
35	21	yarn.lock

--4b70641b--2023-01-26--Nicolas Carlo
--d57726ad--2023-01-26--Nicolas Carlo
7	0	CHANGELOG.md

--0e6a522f--2023-01-26--Nicolas Carlo
33	0	README.md
-	-	docs/demo/toggle-highlights.gif

--1530aced--2023-01-25--Nicolas Carlo
11	13	src/playground/playground.ts

--7ad6c2eb--2023-01-24--Nicolas Carlo
--d8412e5f--2023-01-24--Nicolas Carlo
3	4	src/refactorings/extract-class/extract-class-command.ts

--89102edc--2023-01-24--Nicolas Carlo
3	4	src/refactorings/extract-class/extract-class-command.ts

--cd561d10--2023-01-24--Nicolas Carlo
2	0	src/editor/adapters/vscode-editor.ts
24	8	src/editor/colors.ts

--b40ae95f--2023-01-24--Nicolas Carlo
6	6	src/editor/source-change.ts
13	0	src/highlights/toggle-highlight/toggle-highlight.test.ts

--8e25b30a--2023-01-24--Nicolas Carlo
4	1	src/ast/scope.ts
20	0	src/highlights/toggle-highlight/toggle-highlight.test.ts

--e251a09b--2023-01-24--Nicolas Carlo
0	2	src/editor/adapters/in-memory-editor.test.ts
0	2	src/highlights/toggle-highlight/toggle-highlight.test.ts

--08e2c76c--2023-01-24--Nicolas Carlo
28	0	src/editor/adapters/in-memory-editor.test.ts
22	11	src/editor/adapters/in-memory-editor.ts
21	11	src/editor/adapters/vscode-editor.ts
16	0	src/editor/selection.ts
15	18	src/editor/source-change.ts
40	0	src/highlights/toggle-highlight/toggle-highlight.test.ts

--7fbef360--2023-01-24--Nicolas Carlo
1	1	src/editor/selection.ts
3	3	src/refactorings/extract/extract-variable/occurrence.ts

--040e189f--2023-01-18--Nicolas Carlo
8	0	CHANGELOG.md
2	2	package.json
9	0	src/ast/transformation.test.ts
22	22	src/refactorings/extract-interface/extract-interface.test.ts
6	6	src/refactorings/extract/extract-type/extract-type.test.ts
14	13	yarn.lock

--7f946609--2023-01-18--Nicolas Carlo
16	21	src/refactorings/extract-class/class-refactor.test.ts
16	4	src/refactorings/extract-class/class-refactor.ts
1	1	src/refactorings/extract-class/extract-class-command.ts

--91b027ca--2023-01-11--Nicolas Carlo
--70f91c31--2023-01-11--Nicolas Carlo
1	1	jest.config.js
2	1	package.json
244	10	yarn.lock

--9b997ba0--2023-01-05--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--cabaf780--2023-01-05--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml

--098f1bc7--2023-01-05--dependabot[bot]
2	2	package.json
410	582	yarn.lock

--5f9b28e1--2023-01-05--Nicolas Carlo
--15fb9311--2023-01-05--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--b044b47b--2023-01-05--Nicolas Carlo
26	27	src/editor/adapters/vscode-editor.ts

--88382e29--2023-01-05--Nicolas Carlo
12	80	src/editor/adapters/vscode-editor.ts
5	0	src/extension.ts

--fcc36bcb--2023-01-05--Nicolas Carlo
--b903d363--2023-01-04--dependabot[bot]
1	1	package.json
212	6	yarn.lock

--8f4f2b07--2023-01-04--Nicolas Carlo
--f05450f2--2023-01-04--Nicolas Carlo
--2c74bd12--2023-01-04--dependabot[bot]
1	1	package.json
151	132	yarn.lock

--c87b7594--2023-01-04--dependabot[bot]
1	1	package.json
33	39	yarn.lock

--9d73d899--2023-01-04--Nicolas Carlo
--36d96748--2023-01-04--Nicolas Carlo
--f8689492--2023-01-04--dependabot[bot]
6	6	yarn.lock

--9f34925f--2023-01-04--dependabot[bot]
1	1	package.json
8	101	yarn.lock

--3528d4a7--2023-01-04--Nicolas Carlo
2	2	tsconfig.json

--111c6ea5--2023-01-03--Nicolas Carlo
--938abf9c--2023-01-03--allcontributors[bot]
9	0	.all-contributorsrc

--93ad0949--2023-01-03--allcontributors[bot]
4	1	README.md

--bd049128--2023-01-03--Nicolas Carlo
4	0	CHANGELOG.md
3	3	package.json
9	1	src/refactorings/inline/inline-variable/inline-variable.test.ts
47	10	yarn.lock

--93f4d372--2023-01-02--Nicolas Carlo
--755f83d3--2023-01-02--Nicolas Carlo
--04f065bc--2023-01-02--Nicolas Carlo
--00afb083--2023-01-01--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--51fd47cd--2023-01-01--dependabot[bot]
1	1	package.json
5	10	yarn.lock

--d44c3268--2023-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--506da83f--2022-12-30--Nicolas Carlo
1	1	package.json
3	223	yarn.lock

--fbcf5676--2022-12-30--Nicolas Carlo
2	1	package.json
223	3	yarn.lock

--1cfac73b--2022-12-30--Nicolas Carlo
4	0	CHANGELOG.md
1	44	package.json

--fa1a3821--2022-12-30--Nicolas Carlo
2	1	.vscode/settings.json

--b9709a1c--2022-12-30--Nicolas Carlo
2	0	src/ast/scope.ts
0	1	src/refactorings/merge-if-statements/merge-if-statements.ts

--d61284c9--2022-12-30--Nicolas Carlo
1	1	.travis.yml

--cde09e11--2022-12-30--Nicolas Carlo
2	2	.travis.yml

--47d16ebf--2022-12-30--Nicolas Carlo
1	1	package.json
4	4	yarn.lock

--316e7dfd--2022-12-30--Nicolas Carlo
1	1	.travis.yml
4	4	package.json
10	5	yarn.lock

--2767748b--2022-12-28--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--5e945c92--2022-12-28--Nicolas Carlo
5	1	CHANGELOG.md
1	1	REFACTORINGS.md

--6bd05a59--2022-12-28--Nicolas Carlo
--a80c9ee5--2022-12-23--Nicolas Carlo
--590cac99--2022-12-22--Jose Cabrera
14	18	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--ee54e6a4--2022-12-22--Jose Cabrera
18	12	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--39444148--2022-12-22--Jose Cabrera
1	1	src/refactorings/change-signature/change-signature.ts

--ebcb1cdc--2022-12-22--Jose Cabrera
1	1	src/refactorings/change-signature/change-signature.modules.test.ts

--db002913--2022-12-22--Jose Cabrera
1	1	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--0f400386--2022-12-10--Jose Cabrera
1	1	src/refactorings/change-signature/change-signature.modules.test.ts
32	1	src/refactorings/change-signature/change-signature.test.ts
8	6	src/refactorings/change-signature/change-signature.ts

--e34ad93d--2022-12-09--Jose Cabrera
1	2	.vscode/settings.json

--b1618e22--2022-12-09--Jose Cabrera
11	3	src/refactorings/change-signature/change-signature.ts

--b178bbff--2022-12-09--Jose Cabrera
634	0	src/refactorings/change-signature/change-signature.modules.test.ts
0	225	src/refactorings/change-signature/change-signature.test.ts

--b7fd0899--2022-12-09--Jose Cabrera
14	3	src/editor/adapters/change-signature-webview/change-signature.html
2	2	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
5	4	src/refactorings/change-signature/change-signature.test.ts

--6b1e28c6--2022-12-09--Jose Cabrera
37	0	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--7a52eeaa--2022-12-09--Jose Cabrera
49	0	src/refactorings/change-signature/change-signature.test.ts
2	4	src/refactorings/change-signature/change-signature.ts

--556054c2--2022-12-09--Jose Cabrera
110	0	src/refactorings/change-signature/change-signature.test.ts
6	0	src/refactorings/change-signature/change-signature.ts

--c62cf7f9--2022-12-09--Jose Cabrera
21	6	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--ff0e6ee2--2022-12-09--Jose Cabrera
25	5	src/editor/adapters/change-signature-webview/change-signature.html
4	1	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--6ce3c48b--2022-12-09--Jose Cabrera
5	0	src/editor/adapters/change-signature-webview/change-signature.html
27	0	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--81bd2317--2022-12-09--Jose Cabrera
14	17	src/editor/adapters/change-signature-webview/change-signature.html

--e1990d91--2022-12-09--Jose Cabrera
16	15	src/editor/adapters/change-signature-webview/change-signature.html

--e865997d--2022-12-09--Jose Cabrera
51	16	src/editor/adapters/change-signature-webview/change-signature.html
43	2	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--5ee840e4--2022-12-08--Jose Cabrera
2	1	.vscode/settings.json
3	1	src/editor/adapters/vscode-editor.ts

--c72836c0--2022-12-08--Jose Cabrera
6	5	src/editor/adapters/change-signature-webview/change-signature.html

--467dda0d--2022-12-08--Jose Cabrera
4	30	src/refactorings/change-signature/change-signature.test.ts
11	21	src/refactorings/change-signature/change-signature.ts

--52093267--2022-12-08--Jose Cabrera
1	1	src/editor/editor.ts
52	13	src/refactorings/change-signature/change-signature.test.ts
24	1	src/refactorings/change-signature/change-signature.ts

--f1d24541--2022-12-08--Jose Cabrera
1	0	src/editor/editor.ts
150	5	src/refactorings/change-signature/change-signature.test.ts
11	6	src/refactorings/change-signature/change-signature.ts

--d7ea477d--2022-12-08--Jose Cabrera
7	1	src/editor/adapters/change-signature-webview/change-signature.html

--6d4eac6f--2022-12-08--Jose Cabrera
18	7	src/editor/adapters/change-signature-webview/change-signature.html
30	3	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts

--f345cf55--2022-12-08--Jose Cabrera
15	10	src/editor/adapters/change-signature-webview/change-signature.html

--b7fa942c--2022-12-08--Jose Cabrera
49	13	src/editor/adapters/change-signature-webview/change-signature.html
61	12	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
2	1	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts

--1623abe4--2022-12-07--Jose Cabrera
37	20	src/editor/adapters/change-signature-webview/change-signature.html
1	1	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts

--e4f51b29--2022-12-07--Jose Cabrera
64	11	src/editor/adapters/change-signature-webview/change-signature.html
54	24	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
3	7	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts

--ca0cef05--2022-12-21--Nicolas Carlo
--06c288a7--2022-12-09--Nicolas Carlo
4	0	CHANGELOG.md

--25cd6fde--2022-12-09--Nicolas Carlo
--ab537b5e--2022-12-09--Nicolas Carlo
--3e9a7b82--2022-12-07--Jose Cabrera
1	1	src/editor/adapters/change-signature-webview/change-signature.html
20	10	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
4	2	src/editor/adapters/vscode-editor.ts

--67fd6807--2022-12-07--Jose Cabrera
3	1	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts
4	1	webpack.config.js

--f826b3fa--2022-12-07--Jose Cabrera
126	0	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts
24	0	src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts
0	126	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts
0	24	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.ts
3	2	src/editor/adapters/vscode-editor.ts

--8d7e1091--2022-12-07--Jose Cabrera
5	1	jest.config.js
1	0	package.json
127	7	yarn.lock

--35935c48--2022-12-07--Jose Cabrera
1	8	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts
5	2	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.ts
1	5	src/editor/adapters/vscode-editor.ts

--276534b1--2022-12-07--Jose Cabrera
11	0	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--d5b501d0--2022-12-07--Jose Cabrera
16	5	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--74c9d2ea--2022-12-07--Jose Cabrera
4	0	src/editor/adapters/change-signature-webview/html.d.ts
0	4	src/editor/adapters/html.d.ts

--6bcd85df--2022-12-07--Jose Cabrera
3	9	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--2115db6c--2022-12-07--Jose Cabrera
137	0	src/editor/adapters/change-signature-webview/change-signature.html
0	137	src/editor/adapters/change-signature-webview/change-signature.webview.html
1	1	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts
1	1	src/editor/adapters/vscode-editor.ts

--9fd66652--2022-12-07--Jose Cabrera
23	39	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--b0189173--2022-12-07--Jose Cabrera
24	23	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--8ec8efcd--2022-12-07--Jose Cabrera
132	0	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts

--11b2655a--2022-12-07--Jose Cabrera
137	0	src/editor/adapters/change-signature-webview/change-signature.webview.html
21	0	src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.ts
0	137	src/editor/adapters/change-signature.webview.html
1	20	src/editor/adapters/vscode-editor.ts

--11668825--2022-12-07--Jose Cabrera
1	0	package.json
14	0	yarn.lock

--3181b07e--2022-12-06--Jose Cabrera
9	8	src/editor/adapters/vscode-editor.ts

--53adfa6b--2022-12-06--Jose Cabrera
137	0	src/editor/adapters/change-signature.webview.html
7	145	src/editor/adapters/vscode-editor.ts

--68ce05fa--2022-12-06--Jose Cabrera
4	0	src/editor/adapters/html.d.ts

--4646fd59--2022-12-06--Jose Cabrera
4	0	webpack.config.js

--99fe86ff--2022-12-06--Jose Cabrera
1	0	package.json
61	1	yarn.lock

--0994c557--2022-12-06--Jose Cabrera
42	1	src/refactorings/change-signature/change-signature.test.ts

--7942acdb--2022-12-06--dependabot[bot]
18	4	yarn.lock

--61ac6e72--2022-12-04--Jose Cabrera
13	0	src/refactorings/change-signature/change-signature.ts

--303ec414--2022-12-03--Nicolas Carlo
--9dbbfdc9--2022-12-03--Nicolas Carlo
--18f90ef5--2022-12-03--dependabot[bot]
1	1	package.json
36	25	yarn.lock

--327c1de1--2022-12-03--dependabot[bot]
1	1	package.json
21	55	yarn.lock

--f36a0ad3--2022-12-03--Nicolas Carlo
--33848c58--2022-12-03--Nicolas Carlo
--0eccb373--2022-12-03--Nicolas Carlo
--a2402dfb--2022-12-03--Nicolas Carlo
--96c3270f--2022-12-03--Nicolas Carlo
--51f699b5--2022-12-03--dependabot[bot]
3	3	yarn.lock

--36b8c853--2022-12-01--dependabot[bot]
1	1	package.json
32	6	yarn.lock

--7a1f0b8f--2022-12-01--dependabot[bot]
1	1	package.json
41	41	yarn.lock

--1d034f29--2022-12-01--dependabot[bot]
1	1	package.json
43	50	yarn.lock

--c72bc21b--2022-12-01--dependabot[bot]
2	2	package.json
13	13	yarn.lock

--ea9c0dc9--2022-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--dbaa29de--2022-11-30--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts
10	23	src/editor/source-change.ts
5	1	src/highlights/highlights-repository.ts
32	11	src/highlights/toggle-highlight/toggle-highlight.test.ts

--d08843da--2022-11-24--Nicolas Carlo
4	14	src/editor/adapters/in-memory-editor.ts
9	1	src/editor/position.ts
40	34	src/editor/source-change.ts
39	13	src/highlights/toggle-highlight/toggle-highlight.test.ts

--b288e209--2022-11-24--Nicolas Carlo
13	13	src/highlights/toggle-highlight/toggle-highlight.test.ts

--001cf217--2022-11-24--Nicolas Carlo
7	1	src/editor/adapters/in-memory-editor.ts
17	0	src/editor/source-change.ts
15	2	src/highlights/toggle-highlight/toggle-highlight.test.ts

--ddf5fbd9--2022-11-24--Nicolas Carlo
31	0	src/editor/adapters/in-memory-editor.ts
20	3	src/highlights/toggle-highlight/toggle-highlight.test.ts

--ab9d182b--2022-11-24--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts

--f403cb56--2022-11-24--Nicolas Carlo
--d3a3d25f--2022-11-24--Nicolas Carlo
44	4	src/editor/adapters/vscode-editor.ts
4	0	src/extension.ts

--e34ec168--2022-11-24--Nicolas Carlo
6	0	src/editor/adapters/in-memory-editor.ts
36	25	src/editor/source-change.ts
17	0	src/highlights/highlights-repository.ts
13	1	src/highlights/toggle-highlight/toggle-highlight.test.ts

--e8cc7d05--2022-11-24--Nicolas Carlo
11	24	src/editor/adapters/in-memory-editor.ts
6	0	src/highlights/highlights-repository.ts
11	0	src/highlights/highlights.ts

--197c5259--2022-11-24--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts
1	1	src/highlights/toggle-highlight/toggle-highlight.test.ts

--f7a1ca7d--2022-11-17--Nicolas Carlo
20	1	src/editor/adapters/in-memory-editor.ts
11	9	src/highlights/toggle-highlight/toggle-highlight.test.ts

--720ffab1--2022-11-18--Nicolas Carlo
--dc10f23b--2022-11-18--Nicolas Carlo
4	0	CHANGELOG.md

--be80e19d--2022-11-18--dependabot[bot]
2	2	package.json
7	12	yarn.lock

--cc6c6e55--2022-11-18--Nicolas Carlo
--a58273dd--2022-11-18--Nicolas Carlo
--933cbd8c--2022-11-18--allcontributors[bot]
9	0	.all-contributorsrc

--e2bb9f06--2022-11-18--allcontributors[bot]
2	1	README.md

--dd0cca75--2022-11-18--Nicolas Carlo
3	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts
3	3	src/refactorings/extract/extract-type/extract-type.ts
3	3	src/refactorings/extract/extract-variable/occurrence.ts

--fe9c9266--2022-11-18--Nicolas Carlo
22	22	src/refactorings/extract-interface/extract-interface.test.ts
4	4	src/refactorings/extract/extract-type/extract-type.test.ts
1	1	src/refactorings/flip-operator/flip-operator.test.ts
5	3	src/refactorings/move-statement-up/move-statement-up.ts
1	1	src/refactorings/react/extract-use-callback/extract-use-callback.test.ts
1	1	src/refactorings/split-if-statement/split-if-statement.test.ts
4	4	src/refactorings/toggle-braces/toggle-braces.test.ts

--c649da93--2022-11-18--Nicolas Carlo
5	5	package.json
95	28	yarn.lock

--509afd9b--2022-11-18--Nicolas Carlo
1	1	src/refactorings/flip-if-else/flip-if-else.ts

--778a47c6--2022-11-18--Nicolas Carlo
14	1	src/refactorings/flip-if-else/flip-if-else.test.ts

--5b2d9786--2022-11-16--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts
1	1	src/extension.ts

--dc8d6cd9--2022-11-16--Nicolas Carlo
2	0	src/playground/playground.ts

--d86c55d5--2022-11-16--Nicolas Carlo
0	1	src/editor/adapters/in-memory-editor.ts

--130e7620--2022-11-15--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml

--7f2882f6--2022-11-15--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--6b391f07--2022-11-15--Nicolas Carlo
12	0	REFACTORINGS.md
-	-	docs/demo/flip-operator.gif

--9e7c1ad5--2022-11-15--Nicolas Carlo
1	2	CHANGELOG.md

--3c16a4d4--2022-11-15--Nicolas Carlo
20	0	src/refactorings/flip-operator/flip-operator.test.ts
43	1	src/refactorings/flip-operator/flip-operator.ts

--3f481742--2022-11-15--Nicolas Carlo
1	1	CHANGELOG.md
10	10	package.json
3	3	src/editor/error-reason.ts
7	7	src/extension.ts
71	0	src/refactorings/flip-operator/flip-operator.test.ts
52	0	src/refactorings/flip-operator/flip-operator.ts
17	0	src/refactorings/flip-operator/index.ts
0	71	src/refactorings/flip-yoda-condition/flip-yoda-condition.test.ts
0	52	src/refactorings/flip-yoda-condition/flip-yoda-condition.ts
0	17	src/refactorings/flip-yoda-condition/index.ts

--dbc07406--2022-11-15--Nicolas Carlo
--f52f1883--2022-11-15--Nicolas Carlo
--7bb4a254--2022-11-15--allcontributors[bot]
2	1	.all-contributorsrc

--cb324f15--2022-11-15--allcontributors[bot]
1	1	README.md

--dd8c8c88--2022-11-15--allcontributors[bot]
9	0	.all-contributorsrc

--86b73916--2022-11-15--allcontributors[bot]
2	1	README.md

--1c3e983c--2022-11-15--Nicolas Carlo
--f4ca01a8--2022-11-11--j4k0xb
36	6	src/refactorings/flip-yoda-condition/flip-yoda-condition.test.ts
0	5	src/refactorings/flip-yoda-condition/flip-yoda-condition.ts

--53314b13--2022-11-07--Nicolas Carlo
--a0a176e5--2022-10-07--Nicolas Carlo
52	0	src/editor/adapters/vscode-editor.ts
46	0	src/editor/source-change.ts
17	0	src/highlights/toggle-highlight/toggle-highlight.test.ts
17	17	src/playground/playground.ts

--12d3d6c3--2022-11-10--j4k0xb
2	0	CHANGELOG.md
35	0	package.json
4	0	src/editor/error-reason.ts
2	0	src/extension.ts
41	0	src/refactorings/flip-yoda-condition/flip-yoda-condition.test.ts
57	0	src/refactorings/flip-yoda-condition/flip-yoda-condition.ts
17	0	src/refactorings/flip-yoda-condition/index.ts

--e215e0fa--2022-11-09--Nicolas Carlo
4	0	CHANGELOG.md
19	1	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
18	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts
12	0	src/refactorings/toggle-braces/toggle-braces.test.ts
12	0	src/refactorings/toggle-braces/toggle-braces.ts

--6bd58f62--2022-11-09--Nicolas Carlo
1	1	CHANGELOG.md
2	1	REFACTORINGS.md

--a0dce91f--2022-11-09--Nicolas Carlo
3	3	src/refactorings/change-signature/change-signature.ts

--e4eec037--2022-11-09--Nicolas Carlo
--43c9a754--2022-11-08--Nicolas Carlo
--95786c1b--2022-11-05--Jose Cabrera
59	0	src/refactorings/change-signature/change-signature.test.ts

--d8a54171--2022-11-05--Jose Cabrera
4	1	src/playground/maths/calculator.ts
6	0	src/playground/maths/maths.ts

--60d62600--2022-11-05--Jose Cabrera
51	0	src/refactorings/change-signature/change-signature.test.ts
15	2	src/refactorings/change-signature/change-signature.ts

--d3a78c10--2022-11-07--Jose Cabrera
1	8	src/refactorings/change-signature/change-signature.ts

--3579f40d--2022-11-07--Jose Cabrera
0	9	src/refactorings/change-signature/change-signature.test.ts

--e19000b5--2022-11-07--Jose Cabrera
5	5	src/refactorings/change-signature/change-signature.test.ts

--1563290b--2022-11-05--Jose Cabrera
2	1	src/playground/maths/calculator.ts
5	0	src/playground/maths/maths.ts

--cd4b94d9--2022-11-05--Jose Cabrera
51	0	src/refactorings/change-signature/change-signature.test.ts

--630422b4--2022-11-05--Jose Cabrera
71	1	src/refactorings/change-signature/change-signature.test.ts
42	9	src/refactorings/change-signature/change-signature.ts

--8bd1c0d1--2022-11-07--Nicolas Carlo
11	9	src/editor/editor-contract-test.ts

--1f8e597c--2022-11-07--Nicolas Carlo
--5686d4b1--2022-11-02--Jose Cabrera
14	9	src/editor/adapters/in-memory-editor.ts
24	1	src/editor/editor-contract-test.ts

--5d1ea821--2022-11-02--Nicolas Carlo
4	0	CHANGELOG.md
13	0	REFACTORINGS.md
-	-	docs/demo/change-signature.gif

--7b62f111--2022-11-02--Nicolas Carlo
2	2	src/editor/error-reason.ts
2	4	src/refactorings/change-signature/change-signature.test.ts
35	17	src/refactorings/change-signature/change-signature.ts

--9c247272--2022-11-02--Nicolas Carlo
35	0	package.json

--ff9c1696--2022-11-02--Nicolas Carlo
0	3	src/refactorings/change-signature/change-signature.test.ts
0	1	src/refactorings/change-signature/change-signature.ts

--80f30f31--2022-11-01--Nicolas Carlo
--75cc7099--2022-11-01--Nicolas Carlo
--41087ae9--2022-11-01--Jose Cabrera
0	1	src/editor/adapters/attempting-editor.ts
1	1	src/editor/editor.ts

--9c1ba7a6--2022-11-01--Jose Cabrera
3	0	src/refactorings/change-signature/change-signature.test.ts

--d2d8425a--2022-11-01--Jose Cabrera
9	9	src/refactorings/change-signature/change-signature.test.ts

--aa1f3fd3--2022-11-01--Jose Cabrera
0	1	src/editor/adapters/in-memory-editor.ts
1	1	src/editor/adapters/vscode-editor.ts

--5695d2da--2022-11-01--Jose Cabrera
1	1	src/editor/adapters/attempting-editor.ts
6	6	src/editor/adapters/in-memory-editor.ts
1	1	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts

--9439a47c--2022-11-01--Jose Cabrera
1	7	src/editor/adapters/vscode-editor.ts
8	0	src/editor/path.ts

--bea756d2--2022-11-01--Jose Cabrera
1	0	src/refactorings/change-signature/change-signature.ts

--628d0497--2022-11-01--Jose Cabrera
0	2	src/refactorings/change-signature/change-signature.ts

--3b5181c2--2022-11-01--Jose Cabrera
1	1	src/refactorings/change-signature/change-signature.test.ts

--05cf6cdf--2022-11-01--Jose Cabrera
32	68	src/refactorings/change-signature/change-signature.test.ts

--8eae0dbc--2022-11-01--Jose Cabrera
13	21	src/refactorings/change-signature/change-signature.test.ts

--d4d2cd12--2022-11-01--Jose Cabrera
5	5	src/refactorings/change-signature/change-signature.ts

--1ebc1522--2022-11-01--Jose Cabrera
1	3	src/editor/adapters/vscode-editor.ts

--bfd85878--2022-11-01--Nicolas Carlo
--368f8a61--2022-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--2947a6ec--2022-11-01--allcontributors[bot]
2	1	.all-contributorsrc

--da35ab8d--2022-11-01--allcontributors[bot]
1	1	README.md

--2fda1b2c--2022-11-01--Nicolas Carlo
--a36fbe88--2022-11-01--Nicolas Carlo
--41f085b0--2022-11-01--Nicolas Carlo
--898587bb--2022-11-01--Nicolas Carlo
--c3b1ea6b--2022-11-01--dependabot[bot]
1	1	package.json
5	11	yarn.lock

--673bee03--2022-11-01--dependabot[bot]
2	2	package.json
8	14	yarn.lock

--1a2c47fa--2022-11-01--dependabot[bot]
1	1	package.json
66	54	yarn.lock

--f254a7f1--2022-11-01--dependabot[bot]
1	1	package.json
1	16	yarn.lock

--57517df4--2022-10-20--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--462bd32b--2022-10-20--Nicolas Carlo
4	0	CHANGELOG.md

--868b9b86--2022-10-20--Nicolas Carlo
1	1	REFACTORINGS.md
2	2	src/editor/adapters/create-vscode-editor.ts
107	0	src/editor/adapters/vue-and-svelte-vscode-editor.ts
0	107	src/editor/adapters/vue-vscode-editor.ts
1	1	src/extension.ts

--732f529c--2022-10-20--Nicolas Carlo
8	0	CHANGELOG.md

--8c9bdacb--2022-10-20--Nicolas Carlo
1	1	README.md
1	1	REFACTORINGS.md

--84e8c52a--2022-10-20--Nicolas Carlo
--98af35f5--2022-10-20--Nicolas Carlo
--07f6a767--2022-10-20--Nicolas Carlo
--c84500ea--2022-10-20--Nicolas Carlo
--7d30edd6--2022-10-20--allcontributors[bot]
10	0	.all-contributorsrc

--e5562872--2022-10-20--allcontributors[bot]
2	1	README.md

--57caf29b--2022-10-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e2c29467--2022-10-20--dependabot[bot]
1	1	package.json
39	20	yarn.lock

--7f29ae5d--2022-10-20--Nicolas Carlo
--748a77ca--2022-10-20--Nicolas Carlo
--974da150--2022-10-20--Nicolas Carlo
--3a6f70bb--2022-10-20--Nicolas Carlo
--34f1e840--2022-10-20--allcontributors[bot]
2	1	.all-contributorsrc

--1fb00ec9--2022-10-20--allcontributors[bot]
46	46	README.md

--414c3493--2022-10-20--Nicolas Carlo
--9d87f5cf--2022-10-16--Christina Braun
32	5	src/ast/transformation.ts
36	0	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.test.ts
3	2	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts
13	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
4	1	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts
25	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
17	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
1	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
34	0	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
2	2	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
79	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
7	4	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
2	6	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts
13	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
11	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
1	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
11	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts
1	1	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--955aba62--2022-10-13--Simon Holmes
6	0	src/playground/Playground.svelte

--76f65126--2022-10-13--Simon Holmes
0	14	src/playground/Playground.svelte

--602da77a--2022-10-13--Simon Holmes
144	2	package.json
2	1	src/editor/adapters/create-vscode-editor.ts
2	1	src/extension.ts
20	0	src/playground/Playground.svelte

--b3f2a371--2022-10-09--Jose Cabrera
1	2	.vscode/settings.json
0	3	src/app.css

--f18ea66c--2022-10-09--Jose Cabrera
1	6	src/playground/maths/calculator.ts
33	0	src/refactorings/change-signature/change-signature.test.ts

--aa92bf0e--2022-10-09--Jose Cabrera
0	2	src/refactorings/change-signature/change-signature.test.ts

--8176f3f6--2022-10-09--Jose Cabrera
13	9	src/refactorings/change-signature/change-signature.test.ts

--9bc23e0c--2022-10-09--Jose Cabrera
13	5	src/editor/adapters/vscode-editor.ts
7	0	src/playground/maths/calculator.ts
38	8	src/refactorings/change-signature/change-signature.test.ts
46	3	src/refactorings/change-signature/change-signature.ts

--2e1f0e91--2022-10-09--Jose Cabrera
100	1	src/refactorings/change-signature/change-signature.test.ts
18	9	src/refactorings/change-signature/change-signature.ts

--f32e51ea--2022-10-09--Jose Cabrera
1	1	src/editor/error-reason.ts
18	13	src/refactorings/change-signature/change-signature.ts

--c610561a--2022-10-09--Jose Cabrera
3	3	src/editor/adapters/attempting-editor.ts
0	9	src/playground/calculator.ts
0	10	src/playground/fileOne.ts
0	10	src/playground/fileTwo.ts
0	11	src/playground/maths.ts
9	0	src/playground/maths/calculator.ts
5	0	src/playground/maths/maths.ts

--1994b908--2022-10-09--Jose Cabrera
1	2	src/editor/adapters/attempting-editor.ts
10	6	src/editor/adapters/in-memory-editor.ts
5	6	src/editor/adapters/vscode-editor.ts
9	10	src/editor/editor.ts
14	0	src/refactorings/change-signature/change-signature.test.ts
16	13	src/refactorings/change-signature/change-signature.ts

--0ac35ab1--2022-10-09--Jose Cabrera
12	12	src/editor/adapters/vscode-editor.ts

--4ab917ef--2022-10-09--Jose Cabrera
10	1	src/editor/adapters/attempting-editor.ts
10	1	src/editor/adapters/in-memory-editor.ts
207	1	src/editor/adapters/vscode-editor.ts
13	0	src/editor/editor.ts
1	1	src/playground/calculator.ts
1	1	src/playground/fileOne.ts
2	2	src/playground/maths.ts
20	185	src/refactorings/change-signature/change-signature.ts

--f3b8a937--2022-10-09--Jose Cabrera
22	22	src/refactorings/change-signature/change-signature.ts

--59e1546c--2022-10-08--Jose Cabrera
2	2	src/refactorings/change-signature/change-signature.ts

--8ac51d83--2022-10-08--Jose Cabrera
2	2	src/playground/maths.ts

--f01b0378--2022-10-08--Jose Cabrera
2	2	src/playground/maths.ts
141	128	src/refactorings/change-signature/change-signature.ts
0	128	src/refactorings/change-signature/webView.html

--f9bdef67--2022-10-08--Jose Cabrera
0	8	package.json
3	0	src/app.css
1	1	src/editor/adapters/in-memory-editor.ts
2	2	src/playground/maths.ts
196	6	src/refactorings/change-signature/change-signature.ts
128	0	src/refactorings/change-signature/webView.html

--b893c613--2022-10-08--Jose Cabrera
124	19	src/refactorings/change-signature/change-signature.test.ts

--f2daf62e--2022-10-08--Jose Cabrera
60	0	src/refactorings/change-signature/change-signature.test.ts

--2088b37d--2022-10-08--Jose Cabrera
23	25	src/editor/adapters/in-memory-editor.ts

--65c575e3--2022-10-08--Jose Cabrera
43	2	src/editor/adapters/in-memory-editor.ts
21	1	src/refactorings/change-signature/change-signature.test.ts
2	0	src/refactorings/change-signature/change-signature.ts

--6a031b7a--2022-10-08--Jose Cabrera
31	3	src/editor/adapters/in-memory-editor.ts
2	2	src/editor/adapters/vscode-editor.ts
26	18	src/refactorings/change-signature/change-signature.test.ts
1	0	src/refactorings/change-signature/change-signature.ts

--fad8fc6f--2022-10-08--Jose Cabrera
1	6	src/refactorings/change-signature/change-signature.ts

--19a12530--2022-10-08--Jose Cabrera
2	1	src/editor/adapters/attempting-editor.ts
19	2	src/editor/adapters/vscode-editor.ts
9	0	src/editor/code-reference.ts
2	1	src/editor/editor.ts
13	22	src/refactorings/change-signature/change-signature.ts

--39e268fd--2022-10-08--Jose Cabrera
4	0	src/editor/adapters/attempting-editor.ts
4	0	src/editor/adapters/in-memory-editor.ts
8	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts
6	6	src/refactorings/change-signature/change-signature.ts

--8ec94aff--2022-10-08--Jose Cabrera
3	9	src/editor/adapters/attempting-editor.ts
3	10	src/editor/adapters/in-memory-editor.ts
14	26	src/editor/adapters/vscode-editor.ts
3	6	src/editor/editor.ts
4	9	src/refactorings/change-signature/change-signature.ts

--c340bc8f--2022-10-08--Jose Cabrera
2	3	src/editor/adapters/vscode-editor.ts
33	26	src/refactorings/change-signature/change-signature.ts

--e6adbadf--2022-10-08--Jose Cabrera
2	1	.vscode/settings.json
8	0	package.json
7	0	src/editor/adapters/attempting-editor.ts
8	0	src/editor/adapters/in-memory-editor.ts
21	0	src/editor/adapters/vscode-editor.ts
3	0	src/editor/editor.ts
4	0	src/editor/error-reason.ts
2	0	src/extension.ts
9	0	src/playground/calculator.ts
10	0	src/playground/fileOne.ts
10	0	src/playground/fileTwo.ts
11	0	src/playground/maths.ts
0	0	src/playground/playground.ts
33	0	src/refactorings/change-signature/change-signature.test.ts
109	0	src/refactorings/change-signature/change-signature.ts
17	0	src/refactorings/change-signature/index.ts

--013a9706--2022-10-07--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--36861b44--2022-10-07--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--41ef8340--2022-10-07--dependabot[bot]
6	6	package.json
268	179	yarn.lock

--63f8e92b--2022-10-07--Nicolas Carlo
--ea86c123--2022-10-07--Nicolas Carlo
--f253b525--2022-10-07--dependabot[bot]
1	1	package.json
5	10	yarn.lock

--7713f66e--2022-10-07--dependabot[bot]
2	2	package.json
8	8	yarn.lock

--22b5274e--2022-10-07--Nicolas Carlo
--cffcca6c--2022-10-07--Nicolas Carlo
--2b27fd3b--2022-10-07--Nicolas Carlo
--039720a7--2022-10-07--Nicolas Carlo
--8e1bb2d7--2022-10-07--Nicolas Carlo
--bd141f1b--2022-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3f4a21df--2022-10-01--dependabot[bot]
1	1	package.json
217	118	yarn.lock

--9881dec7--2022-10-01--dependabot[bot]
1	1	package.json
41	41	yarn.lock

--49f32068--2022-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--def92a36--2022-10-01--dependabot[bot]
1	1	package.json
4	9	yarn.lock

--fb89b506--2022-09-23--Nicolas Carlo
--793f1591--2022-09-23--Nicolas Carlo
--74570d33--2022-09-23--allcontributors[bot]
10	0	.all-contributorsrc

--65a83df8--2022-09-23--allcontributors[bot]
63	60	README.md

--791eb621--2022-09-23--Artem Zhivoderov
8	8	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
3	3	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
6	1	src/refactorings/extract/extract-variable/occurrence.ts
9	0	src/refactorings/extract/extract-variable/variable.ts

--0109e114--2022-09-07--Nicolas Carlo
--8bb95350--2022-09-07--Nicolas Carlo
--6ce14060--2022-09-01--dependabot[bot]
1	1	package.json
55	20	yarn.lock

--fd98498c--2022-09-01--dependabot[bot]
1	1	package.json
44	23	yarn.lock

--773539e6--2022-08-31--Nicolas Carlo
18	2	src/highlights/toggle-highlight/toggle-highlight.test.ts

--c3731044--2022-08-31--Nicolas Carlo
14	14	src/editor/adapters/vscode-editor.ts

--fb840412--2022-08-24--Nicolas Carlo
50	51	src/editor/adapters/vscode-editor.ts
2	2	src/extension.ts

--c06bd537--2022-08-24--Nicolas Carlo
19	20	src/editor/adapters/in-memory-editor.ts
18	29	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts
35	17	src/highlights/highlights-repository.ts
4	0	src/highlights/highlights.ts

--339aa2dc--2022-08-24--Nicolas Carlo
2	1	.vscode/settings.json

--9d8fbd1f--2022-08-23--Nicolas Carlo
2	5	src/editor/adapters/attempting-editor.ts
11	3	src/editor/adapters/in-memory-editor.ts
7	18	src/editor/adapters/vscode-editor.ts
0	1	src/editor/editor.ts
13	0	src/highlights/highlights-repository.ts
0	1	src/highlights/toggle-highlight/toggle-highlight.ts

--7ec029f4--2022-08-23--Nicolas Carlo
17	17	src/editor/adapters/vscode-editor.ts
0	12	src/highlights/highlights-repository.ts

--13b476f8--2022-08-10--Nicolas Carlo
17	3	src/editor/adapters/vscode-editor.ts
13	7	src/highlights/highlights-repository.ts

--c35c54d4--2022-08-08--Nicolas Carlo
41	21	src/editor/adapters/vscode-editor.ts
6	3	src/highlights/highlights-repository.ts
5	12	src/highlights/highlights.ts

--75364be6--2022-07-23--Nicolas Carlo
11	22	src/editor/adapters/vscode-editor.ts
55	0	src/highlights/highlights-repository.ts

--4cfdce94--2022-06-25--Nicolas Carlo
1	1	src/editor/adapters/attempting-editor.ts
1	1	src/editor/adapters/in-memory-editor.ts
1	1	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts
0	38	src/editor/highlights.ts
38	0	src/highlights/highlights.ts

--8577d528--2022-06-25--Nicolas Carlo
56	0	src/editor/selection.test.ts

--c4a56377--2022-06-22--Nicolas Carlo
133	0	src/editor/selection.test.ts
58	1	src/editor/selection.ts

--e0c6b195--2022-05-09--Nicolas Carlo
6	5	src/editor/adapters/attempting-editor.ts
24	19	src/editor/adapters/in-memory-editor.ts
32	28	src/editor/adapters/vscode-editor.ts
4	3	src/editor/editor.ts
18	9	src/editor/highlights.ts
12	10	src/highlights/toggle-highlight/toggle-highlight.ts

--478fe340--2022-05-09--Nicolas Carlo
1	1	package.json

--7b693707--2022-05-09--Nicolas Carlo
13	16	src/editor/adapters/vscode-editor.ts
29	0	src/editor/highlights.ts

--d3216b08--2022-05-09--Nicolas Carlo
3	1	src/editor/adapters/vscode-editor.ts

--c6541985--2022-05-09--Nicolas Carlo
2	3	src/playground/playground.ts

--cf0c6ab4--2022-04-29--Nicolas Carlo
101	0	src/playground/playground.ts

--ad2d49ab--2022-04-29--Nicolas Carlo
11	13	src/extension.ts

--2087aaf2--2022-04-29--Nicolas Carlo
51	7	src/editor/adapters/vscode-editor.ts
10	0	src/extension.ts

--fc231b87--2022-04-08--Nicolas Carlo
2	0	src/editor/adapters/vscode-editor.ts

--47801fb0--2022-04-08--Nicolas Carlo
6	7	src/editor/adapters/vscode-editor.ts

--37c15a17--2022-04-08--Nicolas Carlo
2	2	package.json

--53615f23--2022-04-03--Nicolas Carlo
11	0	package.json
4	0	src/editor/adapters/attempting-editor.ts
6	0	src/editor/adapters/in-memory-editor.ts
6	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts
11	8	src/extension.ts
10	0	src/highlights/remove-all-highlights.ts

--5e4c05fb--2022-04-03--Nicolas Carlo
2	2	src/extension.ts
0	11	src/highlight/index.ts
0	80	src/highlight/toggle-highlight.test.ts
0	30	src/highlight/toggle-highlight.ts
11	0	src/highlights/toggle-highlight/index.ts
79	0	src/highlights/toggle-highlight/toggle-highlight.test.ts
29	0	src/highlights/toggle-highlight/toggle-highlight.ts

--36da011c--2022-04-03--Nicolas Carlo
14	5	src/editor/adapters/vscode-editor.ts

--c56dab92--2022-04-03--Nicolas Carlo
0	1	src/editor/adapters/attempting-editor.ts
0	1	src/editor/adapters/in-memory-editor.ts
1	1	src/editor/adapters/vscode-editor.ts
0	2	src/editor/colors.ts
0	1	src/editor/editor.ts
0	1	src/highlight/toggle-highlight.ts

--febb86ee--2022-04-03--Nicolas Carlo
8	0	src/editor/adapters/attempting-editor.ts
15	1	src/editor/adapters/in-memory-editor.ts
17	0	src/editor/adapters/vscode-editor.ts
2	0	src/editor/editor.ts
25	1	src/highlight/toggle-highlight.test.ts
7	2	src/highlight/toggle-highlight.ts

--87f4c3bb--2022-04-02--Nicolas Carlo
2	2	src/editor/adapters/attempting-editor.ts
2	6	src/editor/adapters/in-memory-editor.ts
3	2	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts
1	2	src/highlight/toggle-highlight.ts

--d3948508--2022-04-02--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts
11	11	src/editor/colors.ts
2	7	src/highlight/toggle-highlight.ts

--323d3c1b--2022-04-02--Nicolas Carlo
11	0	package.json
13	13	src/extension.ts

--306fd52a--2022-04-02--Nicolas Carlo
9	0	src/ast/scope.ts
11	0	src/highlight/index.ts
56	0	src/highlight/toggle-highlight.test.ts
32	0	src/highlight/toggle-highlight.ts

--0f0140ea--2022-04-02--Nicolas Carlo
6	0	src/editor/index.ts

--efb27948--2022-04-02--Nicolas Carlo
2	2	src/editor/adapters/in-memory-editor.ts

--95a6eabf--2022-04-02--Nicolas Carlo
9	1	src/editor/adapters/attempting-editor.ts
55	0	src/editor/adapters/in-memory-editor.ts
26	0	src/editor/adapters/vscode-editor.ts
36	0	src/editor/colors.ts
3	0	src/editor/editor.ts

--229230db--2022-08-01--Nicolas Carlo
2	0	.github/workflows/ovsx-deploy.yml
1	0	.github/workflows/vscode-deploy.yml
6	1	CHANGELOG.md
1	1	package.json

--1b40da8c--2022-08-01--Nicolas Carlo
7	1	CHANGELOG.md
1	1	package.json
1	0	webpack.config.js

--758de51d--2022-08-01--Nicolas Carlo
4	0	CHANGELOG.md
1	1	package.json

--64f81093--2022-08-01--Nicolas Carlo
1	1	package.json

--25a3f0a3--2022-08-01--Nicolas Carlo
--9777c60e--2022-08-01--Nicolas Carlo
--8364c532--2022-08-01--dependabot[bot]
1	1	package.json
1	6	yarn.lock

--92a0e806--2022-08-01--Nicolas Carlo
--e389f37a--2022-08-01--Nicolas Carlo
--70e2c61b--2022-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ce31f51c--2022-08-01--dependabot[bot]
1	1	package.json
35	16	yarn.lock

--0cc425df--2022-08-01--dependabot[bot]
1	1	package.json
20	34	yarn.lock

--ef9e9132--2022-08-01--Nicolas Carlo
--cbc1596a--2022-08-01--Nicolas Carlo
--389f6636--2022-08-01--Nicolas Carlo
--619ff765--2022-08-01--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--9b3085f6--2022-08-01--dependabot[bot]
1	1	package.json
178	146	yarn.lock

--deffceda--2022-08-01--Nicolas Carlo
--47a08704--2022-08-01--Nicolas Carlo
--565d67ca--2022-08-01--dependabot[bot]
1	1	package.json
30	25	yarn.lock

--e76dbc6a--2022-08-01--dependabot[bot]
3	3	package.json
27	81	yarn.lock

--e73ab7ac--2022-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--4d421ba8--2022-08-01--Nicolas Carlo
--e2e213eb--2022-08-01--dependabot[bot]
1	1	package.json
20	54	yarn.lock

--4ec9d0e2--2022-08-01--Nicolas Carlo
--86c56f67--2022-08-01--Nicolas Carlo
--dcd9889d--2022-08-01--Nicolas Carlo
--3d0efff5--2022-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--5e272671--2022-08-01--dependabot[bot]
1	1	package.json
42	8	yarn.lock

--d7a24912--2022-08-01--dependabot[bot]
1	1	package.json
17	43	yarn.lock

--025e1785--2022-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e1160fec--2022-07-31--Nicolas Carlo
1	0	CHANGELOG.md
53	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
13	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--0cdea62d--2022-07-31--Nicolas Carlo
12	7	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--76990b81--2022-07-31--Nicolas Carlo
4	0	CHANGELOG.md
12	3	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
3	2	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts

--b4cf17bb--2022-07-31--Nicolas Carlo
1	0	CHANGELOG.md
6	0	src/refactorings/inline/inline-variable/find-inlinable-code.ts
8	1	src/refactorings/inline/inline-variable/inline-variable.object-pattern.test.ts

--5a529014--2022-07-26--Nicolas Carlo
--a053e7de--2022-07-26--allcontributors[bot]
9	0	.all-contributorsrc

--8d9c3ce0--2022-07-26--allcontributors[bot]
2	1	README.md

--a32564ec--2022-07-26--Nicolas Carlo
--b87299f6--2022-07-25--Andrew Ash
1	1	README.md

--20ef49eb--2022-07-21--Nicolas Carlo
--aaba2b7f--2022-07-20--dependabot[bot]
33	37	yarn.lock

--ca425b2a--2022-07-07--Nicolas Carlo
--325f8cb3--2022-07-07--Nicolas Carlo
--7961cf53--2022-07-07--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--2a2c2356--2022-07-07--dependabot[bot]
1	1	package.json
8	42	yarn.lock

--ef04e83d--2022-07-07--Nicolas Carlo
--b76b82c0--2022-07-07--dependabot[bot]
1	1	package.json
53	53	yarn.lock

--bac92baf--2022-07-07--Nicolas Carlo
--3d7698cc--2022-07-07--Nicolas Carlo
--e00f42fe--2022-07-07--Nicolas Carlo
--ce9fb151--2022-07-07--Nicolas Carlo
--c2abbaf7--2022-07-07--dependabot[bot]
1	1	package.json
411	523	yarn.lock

--c3f28a36--2022-07-07--dependabot[bot]
1	1	package.json
1	6	yarn.lock

--3ab3cc89--2022-07-07--dependabot[bot]
4	4	package.json
53	19	yarn.lock

--2ad7325b--2022-07-07--Nicolas Carlo
--9cea1e81--2022-07-07--Nicolas Carlo
--5ebb64d9--2022-07-01--dependabot[bot]
3	3	package.json
54	63	yarn.lock

--c6bd7e2c--2022-07-01--dependabot[bot]
3	3	package.json
82	85	yarn.lock

--36a47f74--2022-07-01--Nicolas Carlo
--a24fed6c--2022-07-01--Nicolas Carlo
--c28bb8ac--2022-07-01--Nicolas Carlo
--bb04e4b8--2022-07-01--Nicolas Carlo
--d524cdcf--2022-07-01--dependabot[bot]
5	5	package.json
22	17	yarn.lock

--0881a728--2022-07-01--dependabot[bot]
5	5	package.json
22	17	yarn.lock

--8e66b734--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--08afb584--2022-07-01--dependabot[bot]
5	5	package.json
27	65	yarn.lock

--cd210ad8--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--a24ac1a7--2022-07-01--Nicolas Carlo
--eb4ad90f--2022-07-01--Nicolas Carlo
--1b63fc16--2022-07-01--Nicolas Carlo
--4b9272f1--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c57b74bd--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3bb3b15b--2022-07-01--dependabot[bot]
1	1	package.json
6	1	yarn.lock

--c386286d--2022-07-01--Nicolas Carlo
--233ad687--2022-07-01--Nicolas Carlo
--2a4e0da0--2022-07-01--Nicolas Carlo
--92f0b0da--2022-07-01--Nicolas Carlo
--256ec91e--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b3493ac7--2022-07-01--dependabot[bot]
1	1	package.json
150	80	yarn.lock

--60b90e40--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--822f2875--2022-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--1c1c2186--2022-06-18--Nicolas Carlo
--b7a13330--2022-06-18--Nicolas Carlo
4	1	src/ast/selection.ts

--3f0af0b3--2022-06-18--dependabot[bot]
2	2	package.json
9	62	yarn.lock

--db40d573--2022-06-18--Nicolas Carlo
--97bde267--2022-06-18--Nicolas Carlo
1	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts

--ffb151da--2022-06-18--Nicolas Carlo
1	0	src/refactorings/extract-interface/extract-interface.ts
7	7	src/refactorings/extract/extract-variable/extract-variable.ts
6	7	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--6767df1c--2022-06-18--Nicolas Carlo
--d8e6a1e5--2022-06-18--allcontributors[bot]
9	0	.all-contributorsrc

--c5a00a4c--2022-06-18--allcontributors[bot]
2	1	README.md

--b7dc2e04--2022-06-18--Nicolas Carlo
1	0	CHANGELOG.md
18	8	src/ast/scope.ts
26	2	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts

--7ad1310d--2022-06-18--Nicolas Carlo
4	0	CHANGELOG.md
9	0	src/ast/domain.ts
7	2	src/refactorings/move-to-existing-file/movable-node.ts
24	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
1	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--1ac5af83--2022-06-14--Nicolas Carlo
--1bf547ba--2022-06-14--dependabot[bot]
1	1	package.json
11	11	yarn.lock

--10a1cca1--2022-06-14--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--af13336b--2022-06-14--Nicolas Carlo
--30fe9f1d--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3feff25d--2022-06-13--Nicolas Carlo
--e18e4970--2022-06-13--Nicolas Carlo
--ee1455c9--2022-06-13--Nicolas Carlo
--84ba099a--2022-06-14--dependabot[bot]
1	1	package.json
11	0	yarn.lock

--a1d11e93--2022-06-14--dependabot[bot]
1	1	package.json
11	57	yarn.lock

--ccc61add--2022-06-14--dependabot[bot]
3	3	package.json
29	29	yarn.lock

--a4cd3d43--2022-06-13--Nicolas Carlo
--d9cdde5b--2022-06-13--Nicolas Carlo
--2389dbd2--2022-06-13--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--5899a9c2--2022-06-14--dependabot[bot]
3	3	package.json
119	52	yarn.lock

--9b0d4e43--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--7df014a8--2022-06-13--Nicolas Carlo
--98145ddf--2022-06-13--Nicolas Carlo
--66fc2ef2--2022-06-13--Nicolas Carlo
--6f84e3d4--2022-06-13--Nicolas Carlo
--7e99a0a8--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--1380251f--2022-06-14--dependabot[bot]
1	1	package.json
5	13	yarn.lock

--332ece0a--2022-06-14--dependabot[bot]
1	1	package.json
20	20	yarn.lock

--93d7035b--2022-06-14--dependabot[bot]
1	1	package.json
91	29	yarn.lock

--dec80208--2022-06-13--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--19bf095f--2022-06-13--Nicolas Carlo
--4679e5ad--2022-06-13--Nicolas Carlo
--c21fc891--2022-06-13--Nicolas Carlo
1	1	package.json

--dc3a6261--2022-06-13--Nicolas Carlo
3	0	package.json

--abe20ed1--2022-06-13--Nicolas Carlo
--7fef65dc--2022-06-13--Nicolas Carlo
--1e451e2f--2022-06-13--Nicolas Carlo
--7cdb1630--2022-06-13--Nicolas Carlo
--c7174ce9--2022-06-14--dependabot[bot]
1	1	package.json
21	73	yarn.lock

--9f040e23--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--054edd61--2022-06-14--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--ba29111c--2022-06-13--Nicolas Carlo
--c87a82e1--2022-06-13--Nicolas Carlo
--d421e580--2022-06-14--dependabot[bot]
2	2	package.json
51	6	yarn.lock

--11abb74c--2022-06-13--Nicolas Carlo
--d34cd05f--2022-06-14--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--3467e417--2022-06-13--Nicolas Carlo
--e442b256--2022-06-13--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--b0ec430c--2022-06-13--Nicolas Carlo
3	1	CHANGELOG.md
2	0	REFACTORINGS.md

--83f72b1a--2022-06-13--Nicolas Carlo
34	0	src/playground/playground.jsx

--83c240eb--2022-06-13--Nicolas Carlo
5	2	webpack.config.js

--8dbf7e06--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--40d21cd3--2022-06-14--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--92981c3b--2022-06-14--dependabot[bot]
1	1	package.json
125	62	yarn.lock

--89e45885--2022-06-13--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--4f834f65--2022-06-13--Nicolas Carlo
1	0	package.json
6	0	webpack.config.js
1	1	yarn.lock

--c7d6182a--2022-06-13--Nicolas Carlo
8	1	src/refactorings/react/extract-use-callback/extract-use-callback.ts

--d554335b--2022-06-13--Nicolas Carlo
1	1	package.json
17	9	yarn.lock

--e72f6e30--2022-06-13--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c0f0f15c--2022-06-13--Nicolas Carlo
1	1	REFACTORINGS.md

--84270281--2022-06-13--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--d6414811--2022-06-13--dependabot[bot]
1	1	package.json
339	271	yarn.lock

--3fefa3e8--2022-06-13--Nicolas Carlo
--2a30a4c9--2022-06-13--Nicolas Carlo
--428acb72--2022-06-13--Nicolas Carlo
--6da38189--2022-06-13--Nicolas Carlo
--2c765ca7--2022-06-13--Nicolas Carlo
1	0	CHANGELOG.md
12	0	REFACTORINGS.md
-	-	docs/demo/extract-use-callback.gif

--16844675--2022-06-13--Nicolas Carlo
1	0	webpack.config.js

--543a7379--2022-06-13--Nicolas Carlo
2	2	package.json
74	16	src/refactorings/react/extract-use-callback/extract-use-callback.ts
1	1	webpack.config.js

--fe1c0022--2022-06-13--Nicolas Carlo
1	1	package.json
1	1	yarn.lock

--bde48985--2022-06-01--dependabot[bot]
1	1	package.json
8	1	yarn.lock

--e5e21560--2022-06-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--02fb5e2c--2022-06-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c384e477--2022-06-01--dependabot[bot]
1	1	package.json
40	148	yarn.lock

--9c076fb2--2022-05-19--Nicolas Carlo
1	2	package.json
24	0	src/refactorings/react/extract-use-callback/extract-use-callback.test.ts
1	1	src/refactorings/react/extract-use-callback/extract-use-callback.ts
1	10	yarn.lock

--b37139bc--2022-05-18--Nicolas Carlo
--a64d2c02--2022-05-19--allcontributors[bot]
2	1	.all-contributorsrc

--6ea49643--2022-05-19--allcontributors[bot]
1	1	README.md

--6818070e--2022-05-18--Nicolas Carlo
--8122a6b3--2022-05-14--Ian Obermiller
0	1	src/refactorings/react/extract-use-callback/extract-use-callback.ts

--67160780--2022-05-14--Ian Obermiller
1	0	package.json
48	49	src/ast/transformation.ts
7	7	src/refactorings/react/extract-use-callback/extract-use-callback.ts
10	1	yarn.lock

--464ea50f--2022-05-14--Ian Obermiller
4	3	package.json
13	1	src/refactorings/react/extract-use-callback/extract-use-callback.test.ts
30	1	src/refactorings/react/extract-use-callback/extract-use-callback.ts
5	0	yarn.lock

--928ac85b--2022-05-11--Ian Obermiller
4	0	src/editor/error-reason.ts
2	1	src/extension.ts
89	0	src/refactorings/react/extract-use-callback/extract-use-callback.test.ts
84	0	src/refactorings/react/extract-use-callback/extract-use-callback.ts
16	0	src/refactorings/react/extract-use-callback/index.ts

--375b104c--2022-05-04--Nicolas Carlo
4	0	CHANGELOG.md
7	0	src/playground/playground.ts
1	0	src/refactorings/inline/inline-variable/find-inlinable-code.test.ts
14	5	src/refactorings/inline/inline-variable/find-inlinable-code.ts
15	2	src/refactorings/inline/inline-variable/inline-variable.test.ts
12	5	src/refactorings/inline/inline-variable/inline-variable.ts

--87b47b5f--2022-05-02--Nicolas Carlo
--e7765f88--2022-05-02--Nicolas Carlo
--b58d0da6--2022-05-02--dependabot[bot]
1	1	package.json
131	175	yarn.lock

--a6becf8e--2022-05-02--Nicolas Carlo
--da9d7823--2022-05-02--Nicolas Carlo
--130dd331--2022-05-02--Nicolas Carlo
--7734318b--2022-05-02--Nicolas Carlo
--3cd0fa64--2022-05-01--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--f2402816--2022-05-01--dependabot[bot]
1	1	package.json
28	42	yarn.lock

--fe5dbc9a--2022-05-01--Nicolas Carlo
--ce6ddb96--2022-05-01--Nicolas Carlo
--31a8875d--2022-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ab0a4909--2022-05-01--dependabot[bot]
1	1	package.json
104	43	yarn.lock

--62cbe8cf--2022-05-01--dependabot[bot]
1	6	yarn.lock

--386b195f--2022-05-01--Nicolas Carlo
--4814c839--2022-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c9c08676--2022-05-01--Nicolas Carlo
--7057691f--2022-05-01--Nicolas Carlo
--a8c66cf4--2022-05-01--dependabot[bot]
40	26	yarn.lock

--2dfefded--2022-05-01--Nicolas Carlo
--017ce0c5--2022-05-01--Nicolas Carlo
--11ba4c2b--2022-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--d99c24b3--2022-05-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--1df3c5ff--2022-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b853c2ea--2022-05-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--026e22b5--2022-05-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--5a472c37--2022-04-29--Nicolas Carlo
--c4b7b999--2022-04-29--allcontributors[bot]
9	0	.all-contributorsrc

--4b9bc80e--2022-04-29--allcontributors[bot]
4	1	README.md

--4ffd4f93--2022-04-28--Nicolas Carlo
1	0	CHANGELOG.md
42	2	src/refactorings/inline/inline-function/inline-function.test.ts
14	4	src/refactorings/inline/inline-function/inline-function.ts

--ddea1b35--2022-04-28--Nicolas Carlo
1	0	CHANGELOG.md
5	2	src/ast/scope.ts
21	2	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--722fe0be--2022-04-28--Nicolas Carlo
4	0	CHANGELOG.md
14	3	src/ast/transformation.ts
30	3	src/refactorings/extract-interface/extract-interface.test.ts
23	1	src/refactorings/extract-interface/extract-interface.ts

--e8e95490--2022-04-21--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--c09f8d28--2022-04-21--Nicolas Carlo
--b58ec420--2022-04-21--allcontributors[bot]
9	0	.all-contributorsrc

--7f036a52--2022-04-21--allcontributors[bot]
2	1	README.md

--0acf6380--2022-04-21--Nicolas Carlo
--c09547ae--2022-04-21--allcontributors[bot]
9	0	.all-contributorsrc

--339b091d--2022-04-21--allcontributors[bot]
2	1	README.md

--c8a7b519--2022-04-21--Nicolas Carlo
4	0	CHANGELOG.md
20	0	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
4	11	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--f94b18e2--2022-04-21--Nicolas Carlo
1	0	CHANGELOG.md
25	0	src/refactorings/extract-interface/extract-interface.test.ts
6	2	src/refactorings/extract-interface/extract-interface.ts

--3f801fb6--2022-04-21--Nicolas Carlo
1	0	CHANGELOG.md
5	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--2ba0bc7f--2022-04-01--Nicolas Carlo
4	0	CHANGELOG.md
13	0	src/refactorings/inline/inline-function/inline-function.test.ts
9	2	src/refactorings/inline/inline-function/inline-function.ts

--cfa120bd--2022-04-01--Nicolas Carlo
--5995d9a7--2022-04-01--Nicolas Carlo
4	0	CHANGELOG.md
2	0	REFACTORINGS.md
5	0	package.json
1	1	src/extension.ts
12	4	src/refactorings/inline/index.ts
23	8	src/refactorings/inline/inline-variable/inline-variable.ts

--18310227--2022-04-01--allcontributors[bot]
9	0	.all-contributorsrc

--c8e1e027--2022-04-01--allcontributors[bot]
2	1	README.md

--942d7dcd--2022-04-01--Nicolas Carlo
--e71c2160--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--d8dad867--2022-04-01--Nicolas Carlo
--cf66b026--2022-04-01--Nicolas Carlo
1	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts

--03dd8bb5--2022-04-01--dependabot[bot]
1	1	package.json
1	6	yarn.lock

--e6c6ac46--2022-04-01--Nicolas Carlo
--16e1613b--2022-04-01--Nicolas Carlo
--2804024f--2022-04-01--Nicolas Carlo
--67d385df--2022-04-01--Nicolas Carlo
--4822e63f--2022-04-01--dependabot[bot]
1	1	package.json
20	54	yarn.lock

--91eef086--2022-04-01--Nicolas Carlo
--05eb356e--2022-04-01--Nicolas Carlo
--d19c0ae0--2022-04-01--Nicolas Carlo
--5dacaf9f--2022-04-01--dependabot[bot]
1	1	package.json
10	10	yarn.lock

--2fdb1ca6--2022-04-01--dependabot[bot]
1	1	package.json
42	8	yarn.lock

--ccc85010--2022-04-01--dependabot[bot]
1	1	package.json
4	30	yarn.lock

--2a2a8a78--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c0bd50ba--2022-04-01--Nicolas Carlo
--37ff83bd--2022-04-01--dependabot[bot]
2	2	package.json
14	8	yarn.lock

--e3deb4e7--2022-04-01--Nicolas Carlo
--0062b884--2022-04-01--dependabot[bot]
2	2	package.json
13	13	yarn.lock

--5a190052--2022-04-01--Nicolas Carlo
--24ec202d--2022-04-01--Nicolas Carlo
--198b342b--2022-04-01--Nicolas Carlo
--3b90c181--2022-04-01--dependabot[bot]
3	3	yarn.lock

--15969802--2022-04-01--Nicolas Carlo
--167cf3c8--2022-04-01--Nicolas Carlo
--1c97eddc--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--70904561--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--308a07d9--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--a2aaf3ff--2022-04-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b0a5f087--2022-04-01--dependabot[bot]
1	1	package.json
49	32	yarn.lock

--4c9c8c80--2022-04-01--dependabot[bot]
1	1	package.json
10	4	yarn.lock

--c3a7303f--2022-03-11--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--32bd1728--2022-03-11--Nicolas Carlo
2	0	REFACTORINGS.md

--bd903f68--2022-03-11--Nicolas Carlo
--42d8de1e--2022-03-11--Nicolas Carlo
--cd35de2b--2022-03-11--allcontributors[bot]
2	1	.all-contributorsrc

--9bb6ddaf--2022-03-11--allcontributors[bot]
1	1	README.md

--a314ca5c--2022-03-11--Nicolas Carlo
1	0	CHANGELOG.md

--0b47d8af--2022-03-11--Nicolas Carlo
38	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--eeb923a3--2022-03-11--Nicolas Carlo
23	6	src/refactorings/move-to-existing-file/export-declaration.ts
1	164	src/refactorings/move-to-existing-file/movable-node.ts
19	70	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--804a7af6--2022-03-11--Nicolas Carlo
59	0	src/refactorings/move-to-existing-file/movable-node.ts
25	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
18	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--d802ec0a--2022-03-10--Nicolas Carlo
16	14	src/ast/scope.ts
17	4	src/refactorings/move-to-existing-file/movable-node.ts
31	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--3a4bb8d4--2022-03-10--Nicolas Carlo
49	0	src/refactorings/move-to-existing-file/movable-node.ts
54	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
20	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--7fa78226--2022-03-10--Nicolas Carlo
41	0	src/refactorings/move-to-existing-file/export-declaration.ts
233	0	src/refactorings/move-to-existing-file/movable-node.ts
9	268	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--2b687b6e--2022-03-04--Nicolas Carlo
--bf01a8b1--2022-03-04--Nicolas Carlo
--2cd34198--2022-03-04--allcontributors[bot]
9	0	.all-contributorsrc

--20d29614--2022-03-04--allcontributors[bot]
2	1	README.md

--1535a013--2022-03-04--Nicolas Carlo
4	0	CHANGELOG.md

--fec09b28--2022-03-04--Nicolas Carlo
42	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
35	24	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--5ad7b5dc--2022-03-04--Nicolas Carlo
48	18	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--6f52009a--2022-03-04--Nicolas Carlo
42	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
81	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--db8f00a4--2022-03-04--Nicolas Carlo
21	8	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
73	3	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--5f92aeb3--2022-03-04--Nicolas Carlo
1	1	package.json
19	21	yarn.lock

--e4f131e2--2022-03-04--Nicolas Carlo
6	0	src/editor/selection.ts
0	9	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts
0	4	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts
0	9	src/refactorings/extract-interface/extract-interface.ts

--51ab1e1f--2022-03-04--Nicolas Carlo
12	0	src/ast/domain.ts
38	24	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--61bed3f9--2022-03-04--Nicolas Carlo
1	1	package.json
1	0	src/refactorings/extract/extract-type/extract-type.ts
2	18	yarn.lock

--12a4a1e0--2022-03-04--Nicolas Carlo
1	1	package.json
10	12	yarn.lock

--3e0f42a8--2022-03-04--Nicolas Carlo
1	1	package.json
5	28	yarn.lock

--df8c141c--2022-03-04--Nicolas Carlo
1	1	package.json

--0b2dcc24--2022-03-04--Nicolas Carlo
--bdfc0ff9--2022-03-01--Nicolas Carlo
--f053f3a3--2022-03-01--Nicolas Carlo
--aa25d18b--2022-03-01--Nicolas Carlo
--1729912f--2022-03-01--dependabot[bot]
1	1	package.json
14	0	yarn.lock

--8810fd13--2022-03-01--dependabot[bot]
1	1	package.json
22	58	yarn.lock

--4c66db17--2022-03-01--Nicolas Carlo
--6756688c--2022-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--8f1ff245--2022-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--9c74f303--2022-03-01--Nicolas Carlo
--644cceba--2022-03-01--Nicolas Carlo
--e1297764--2022-03-01--dependabot[bot]
1	1	package.json
65	315	yarn.lock

--cd3839d9--2022-03-01--dependabot[bot]
1	1	package.json
8	42	yarn.lock

--6a8de3cf--2022-03-01--Nicolas Carlo
--18b69ca1--2022-03-01--Nicolas Carlo
--8acfa305--2022-03-01--Nicolas Carlo
--a6ea31d7--2022-03-01--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--b97a06ab--2022-03-01--dependabot[bot]
1	1	package.json
1	6	yarn.lock

--0823baee--2022-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--9513cb8d--2022-03-01--dependabot[bot]
2	2	package.json
345	398	yarn.lock

--31b3ef04--2022-03-01--Nicolas Carlo
--d8910622--2022-03-01--Nicolas Carlo
--19513372--2022-03-01--Nicolas Carlo
--5baa135c--2022-03-01--Nicolas Carlo
--6ec17c68--2022-03-01--dependabot[bot]
1	1	package.json
54	20	yarn.lock

--a51e627d--2022-03-01--dependabot[bot]
1	1	package.json
326	379	yarn.lock

--f02b7218--2022-03-01--dependabot[bot]
2	2	package.json
26	45	yarn.lock

--a6d0669e--2022-03-01--Nicolas Carlo
--04fc1993--2022-03-01--dependabot[bot]
1	1	package.json
22	41	yarn.lock

--a0135fc2--2022-03-01--dependabot[bot]
1	1	package.json
26	26	yarn.lock

--aef98528--2022-03-01--Nicolas Carlo
--d09c691e--2022-03-01--Nicolas Carlo
--1d0ce130--2022-03-01--dependabot[bot]
2	2	package.json
25	23	yarn.lock

--6c605025--2022-03-01--dependabot[bot]
2	2	package.json
25	23	yarn.lock

--de3ccd36--2022-03-01--Nicolas Carlo
--b6190cd6--2022-03-01--Nicolas Carlo
--4be0b81b--2022-03-01--Nicolas Carlo
--2698f197--2022-03-01--Nicolas Carlo
--be7af171--2022-03-01--Nicolas Carlo
--3b0459b6--2022-03-01--Nicolas Carlo
--8b5e0009--2022-03-01--dependabot[bot]
1	1	package.json
21	19	yarn.lock

--50be4ffd--2022-03-01--dependabot[bot]
1	1	package.json
89	46	yarn.lock

--52d01df6--2022-03-01--dependabot[bot]
1	1	package.json
59	39	yarn.lock

--d33958ca--2022-03-01--dependabot[bot]
1	1	package.json
19	19	yarn.lock

--452cde2c--2022-03-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--172afe75--2022-03-01--dependabot[bot]
1	1	package.json
36	36	yarn.lock

--838602bb--2022-02-19--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--dc5b5de1--2022-02-19--Nicolas Carlo
1	0	CHANGELOG.md
8	0	src/ast/domain.ts
11	6	src/ast/siblings.ts
26	1	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
16	7	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--75a0e94d--2022-02-19--Nicolas Carlo
--ac5197bc--2022-02-19--allcontributors[bot]
9	0	.all-contributorsrc

--99e541e2--2022-02-19--allcontributors[bot]
2	1	README.md

--776ea865--2022-02-19--Nicolas Carlo
4	0	CHANGELOG.md
1	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
6	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--8b875520--2022-02-14--Nicolas Carlo
--cc0b6c76--2022-02-14--Nicolas Carlo
1	6	src/ast/transformation.ts
2	2	src/editor/editor.ts
2	2	src/refactorings/split-multiple-declarations/index.ts

--a1401f74--2022-02-14--Nicolas Carlo
3	3	docs/adr/0012-export-symbols-at-declaration.md

--c27bbfad--2022-02-14--Nicolas Carlo
49	0	docs/adr/0012-export-symbols-at-declaration.md

--cb4f2095--2022-02-14--Nicolas Carlo
7	8	CONTRIBUTING.md
2	8	_templates/refactoring/new/refactoring.ejs.t
1	3	src/action-providers.ts
4	6	src/array.ts
17	31	src/ast/domain.ts
1	3	src/ast/export-default-workaround.ts
32	58	src/ast/identity.ts
17	29	src/ast/scope.ts
5	13	src/ast/selection.ts
5	13	src/ast/siblings.ts
2	4	src/ast/switch.ts
2	4	src/ast/template-literal.ts
14	29	src/ast/transformation.ts
4	4	src/commands.ts
1	3	src/editor/adapters/attempting-editor.ts
1	3	src/editor/adapters/create-vscode-editor.ts
1	3	src/editor/adapters/in-memory-editor.ts
1	3	src/editor/adapters/vscode-editor.ts
1	3	src/editor/adapters/vue-vscode-editor.ts
1	3	src/editor/editor-contract-test.ts
2	6	src/editor/editor.ts
3	5	src/editor/path.ts
1	3	src/editor/position.ts
1	3	src/editor/selection.ts
2	4	src/refactorings/add-numeric-separator/add-numeric-separator.ts
2	4	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts
2	5	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
2	4	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts
2	5	src/refactorings/convert-for-to-for-each/index.ts
2	4	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
2	2	src/refactorings/convert-if-else-to-switch/index.ts
2	4	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
2	2	src/refactorings/convert-if-else-to-ternary/index.ts
2	4	src/refactorings/convert-let-to-const/convert-let-to-const.ts
2	4	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
2	2	src/refactorings/convert-switch-to-if-else/index.ts
2	4	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
2	2	src/refactorings/convert-ternary-to-if-else/index.ts
2	4	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts
2	2	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
2	7	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
2	2	src/refactorings/convert-to-template-literal/index.ts
2	4	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts
2	4	src/refactorings/destructure-object/destructure-object.ts
2	4	src/refactorings/extract-class/extract-class.ts
3	5	src/refactorings/extract-generic-type/extract-generic-type.ts
2	4	src/refactorings/extract-interface/extract-interface.ts
2	2	src/refactorings/extract-interface/index.ts
1	3	src/refactorings/extract/extract-type/extract-type.ts
1	3	src/refactorings/extract/extract-variable/extract-variable.ts
2	4	src/refactorings/extract/extract-variable/occurrence.ts
1	3	src/refactorings/extract/extract-variable/parts.ts
3	9	src/refactorings/extract/extract-variable/variable-declaration-modification.ts
4	11	src/refactorings/extract/extract-variable/variable.ts
1	3	src/refactorings/extract/replacement-strategy.ts
2	4	src/refactorings/flip-if-else/flip-if-else.ts
2	2	src/refactorings/flip-if-else/index.ts
2	4	src/refactorings/flip-ternary/flip-ternary.ts
2	2	src/refactorings/flip-ternary/index.ts
1	3	src/refactorings/inline/find-exported-id-names.ts
1	3	src/refactorings/inline/inline-function/find-param-matching-id.ts
1	3	src/refactorings/inline/inline-function/inline-function.ts
5	13	src/refactorings/inline/inline-variable/find-inlinable-code.ts
1	3	src/refactorings/inline/inline-variable/inline-variable.ts
2	2	src/refactorings/invert-boolean-logic/index.ts
3	6	src/refactorings/invert-boolean-logic/invert-boolean-logic.test.ts
4	11	src/refactorings/invert-boolean-logic/invert-boolean-logic.ts
2	2	src/refactorings/lift-up-conditional/index.ts
2	4	src/refactorings/lift-up-conditional/lift-up-conditional.ts
2	2	src/refactorings/merge-if-statements/index.ts
3	3	src/refactorings/merge-if-statements/merge-if-statements.test.ts
2	4	src/refactorings/merge-if-statements/merge-if-statements.ts
2	2	src/refactorings/merge-with-previous-if-statement/index.ts
2	7	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
1	3	src/refactorings/move-statement-down/move-statement-down.ts
1	3	src/refactorings/move-statement-up/move-statement-up.ts
2	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts
1	3	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
2	2	src/refactorings/remove-dead-code/index.ts
5	4	src/refactorings/remove-dead-code/remove-dead-code.ts
2	2	src/refactorings/remove-redundant-else/index.ts
2	4	src/refactorings/remove-redundant-else/remove-redundant-else.ts
1	3	src/refactorings/rename-symbol/rename-symbol.ts
2	2	src/refactorings/replace-binary-with-assignment/index.ts
2	7	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
2	2	src/refactorings/simplify-ternary/index.ts
2	4	src/refactorings/simplify-ternary/simplify-ternary.ts
2	2	src/refactorings/split-declaration-and-initialization/index.ts
2	7	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
2	2	src/refactorings/split-if-statement/index.ts
2	4	src/refactorings/split-if-statement/split-if-statement.ts
2	2	src/refactorings/split-multiple-declarations/index.ts
2	7	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts
2	4	src/refactorings/toggle-braces/toggle-braces.ts
1	3	src/tests-helpers.ts
3	5	src/vscode-configuration.ts

--ba12efe8--2022-02-14--Nicolas Carlo
--8d19b2f9--2022-02-13--dependabot[bot]
3	3	yarn.lock

--18dd9769--2022-02-13--Nicolas Carlo
--a02679e5--2022-02-13--dependabot[bot]
3	3	yarn.lock

--7dd98512--2022-02-04--Nicolas Carlo
4	0	CHANGELOG.md
21	0	src/refactorings/move-statement-down/move-statement-down.test.ts
2	0	src/refactorings/move-statement-down/move-statement-down.ts
21	0	src/refactorings/move-statement-up/move-statement-up.test.ts
2	0	src/refactorings/move-statement-up/move-statement-up.ts

--71d3a0c9--2022-02-02--Nicolas Carlo
38	0	docs/adr/0011-use-babel-jest-instead-of-ts-jest.md

--19afb3fa--2022-02-02--Nicolas Carlo
1	1	package.json

--b8e94839--2022-02-02--Nicolas Carlo
4	3	src/ast/domain.ts
1	2	src/ast/identity.ts
8	18	src/ast/selection.ts
4	4	src/ast/transformation.ts
6	9	src/editor/editor.ts
2	4	src/editor/error-reason.ts
1	3	src/refactorings/extract/extract-variable/destructure-strategy.ts
2	2	src/refactorings/extract/replacement-strategy.ts
1	2	src/refactorings/inline/inline-variable/find-inlinable-code.ts
3	6	src/types.ts

--38c9f77a--2022-02-02--Nicolas Carlo
6	0	babel.config.js
1	9	jest.config.js
4	1	package.json
869	94	yarn.lock

--59f7b819--2022-02-01--Nicolas Carlo
--db865b03--2022-02-01--Nicolas Carlo
--eb3863b8--2022-02-01--Nicolas Carlo
--1c122fb0--2022-02-01--Nicolas Carlo
--f57ead92--2022-02-01--Nicolas Carlo
--34d13971--2022-02-01--dependabot[bot]
1	1	package.json
18	11	yarn.lock

--e37fc262--2022-02-01--Nicolas Carlo
--26fd6bc6--2022-02-01--Nicolas Carlo
--bf331cf4--2022-02-01--dependabot[bot]
1	1	package.json
24	48	yarn.lock

--64f887ee--2022-02-01--dependabot[bot]
1	1	package.json
30	50	yarn.lock

--d437e6c0--2022-02-01--Nicolas Carlo
--3f8bdb7c--2022-02-01--Nicolas Carlo
--c8135ea2--2022-02-01--Nicolas Carlo
--946217cf--2022-02-01--dependabot[bot]
1	1	package.json
1	6	yarn.lock

--0409b42a--2022-02-01--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--87a223a7--2022-02-01--Nicolas Carlo
--8280d716--2022-02-01--dependabot[bot]
22	23	yarn.lock

--72868f60--2022-02-01--Nicolas Carlo
--04d1121f--2022-02-01--dependabot[bot]
1	1	package.json
439	292	yarn.lock

--e055ef2c--2022-02-01--dependabot[bot]
1	1	package.json
38	38	yarn.lock

--0e66f731--2022-02-01--dependabot[bot]
1	1	package.json
35	76	yarn.lock

--93e70cc1--2022-02-01--dependabot[bot]
1	1	package.json
50	16	yarn.lock

--9f082f9b--2022-02-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--a143f34c--2022-02-01--dependabot[bot]
1	1	package.json
12	4	yarn.lock

--cf7678eb--2022-01-27--Nicolas Carlo
1	2	src/ast/domain.ts
1	1	src/ast/identity.ts
8	6	src/ast/selection.ts
3	4	src/ast/transformation.ts
1	4	src/editor/editor.ts
1	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
1	1	src/types.ts
2	1	tsconfig.json

--9068c531--2022-01-18--Nicolas Carlo
4	0	src/refactorings/move-statement-down/move-statement-down.ts
4	0	src/refactorings/move-statement-up/move-statement-up.ts

--cf18ebcb--2022-01-17--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--1a78ef82--2022-01-16--Nicolas Carlo
1	1	CHANGELOG.md

--edf58025--2022-01-16--Nicolas Carlo
28	0	CHANGELOG.md
27	0	src/refactorings/move-statement-down/move-statement-down.test.ts
8	1	src/refactorings/move-statement-down/move-statement-down.ts
27	0	src/refactorings/move-statement-up/move-statement-up.test.ts
8	1	src/refactorings/move-statement-up/move-statement-up.ts

--9296a530--2022-01-14--Nicolas Carlo
1	0	CHANGELOG.md
15	0	src/refactorings/move-statement-down/move-statement-down.test.ts
4	2	src/refactorings/move-statement-down/move-statement-down.ts
15	0	src/refactorings/move-statement-up/move-statement-up.test.ts
4	2	src/refactorings/move-statement-up/move-statement-up.ts

--45debf78--2022-01-16--Nicolas Carlo
--0902d9e5--2022-01-16--allcontributors[bot]
2	1	.all-contributorsrc

--8ae75e64--2022-01-16--allcontributors[bot]
1	1	README.md

--2cc12744--2022-01-14--Nicolas Carlo
--8f4d3526--2022-01-14--dependabot[bot]
3	3	yarn.lock

--dbc6b478--2022-01-14--Nicolas Carlo
--96dd0099--2022-01-14--allcontributors[bot]
9	0	.all-contributorsrc

--b791a0fb--2022-01-14--allcontributors[bot]
2	1	README.md

--844533d6--2022-01-14--Nicolas Carlo
1	0	CHANGELOG.md
22	0	src/refactorings/extract-interface/extract-interface.test.ts
1	1	src/refactorings/extract-interface/extract-interface.ts

--d3fa0c14--2022-01-13--Nicolas Carlo
--95ddca4b--2022-01-13--allcontributors[bot]
9	0	.all-contributorsrc

--f2a53910--2022-01-13--allcontributors[bot]
4	1	README.md

--0e258d17--2022-01-13--Nicolas Carlo
2	1	CHANGELOG.md
13	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
1	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts

--9da45333--2022-01-13--Nicolas Carlo
4	0	CHANGELOG.md
9	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
6	1	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts

--58469100--2022-01-12--Nicolas Carlo
12	2	README.md

--75d4fc2e--2022-01-09--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--bb06b6cb--2022-01-09--Nicolas Carlo
5	0	CHANGELOG.md

--709e9c77--2022-01-09--Nicolas Carlo
--8ba59e64--2022-01-09--Nicolas Carlo
--342dcc9a--2022-01-09--allcontributors[bot]
2	1	.all-contributorsrc

--cbb6771f--2022-01-09--allcontributors[bot]
1	1	README.md

--7e2fcdc2--2022-01-09--Nicolas Carlo
2	2	src/refactorings/rename-symbol/rename-symbol.ts

--f81cbb48--2022-01-09--Nicolas Carlo
48	48	yarn.lock

--a1b0574b--2022-01-09--Nicolas Carlo
1	1	src/type-checker/type-checker.ts

--211c10bf--2022-01-09--Nicolas Carlo
2	2	lint-staged.config.js

--0c9fb451--2022-01-09--Nicolas Carlo
8	8	src/playground/gilded-rose.ts
23	24	src/playground/trivia-kata.ts

--dbf38db7--2022-01-09--Nicolas Carlo
--4e96a4dd--2022-01-09--Nicolas Carlo
--dfa90fc9--2022-01-09--allcontributors[bot]
10	0	.all-contributorsrc

--36496757--2022-01-09--allcontributors[bot]
2	1	README.md

--bb6838a1--2022-01-09--Vitaly Turovsky
1	0	.gitignore
1	1	.vscodeignore

--bcef7dc3--2022-01-09--长风
1	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
2	2	src/refactorings/rename-symbol/rename-symbol.ts
2	1	src/type-checker/type-checker.ts

--6393679e--2022-01-09--长风
5	3	.eslintrc.js

--1243a104--2022-01-09--长风
--10d590e4--2022-01-07--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--66b10938--2022-01-07--Nicolas Carlo
1	0	CHANGELOG.md

--15f97a4c--2022-01-07--Nicolas Carlo
--0fb0dd0c--2022-01-07--Nicolas Carlo
1	1	.vscode/extensions.json

--e375374c--2022-01-07--Nicolas Carlo
4	0	CHANGELOG.md

--79db03f4--2022-01-07--Nicolas Carlo
--6dfb8e19--2022-01-07--Nicolas Carlo
1	1	package.json

--34669943--2022-01-07--Nicolas Carlo
0	1	package.json

--bd5f053d--2022-01-07--Nicolas Carlo
1	1	package.json

--6542cf42--2022-01-07--Nicolas Carlo
1	1	package.json
1	1	webpack.config.js

--92172e08--2022-01-07--Nicolas Carlo
2	2	.vscode/launch.json
4	1	package.json
38	34	webpack.config.js
41	2	yarn.lock

--0d4adc5c--2022-01-07--Nicolas Carlo
1	1	src/editor/path.ts

--eeda2316--2022-01-07--Nicolas Carlo
1	1	src/ast/scope.ts

--ddc417f6--2022-01-07--Nicolas Carlo
1	1	src/editor/path.ts

--34c23225--2022-01-07--Nicolas Carlo
6	0	package.json
306	8	yarn.lock

--7b0bfbb2--2022-01-07--Nicolas Carlo
1	0	webpack.config.js

--09b5d0d4--2022-01-07--Nicolas Carlo
24	4	webpack.config.js

--beb9d3fb--2021-12-23--Nicolas Carlo
12	0	webpack.config.js

--98b3a658--2021-12-23--Nicolas Carlo
2	2	package.json
4	0	webpack.config.js

--2a153a16--2021-12-23--Nicolas Carlo
1	1	src/editor/path.ts

--d18f34de--2021-12-10--Nicolas Carlo
13	0	.vscode/launch.json
4	1	.vscode/tasks.json

--582b4f7f--2021-12-10--Nicolas Carlo
1	0	.gitignore
1	0	package.json

--43ed32f8--2022-01-07--Nicolas Carlo
1	1	package.json
14	21	yarn.lock

--fc05f5f8--2022-01-07--j4k0xb
8	0	src/action-providers.ts

--2b0452b7--2022-01-01--Nicolas Carlo
1	1	package.json

--5257543e--2022-01-01--Nicolas Carlo
--9fe2a2b6--2022-01-01--Nicolas Carlo
--aec8d83d--2022-01-01--Nicolas Carlo
--d7c81658--2022-01-01--dependabot[bot]
1	1	package.json
67	59	yarn.lock

--0083c223--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--14723e8a--2022-01-01--dependabot[bot]
1	1	package.json
142	162	yarn.lock

--94ad19d6--2022-01-01--Nicolas Carlo
--74090c8b--2022-01-01--Nicolas Carlo
--42f63258--2022-01-01--Nicolas Carlo
--cb938e02--2022-01-01--Nicolas Carlo
--f5cd90a6--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--5b8503e6--2022-01-01--dependabot[bot]
2	2	package.json
9	9	yarn.lock

--3a6c4d1b--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--190289c1--2022-01-01--Nicolas Carlo
--e08b2377--2022-01-01--Nicolas Carlo
--62f8bf5d--2022-01-01--Nicolas Carlo
--8da76dd7--2022-01-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--53320c91--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--18ef6e14--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3e504ad3--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--cb9b23cd--2022-01-01--Nicolas Carlo
--874e059d--2022-01-01--Nicolas Carlo
--02c30de4--2022-01-01--Nicolas Carlo
--6e6c733b--2022-01-01--Nicolas Carlo
--053945d8--2022-01-01--Nicolas Carlo
--1888df71--2022-01-01--Nicolas Carlo
--cd52e36c--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3cce1fbd--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3f7b024f--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--2d6fd8fb--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--f68d0cf3--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--285720f4--2022-01-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ed6b6bd8--2021-12-20--长风
1	1	.husky/pre-commit
4	0	lint-staged.config.js
2	1	package.json
228	56	yarn.lock

--fc1493b4--2021-12-20--长风
2	2	src/refactorings/move-statement-down/move-statement-down.ts
1	1	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--65b5bbe4--2021-12-20--长风
1	0	src/ast/identity.ts
2	2	src/ast/scope.ts
3	2	src/ast/transformation.ts
1	1	src/editor/editor-contract-test.ts
1	1	src/editor/position.ts
8	8	src/playground/gilded-rose.ts
24	23	src/playground/trivia-kata.ts
1	2	src/refactorings/convert-let-to-const/convert-let-to-const.ts
1	1	src/refactorings/extract-generic-type/extract-generic-type.ts
4	5	src/refactorings/extract/extract-variable/extract-variable.ts
1	0	src/refactorings/extract/extract-variable/occurrence.ts
1	1	src/refactorings/inline/find-exported-id-names.ts
4	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
2	2	src/refactorings/move-statement-down/move-statement-down.ts
5	1	src/refactorings/remove-dead-code/remove-dead-code.ts
1	1	src/refactorings/rename-symbol/rename-symbol.ts
0	2	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts
1	0	src/test/custom-matchers.ts
1	2	src/type-checker/type-checker.ts

--8f289849--2021-12-20--长风
47	0	.eslintrc.js
5	0	package.json
336	15	yarn.lock

--5899c42f--2021-12-20--长风
0	1	.vscodeignore
0	1	package.json
0	12	tslint.json
5	41	yarn.lock

--af7c7e62--2021-12-20--长风
1	0	.npmrc

--3b7a041c--2021-12-10--Nicolas Carlo
--d4b7d8cc--2021-12-10--Fabien BERNARD
3	1	src/ast/transformation.ts
2	1	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts

--944b3e78--2021-12-10--Fabien BERNARD
0	5	src/ast/transformation.ts
1	1	src/refactorings/extract/extract-variable/occurrence.ts

--52c73a35--2021-12-02--Nicolas Carlo
2	1	CHANGELOG.md
1	0	src/ast/scope.ts
6	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
2	1	src/refactorings/extract/extract-variable/extract-variable.ts

--987d5a54--2021-12-02--Nicolas Carlo
4	0	CHANGELOG.md
21	5	src/refactorings/inline/inline-variable/find-inlinable-code.ts
22	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--32fe787b--2021-12-02--Nicolas Carlo
1	1	package.json

--8fe47d09--2021-12-02--Nicolas Carlo
--37eb78af--2021-12-02--Nicolas Carlo
--b343ddab--2021-12-01--dependabot[bot]
1	1	package.json
332	15	yarn.lock

--88921423--2021-12-01--Nicolas Carlo
--0f3493d0--2021-12-01--Nicolas Carlo
--3d3ae728--2021-12-01--Nicolas Carlo
--651c2897--2021-12-01--Nicolas Carlo
--8120451b--2021-12-01--dependabot[bot]
1	1	package.json
14	25	yarn.lock

--531c695e--2021-12-01--dependabot[bot]
1	1	package.json
4	15	yarn.lock

--fdf911cf--2021-12-01--dependabot[bot]
1	1	package.json
4	15	yarn.lock

--2153e16c--2021-12-01--dependabot[bot]
1	1	package.json
1	17	yarn.lock

--917c329f--2021-12-01--Nicolas Carlo
--e299782c--2021-12-01--Nicolas Carlo
--a046a1c4--2021-12-01--Nicolas Carlo
--086fae3e--2021-12-01--Nicolas Carlo
--9646e0b5--2021-12-01--dependabot[bot]
1	1	package.json
6	36	yarn.lock

--7564eb67--2021-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ded7d291--2021-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--5357a3b9--2021-12-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--0159a540--2021-12-01--dependabot[bot]
1	1	package.json
14	40	yarn.lock

--ffc5ffd2--2021-12-01--Nicolas Carlo
--def339b9--2021-12-01--Nicolas Carlo
--7a23cce3--2021-12-01--Nicolas Carlo
--b480b5ef--2021-12-01--Nicolas Carlo
--d9ef53a2--2021-12-01--dependabot[bot]
1	1	package.json
21	14	yarn.lock

--63a97045--2021-12-01--dependabot[bot]
1	1	package.json
394	368	yarn.lock

--57b4ba20--2021-12-01--dependabot[bot]
1	1	package.json
10	10	yarn.lock

--ec0c2a18--2021-12-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--3d7cc05a--2021-11-25--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--1a517a5c--2021-11-25--Nicolas Carlo
1	0	CHANGELOG.md

--981a3475--2021-11-25--Nicolas Carlo
9	0	src/playground/playground.tsx

--fd000de7--2021-11-25--Nicolas Carlo
32	0	src/refactorings/destructure-object/destructure-object.test.ts
10	5	src/refactorings/destructure-object/destructure-object.ts

--51f9424e--2021-11-25--Nicolas Carlo
26	0	src/refactorings/destructure-object/destructure-object.test.ts
12	0	src/refactorings/destructure-object/destructure-object.ts

--01967396--2021-11-25--Nicolas Carlo
11	0	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
15	2	src/refactorings/extract/extract-variable/occurrence.ts

--51cdfa1f--2021-11-24--Nicolas Carlo
1	0	CHANGELOG.md
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
21	6	src/refactorings/extract/extract-variable/occurrence.ts

--8c23650f--2021-11-24--Nicolas Carlo
3	2	src/ast/template-literal.ts
1	1	src/refactorings/extract/extract-variable/occurrence.ts

--e4579208--2021-11-24--Nicolas Carlo
--93188037--2021-11-24--Nicolas Carlo
4	0	CHANGELOG.md

--d98cada6--2021-11-24--Nicolas Carlo
14	0	REFACTORINGS.md
-	-	docs/demo/create-factory-for-constructor.gif

--49b3a441--2021-11-24--Nicolas Carlo
31	0	package.json
2	0	src/extension.ts

--50aa4a1a--2021-11-24--Nicolas Carlo
1	1	src/refactorings/create-factory-for-constructor/index.ts

--5523bde2--2021-11-24--Nicolas Carlo
36	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts
87	4	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts

--3d888a61--2021-11-24--Nicolas Carlo
26	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts
13	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts

--47d45d60--2021-11-24--Nicolas Carlo
20	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts
19	2	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts

--458379a0--2021-11-24--Nicolas Carlo
15	0	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts
7	1	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts

--e84f9a4c--2021-11-24--Nicolas Carlo
0	3	.vscode/settings.json

--38345cda--2021-11-24--Nicolas Carlo
4	0	src/editor/error-reason.ts
30	0	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts
38	0	src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts
20	0	src/refactorings/create-factory-for-constructor/index.ts

--2d1132d0--2021-11-11--Nicolas Carlo
4	0	CHANGELOG.md
23	6	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
6	0	src/refactorings/extract/extract-variable/extract-variable.non-extractable.test.ts
10	0	src/refactorings/extract/extract-variable/extract-variable.ts

--84b432bb--2021-11-11--Nicolas Carlo
4	0	CHANGELOG.md

--462a1021--2021-11-11--Nicolas Carlo
--92f93ac7--2021-11-07--Tobias Hann
13	0	src/refactorings/extract-interface/extract-interface.test.ts
20	2	src/refactorings/extract-interface/extract-interface.ts

--6e8096c6--2021-11-04--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--f7fae3ae--2021-11-04--Nicolas Carlo
1	1	package.json

--42d6ea90--2021-11-04--Nicolas Carlo
1	1	CHANGELOG.md

--969b989d--2021-11-04--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--0516df88--2021-11-04--Nicolas Carlo
5	2	.vscode/launch.json
432	0	src/playground/Get.tsx
407	0	src/playground/Map.vue
75	0	src/playground/gilded-rose.js
77	0	src/playground/gilded-rose.ts
182	0	src/playground/playground.js
0	0	src/playground/playground.jsx
0	0	src/playground/playground.ts
0	0	src/playground/playground.tsx
0	0	src/playground/playground.vue
182	0	src/playground/trivia-kata.js
172	0	src/playground/trivia-kata.ts
7	1	tsconfig.json

--d819f956--2021-11-03--Nicolas Carlo
1	0	CHANGELOG.md
18	0	src/refactorings/extract/extract-variable/extract-variable.basic-extraction.test.ts
4	1	src/refactorings/extract/extract-variable/occurrence.ts

--a6ebca44--2021-11-03--Nicolas Carlo
1	0	CHANGELOG.md
22	0	src/refactorings/extract-interface/extract-interface.test.ts
10	1	src/refactorings/extract-interface/extract-interface.ts

--29d8219d--2021-11-01--Nicolas Carlo
--24c7984b--2021-11-01--Nicolas Carlo
--2dcdd340--2021-11-01--dependabot[bot]
1	1	package.json
25	31	yarn.lock

--7a7411ba--2021-11-01--dependabot[bot]
1	1	package.json
340	330	yarn.lock

--c665b7db--2021-11-01--Nicolas Carlo
--e054e838--2021-11-01--Nicolas Carlo
--359ebedd--2021-11-01--Nicolas Carlo
--4fcf101d--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ce541c13--2021-11-01--dependabot[bot]
2	2	package.json
6	11	yarn.lock

--093178d6--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--f26c8a22--2021-11-01--Nicolas Carlo
--44d6fd07--2021-11-01--Nicolas Carlo
--a11c6ced--2021-11-01--Nicolas Carlo
--20ae7de1--2021-11-01--Nicolas Carlo
--3e629cf6--2021-11-01--Nicolas Carlo
--f5ad580b--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--bf3510d1--2021-11-01--dependabot[bot]
2	2	package.json
21	29	yarn.lock

--ce24d4f7--2021-11-01--dependabot[bot]
2	2	package.json
16	24	yarn.lock

--51ba42e9--2021-11-01--dependabot[bot]
2	2	package.json
16	24	yarn.lock

--959e7211--2021-11-01--Nicolas Carlo
--a7769aa4--2021-11-01--Nicolas Carlo
--95ad7932--2021-11-01--Nicolas Carlo
--822721dc--2021-11-01--Nicolas Carlo
--2cf4eb11--2021-11-01--dependabot[bot]
1	1	package.json
12	20	yarn.lock

--b15709e3--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--ccf61547--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--02ca976b--2021-11-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--7c5b3d0b--2021-11-01--dependabot[bot]
1	1	package.json
71	52	yarn.lock

--bfbce69d--2021-10-28--Nicolas Carlo
--58e76031--2021-10-28--Nicolas Carlo
1	0	CHANGELOG.md
10	8	REFACTORINGS.md
-	-	docs/demo/invert-boolean-logic-partial.gif
-	-	docs/demo/invert-boolean-logic.gif
-	-	docs/demo/negate-expression-partial.gif
-	-	docs/demo/negate-expression.gif
9	9	package.json
4	4	src/editor/error-reason.ts
2	2	src/extension.ts
1	1	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/flip-ternary/flip-ternary.ts
27	0	src/refactorings/invert-boolean-logic/index.ts
257	0	src/refactorings/invert-boolean-logic/invert-boolean-logic.test.ts
256	0	src/refactorings/invert-boolean-logic/invert-boolean-logic.ts
0	27	src/refactorings/negate-expression/index.ts
0	254	src/refactorings/negate-expression/negate-expression.test.ts
0	256	src/refactorings/negate-expression/negate-expression.ts

--f1682dcd--2021-10-28--allcontributors[bot]
9	0	.all-contributorsrc

--869994a2--2021-10-28--allcontributors[bot]
2	1	README.md

--817ac2c1--2021-10-22--Nicolas Carlo
--69851703--2021-10-22--Nicolas Carlo
1	0	CHANGELOG.md

--ad0e54f3--2021-10-22--Nicolas Carlo
15	0	src/refactorings/move-statement-down/move-statement-down.test.ts
34	7	src/refactorings/move-statement-down/move-statement-down.ts

--127a343c--2021-10-22--Nicolas Carlo
42	65	src/refactorings/move-statement-down/move-statement-down.test.ts

--dd0345d4--2021-10-22--Nicolas Carlo
14	0	src/refactorings/move-statement-up/move-statement-up.test.ts

--5db1c1ee--2021-10-21--Nicolas Carlo
15	0	src/refactorings/move-statement-up/move-statement-up.test.ts
28	6	src/refactorings/move-statement-up/move-statement-up.ts

--c7f358ba--2021-10-21--Nicolas Carlo
46	67	src/refactorings/move-statement-up/move-statement-up.test.ts

--7282474e--2021-10-21--Nicolas Carlo
0	2	src/refactorings/move-statement-up/move-statement-up.test.ts

--7598165c--2021-10-17--Nicolas Carlo
4	0	CHANGELOG.md
13	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
9	9	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--6e8efbba--2021-10-17--Nicolas Carlo
4	0	CHANGELOG.md
18	0	src/refactorings/extract/extract-type/extract-type.test.ts
1	0	src/refactorings/extract/extract-type/extract-type.ts

--a9299958--2021-10-14--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--af2ac04a--2021-10-14--Nicolas Carlo
--3cf81850--2021-10-14--Nicolas Carlo
1	0	CHANGELOG.md

--87dbd84f--2021-10-14--Nicolas Carlo
27	30	src/refactorings/extract/extract-variable/extract-variable.extractable-jsx.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
35	5	src/refactorings/extract/extract-variable/occurrence.ts

--0edcb605--2021-10-14--Nicolas Carlo
4	3	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
19	4	src/refactorings/extract/extract-variable/occurrence.ts

--a5869841--2021-10-14--Nicolas Carlo
11	10	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
5	10	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
30	0	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
4	14	src/refactorings/extract/extract-variable/occurrence.ts

--1129a385--2021-10-14--Nicolas Carlo
1	1	package.json

--7038bd0a--2021-10-14--Nicolas Carlo
4	0	CHANGELOG.md
11	0	src/refactorings/extract/extract-type/extract-type.test.ts
7	0	src/refactorings/extract/extract-type/extract-type.ts

--61aa8efa--2021-10-07--Nicolas Carlo
4	0	CHANGELOG.md

--bda33f20--2021-10-07--Nicolas Carlo
12	0	REFACTORINGS.md
-	-	docs/demo/convert-for-each-to-for-of.gif

--5436b4de--2021-10-07--Nicolas Carlo
1	1	REFACTORINGS.md

--dd96d965--2021-10-07--Nicolas Carlo
31	0	package.json

--f39f89a9--2021-10-07--Nicolas Carlo
5	5	REFACTORINGS.md
-	-	docs/demo/convert-for-to-for-each.gif
-	-	docs/demo/convert-for-to-foreach.gif
8	8	package.json
2	2	src/extension.ts
1	1	src/refactorings/convert-for-each-to-for-of/index.ts
361	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts
271	0	src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts
21	0	src/refactorings/convert-for-to-for-each/index.ts
0	361	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
0	271	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
0	21	src/refactorings/convert-for-to-foreach/index.ts

--6898333f--2021-10-05--Nicolas Carlo
1	1	REFACTORINGS.md

--1fcb5dd8--2021-10-06--Nicolas Carlo
--a7093c22--2021-10-06--allcontributors[bot]
10	0	.all-contributorsrc

--10d4147c--2021-10-06--allcontributors[bot]
2	1	README.md

--1f9eccea--2021-10-06--Nicolas Carlo
--f036e965--2021-10-06--Ian Obermiller
1	0	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts

--6265bae1--2021-10-06--Ian Obermiller
28	0	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.test.ts
54	22	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts

--622db0d7--2021-10-06--Ian Obermiller
11	9	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts

--70bdae47--2021-10-05--Ian Obermiller
4	0	src/editor/error-reason.ts
2	0	src/extension.ts
170	0	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.test.ts
86	0	src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts
19	0	src/refactorings/convert-for-each-to-for-of/index.ts

--fbdcb1b4--2021-10-05--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--644f0937--2021-10-05--Nicolas Carlo
8	0	CHANGELOG.md
1	1	src/extension.ts

--74316a57--2021-10-05--Nicolas Carlo
7	1	.vscode/settings.json

--795c8719--2021-10-04--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--8d5b9ae9--2021-10-04--Nicolas Carlo
1	1	package.json

--d12a60d0--2021-10-04--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--8e56f6e9--2021-10-02--Nicolas Carlo
--2cc1fe6d--2021-10-02--Nicolas Carlo
--72e5a872--2021-10-01--Nicolas Carlo
1	1	package.json
4	4	yarn.lock

--d30e3fae--2021-10-01--Nicolas Carlo
7	0	CHANGELOG.md
16	0	REFACTORINGS.md
-	-	docs/demo/destructure-object.gif

--0db26756--2021-10-01--Nicolas Carlo
48	2	src/refactorings/destructure-object/destructure-object.test.ts
10	1	src/refactorings/destructure-object/destructure-object.ts

--8f5e41e2--2021-09-30--Nicolas Carlo
31	0	package.json
2	0	src/extension.ts

--da0ffcd0--2021-09-30--Nicolas Carlo
22	1	src/refactorings/destructure-object/destructure-object.test.ts

--cd22b74b--2021-09-30--Nicolas Carlo
30	32	src/refactorings/destructure-object/destructure-object.ts

--bacd46d0--2021-09-30--Nicolas Carlo
3	1	src/action-providers.ts
3	1	src/types.ts

--64ec3f58--2021-09-30--Nicolas Carlo
8	10	src/action-providers.ts

--7dd3a36f--2021-09-30--Nicolas Carlo
0	7	src/refactorings/destructure-object/adapters/console-logger.ts
0	5	src/refactorings/destructure-object/adapters/noop-logger.ts
1	2	src/refactorings/destructure-object/destructure-object.ts
0	3	src/refactorings/destructure-object/logger.ts
0	55	src/refactorings/destructure-object/ts-position.test.ts
0	32	src/refactorings/destructure-object/ts-position.ts
0	197	src/refactorings/destructure-object/type-checker.test.ts
0	161	src/refactorings/destructure-object/type-checker.ts
7	0	src/type-checker/adapters/console-logger.ts
5	0	src/type-checker/adapters/noop-logger.ts
4	0	src/type-checker/index.ts
3	0	src/type-checker/logger.ts
55	0	src/type-checker/ts-position.test.ts
32	0	src/type-checker/ts-position.ts
197	0	src/type-checker/type-checker.test.ts
161	0	src/type-checker/type-checker.ts

--a3d0ba67--2021-09-30--Nicolas Carlo
24	2	src/refactorings/destructure-object/destructure-object.test.ts
12	0	src/refactorings/destructure-object/destructure-object.ts

--01146e7d--2021-09-30--Nicolas Carlo
6	4	src/refactorings/destructure-object/ts-position.test.ts

--e93d997d--2021-09-30--Nicolas Carlo
7	0	src/refactorings/destructure-object/adapters/console-logger.ts
5	0	src/refactorings/destructure-object/adapters/noop-logger.ts
2	1	src/refactorings/destructure-object/destructure-object.ts
3	0	src/refactorings/destructure-object/logger.ts
7	3	src/refactorings/destructure-object/type-checker.ts

--746d7071--2021-09-30--Nicolas Carlo
8	3	src/refactorings/destructure-object/destructure-object.test.ts
29	3	src/refactorings/destructure-object/type-checker.test.ts
8	2	src/refactorings/destructure-object/type-checker.ts

--30cc1f8f--2021-09-23--Nicolas Carlo
20	0	src/refactorings/destructure-object/destructure-object.test.ts
7	0	src/refactorings/destructure-object/destructure-object.ts

--b19d82d3--2021-09-23--Nicolas Carlo
30	2	src/refactorings/destructure-object/destructure-object.test.ts

--da43e923--2021-09-16--Nicolas Carlo
0	1	src/refactorings/destructure-object/destructure-object.test.ts
13	13	src/refactorings/destructure-object/destructure-object.ts
53	0	src/refactorings/destructure-object/ts-position.test.ts
32	0	src/refactorings/destructure-object/ts-position.ts
171	0	src/refactorings/destructure-object/type-checker.test.ts
151	0	src/refactorings/destructure-object/type-checker.ts

--ea77ae3b--2021-10-01--Nicolas Carlo
1	0	package.json
7	0	yarn.lock

--3fe35212--2021-09-09--Nicolas Carlo
26	1	src/refactorings/destructure-object/destructure-object.test.ts
20	4	src/refactorings/destructure-object/destructure-object.ts

--8bf085da--2021-09-09--Nicolas Carlo
4	0	src/editor/error-reason.ts
33	0	src/refactorings/destructure-object/destructure-object.test.ts
34	0	src/refactorings/destructure-object/destructure-object.ts
17	0	src/refactorings/destructure-object/index.ts

--4672723f--2021-10-01--dependabot[bot]
1	1	package.json
31	4	yarn.lock

--af8d4c46--2021-10-01--Nicolas Carlo
--90f9e4e7--2021-10-01--Nicolas Carlo
--2cd2e5a8--2021-10-01--dependabot[bot]
1	1	package.json
16	42	yarn.lock

--2062cde0--2021-10-01--dependabot[bot]
2	2	package.json
4	4	yarn.lock

--990d75d2--2021-10-01--Nicolas Carlo
--0ced264b--2021-10-01--Nicolas Carlo
--b4ca1eac--2021-10-01--Nicolas Carlo
--426abb67--2021-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--021d4e77--2021-10-01--dependabot[bot]
1	1	package.json
4	9	yarn.lock

--aa00cb69--2021-10-01--Nicolas Carlo
--ae149eba--2021-10-01--dependabot[bot]
1	1	package.json
374	325	yarn.lock

--64b12a64--2021-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--850b6ce9--2021-10-01--Nicolas Carlo
--712aadde--2021-10-01--Nicolas Carlo
--51b43696--2021-10-01--Nicolas Carlo
--1efdece9--2021-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b2a48c44--2021-10-01--dependabot[bot]
3	3	package.json
23	19	yarn.lock

--a957a066--2021-10-01--Nicolas Carlo
--05899721--2021-10-01--Nicolas Carlo
--fd4fd2ed--2021-10-01--Nicolas Carlo
--fa572278--2021-10-01--Nicolas Carlo
--e78b5034--2021-10-01--dependabot[bot]
1	1	package.json
13	1	yarn.lock

--02c42795--2021-10-01--dependabot[bot]
1	1	package.json
6	14	yarn.lock

--085104e7--2021-10-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--28ba3d0b--2021-10-01--dependabot[bot]
1	1	package.json
11	46	yarn.lock

--13149aad--2021-10-01--dependabot[bot]
1	1	package.json
8	147	yarn.lock

--685298e9--2021-09-30--Nicolas Carlo
--6f29e134--2021-09-30--allcontributors[bot]
9	0	.all-contributorsrc

--b3b9ae9d--2021-09-30--allcontributors[bot]
2	1	README.md

--a430ad00--2021-09-30--Nicolas Carlo
4	0	CHANGELOG.md
12	0	src/refactorings/flip-if-else/flip-if-else.test.ts
7	1	src/refactorings/flip-if-else/flip-if-else.ts

--a527baf0--2021-09-27--Nicolas Carlo
1	0	REFACTORINGS.md

--6edac6a9--2021-09-27--Nicolas Carlo
1	0	CHANGELOG.md

--0b970709--2021-09-27--Nicolas Carlo
--7a5684f6--2021-09-18--Christina Braun
18	8	src/action-providers.ts
4	0	src/refactorings/toggle-braces/index.ts
104	0	src/refactorings/toggle-braces/toggle-braces.test.ts
51	0	src/refactorings/toggle-braces/toggle-braces.ts

--64a6dbe5--2021-09-23--Nicolas Carlo
1	0	CHANGELOG.md
18	0	src/refactorings/extract/extract-type/extract-type.test.ts
30	0	src/refactorings/extract/extract-type/extract-type.ts

--486a5d15--2021-09-23--Nicolas Carlo
4	0	CHANGELOG.md
6	0	src/refactorings/extract/extract-type/extract-type.test.ts
14	1	src/refactorings/extract/extract-type/extract-type.ts

--b8b83780--2021-09-18--Nicolas Carlo
3	3	src/refactorings/extract/extract-type/extract-type.test.ts

--069c53d7--2021-09-21--Nicolas Carlo
--9263a21d--2021-09-21--Nicolas Carlo
--f10a05af--2021-09-21--dependabot[bot]
3	3	yarn.lock

--8995902e--2021-09-21--dependabot[bot]
3	3	yarn.lock

--d74ed8ae--2021-09-16--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--cd9ce534--2021-09-16--Nicolas Carlo
1	1	tsconfig.json

--fa91d26d--2021-09-16--Nicolas Carlo
4	0	CHANGELOG.md
17	3	src/refactorings/extract/extract-type/extract-type.test.ts
58	21	src/refactorings/extract/extract-type/extract-type.ts

--21a38d62--2021-09-10--Nicolas Carlo
--8b6e576e--2021-09-10--Nicolas Carlo
0	12	src/refactorings/merge-if-statements/merge-if-statements.ts

--cc19eacb--2021-09-10--Nicolas Carlo
1	0	CHANGELOG.md
22	1	src/refactorings/merge-if-statements/merge-if-statements.test.ts
18	1	src/refactorings/merge-if-statements/merge-if-statements.ts

--894cc29c--2021-09-10--Nicolas Carlo
1	1	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--a3a9c8d8--2021-09-10--Nicolas Carlo
17	41	src/refactorings/merge-if-statements/merge-if-statements.ts

--98e49c07--2021-09-10--Nicolas Carlo
1	1	src/refactorings/merge-if-statements/merge-if-statements.ts

--6bc74916--2021-09-10--Nicolas Carlo
10	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--a6b70699--2021-09-10--Nicolas Carlo
34	11	src/refactorings/merge-if-statements/merge-if-statements.ts

--d35a676d--2021-09-09--Nicolas Carlo
30	1	src/refactorings/merge-if-statements/merge-if-statements.ts

--154c6e7b--2021-09-09--Nicolas Carlo
14	17	src/refactorings/merge-if-statements/merge-if-statements.ts

--8ffa33dd--2021-09-08--Nicolas Carlo
4	0	CHANGELOG.md

--38e9fc76--2021-09-08--Nicolas Carlo
--3f2352b9--2021-09-08--allcontributors[bot]
2	1	.all-contributorsrc

--6d4a9173--2021-09-08--allcontributors[bot]
1	1	README.md

--252dcf64--2021-09-08--Nicolas Carlo
--4de5efb2--2021-09-08--sumbat15
2	1	src/editor/path.ts

--0817b139--2021-09-06--Nicolas Carlo
--371ff16e--2021-09-06--Nicolas Carlo
4	0	CHANGELOG.md
12	0	REFACTORINGS.md
-	-	docs/demo/add-numeric-separator.gif
31	0	package.json
4	0	src/editor/error-reason.ts
2	0	src/extension.ts
62	0	src/refactorings/add-numeric-separator/add-numeric-separator.test.ts
54	0	src/refactorings/add-numeric-separator/add-numeric-separator.ts
17	0	src/refactorings/add-numeric-separator/index.ts

--c54dcfbd--2021-09-04--Nicolas Carlo
4	4	CHANGELOG.md

--28d4fc62--2021-09-04--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--bb7970eb--2021-09-04--Nicolas Carlo
1	0	CHANGELOG.md
4	0	src/editor/path.test.ts
4	8	src/editor/path.ts

--d1850cdf--2021-09-04--Nicolas Carlo
1	3	src/editor/error-reason.ts

--9a35e03b--2021-09-04--Nicolas Carlo
5	0	CHANGELOG.md
2	0	REFACTORINGS.md
6	0	src/editor/error-reason.ts
6	2	src/refactorings/rename-symbol/rename-symbol.ts

--1b275bc2--2021-09-04--Nicolas Carlo
--bac29401--2021-09-04--allcontributors[bot]
2	1	.all-contributorsrc

--c862e356--2021-09-04--allcontributors[bot]
1	1	README.md

--7888967e--2021-09-04--Nicolas Carlo
4	2	src/refactorings/rename-symbol/rename-symbol.test.ts

--c3c9d2e0--2021-09-04--Nicolas Carlo
4	0	CHANGELOG.md
14	0	src/refactorings/rename-symbol/rename-symbol.test.ts
22	12	src/refactorings/rename-symbol/rename-symbol.ts

--ba00213e--2021-09-04--Nicolas Carlo
2	2	package.json
2	2	src/editor/adapters/attempting-editor.test.ts
756	981	yarn.lock

--9ad21e96--2021-09-04--Nicolas Carlo
--5305349d--2021-09-04--Nicolas Carlo
--bb61e413--2021-09-02--Nicolas Carlo
--2cb8f011--2021-09-02--allcontributors[bot]
9	0	.all-contributorsrc

--4399c114--2021-09-02--allcontributors[bot]
2	1	README.md

--df862b86--2021-09-02--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--2aec4c7f--2021-09-02--Nicolas Carlo
1	1	CHANGELOG.md

--c0aef90d--2021-09-02--Nicolas Carlo
1	1	src/ast/identity.ts
17	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--a9fb9e2b--2021-09-02--Nicolas Carlo
1	0	CHANGELOG.md
28	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
42	8	src/refactorings/merge-if-statements/merge-if-statements.ts

--7424651d--2021-09-02--Nicolas Carlo
1	0	CHANGELOG.md
15	0	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
3	5	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--64c92ea7--2021-09-02--Nicolas Carlo
4	0	CHANGELOG.md
17	4	src/refactorings/extract/extract-type/extract-type.test.ts
16	4	src/refactorings/extract/extract-type/extract-type.ts

--65eead7d--2021-09-02--Nicolas Carlo
4	0	CHANGELOG.md
1	1	src/editor/path.ts

--0a753f5d--2021-09-02--Nicolas Carlo
8	3	src/editor/path.ts

--2fb3f3af--2021-09-02--Nicolas Carlo
5	1	src/editor/path.test.ts
1	1	src/editor/path.ts

--a8d3bfec--2021-09-01--dependabot[bot]
1	1	package.json
61	8	yarn.lock

--a2119485--2021-09-01--Nicolas Carlo
--522e6cac--2021-09-01--Nicolas Carlo
--d7dcb856--2021-09-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--841954bc--2021-09-01--dependabot[bot]
1	1	package.json
30	18	yarn.lock

--830ac2f9--2021-09-01--Nicolas Carlo
--947b05cd--2021-09-01--Nicolas Carlo
--43d9fd74--2021-09-01--Nicolas Carlo
--2ea84f55--2021-09-01--Nicolas Carlo
--7662d26e--2021-09-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--86dff555--2021-09-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e07875cb--2021-09-01--dependabot[bot]
1	1	package.json
12	12	yarn.lock

--f89b4425--2021-09-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--72fd5e67--2021-09-01--dependabot[bot]
1	1	package.json
2	17	yarn.lock

--7f619b6f--2021-08-28--Nicolas Carlo
1	1	package.json
3	2	src/ast/transformation.ts
6	4	src/commands.ts
3	1	src/refactorings/extract-class/extract-class-command.ts
4	4	yarn.lock

--19f2bb41--2021-08-28--Nicolas Carlo
2	2	package.json
204	684	yarn.lock

--0adb87d4--2021-08-28--Nicolas Carlo
1	1	package.json
393	29	yarn.lock

--c87b537a--2021-08-28--Nicolas Carlo
--03b9e7e9--2021-08-28--Nicolas Carlo
4	10	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--49d870b6--2021-08-28--Nicolas Carlo
14	10	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--b4391e2f--2021-08-28--Nicolas Carlo
18	16	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--de4f8571--2021-08-28--Nicolas Carlo
22	4	src/ast/transformation.ts
2	2	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
9	5	src/refactorings/inline/inline-variable/find-inlinable-code.ts
4	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--0e7e8a4a--2021-08-26--Nicolas Carlo
6	3	src/ast/identity.ts
2	1	src/ast/scope.test.ts
5	4	src/ast/scope.ts
1	1	src/ast/selection.ts
1	0	src/ast/switch.ts
1	1	src/ast/transformation.ts
8	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
4	2	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
6	3	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
9	11	src/refactorings/extract-generic-type/extract-generic-type.ts
1	0	src/refactorings/extract/extract-variable/extract-variable.ts
4	0	src/refactorings/extract/extract-variable/occurrence.ts
6	3	src/refactorings/extract/extract-variable/variable.ts
5	2	src/refactorings/inline/inline-function/inline-function.ts
8	3	src/refactorings/inline/inline-variable/find-inlinable-code.ts
10	7	src/refactorings/move-to-existing-file/move-to-existing-file.ts
1	1	src/refactorings/negate-expression/negate-expression.ts
2	0	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
5	3	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
1	1	src/refactorings/toggle-braces/toggle-braces.ts

--d8a9cfac--2021-08-26--Nicolas Carlo
6	3	package.json
77	89	yarn.lock

--820c9ede--2021-08-26--Nicolas Carlo
--98ae01a5--2021-08-26--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--483f9c5a--2021-08-26--Nicolas Carlo
--16cf17b6--2021-08-26--Nicolas Carlo
1	1	package.json

--06ea23e3--2021-08-26--Nicolas Carlo
--65542370--2021-08-24--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--6c6da416--2021-08-23--dependabot[bot]
1	1	package.json
172	194	yarn.lock

--b91df494--2021-08-23--Nicolas Carlo
--7e5d88a4--2021-08-21--Nicolas Carlo
6	1	REFACTORINGS.md
-	-	docs/demo/merge-if-statements-with-sibling.gif

--2c6228dc--2021-08-21--Nicolas Carlo
25	0	CHANGELOG.md

--68ad73ee--2021-08-21--Nicolas Carlo
19	1	src/refactorings/merge-if-statements/merge-if-statements.test.ts
53	17	src/refactorings/merge-if-statements/merge-if-statements.ts

--646d2289--2021-08-21--Nicolas Carlo
16	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
44	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--50f9ac88--2021-08-21--Nicolas Carlo
5	0	src/ast/identity.ts

--78694f71--2021-08-21--Nicolas Carlo
5	0	src/ast/siblings.ts

--3dcb7e55--2021-08-21--Nicolas Carlo
5	3	src/refactorings/merge-if-statements/merge-if-statements.ts

--e44c1065--2021-08-21--Nicolas Carlo
7	5	src/refactorings/merge-if-statements/merge-if-statements.ts

--0084293e--2021-08-21--Nicolas Carlo
4	5	src/refactorings/merge-if-statements/merge-if-statements.ts

--aed27183--2021-08-21--Nicolas Carlo
0	9	src/refactorings/merge-if-statements/merge-if-statements.ts

--de3690d4--2021-08-21--Nicolas Carlo
4	10	src/refactorings/merge-if-statements/merge-if-statements.ts

--f9671c5b--2021-08-21--Nicolas Carlo
8	6	src/refactorings/merge-if-statements/merge-if-statements.ts

--7cf7f7e6--2021-08-21--Nicolas Carlo
10	0	src/ast/identity.ts

--28ba5f38--2021-08-21--Nicolas Carlo
2	2	src/refactorings/merge-if-statements/merge-if-statements.ts

--ed62cdc9--2021-08-21--Nicolas Carlo
3	6	src/refactorings/merge-if-statements/merge-if-statements.ts

--08b93faf--2021-08-21--Nicolas Carlo
4	7	src/refactorings/merge-if-statements/merge-if-statements.ts

--4272f3df--2021-08-21--Nicolas Carlo
0	14	src/refactorings/merge-if-statements/merge-if-statements.ts

--4c64beaf--2021-08-21--Nicolas Carlo
6	10	src/refactorings/merge-if-statements/merge-if-statements.ts

--c7664168--2021-08-21--Nicolas Carlo
6	3	src/refactorings/merge-if-statements/merge-if-statements.ts

--e7fbdaab--2021-08-21--Nicolas Carlo
4	2	src/refactorings/merge-if-statements/merge-if-statements.ts

--0cd9f5b9--2021-08-21--Nicolas Carlo
6	2	src/refactorings/merge-if-statements/merge-if-statements.ts

--e1122de6--2021-08-21--Nicolas Carlo
40	18	src/refactorings/merge-if-statements/merge-if-statements.ts

--3947f947--2021-08-20--Nicolas Carlo
--05da3fab--2021-08-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--36d253fd--2021-08-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--e23f174c--2021-08-20--Nicolas Carlo
--ed4cd4cc--2021-08-20--Nicolas Carlo
--6cc1ff4c--2021-08-20--Nicolas Carlo
--a653d62f--2021-08-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--033b97c4--2021-08-20--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/thank_you.md
6	6	CHANGELOG.md
9	9	CONTRIBUTING.md
9	9	README.md
41	41	REFACTORINGS.md
2	2	docs/adr/0004-use-recast-for-ast-manipulation.md
1	1	docs/adr/0008-don-t-propose-quick-fix-for-react-convert-to-pure-component.md

--eb2c9c83--2021-08-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--352a4b1d--2021-08-20--dependabot[bot]
1	1	package.json
42	27	yarn.lock

--38ffbe25--2021-08-20--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--68d306d5--2021-08-12--Nicolas Carlo
4	0	CHANGELOG.md
13	7	src/refactorings/inline/inline-variable/find-inlinable-code.ts
8	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--1170f2a1--2021-08-12--Nicolas Carlo
25	5	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--fb8e4f7a--2021-08-12--Nicolas Carlo
4	0	CHANGELOG.md
30	5	src/refactorings/inline/inline-variable/find-inlinable-code.ts
7	1	src/refactorings/inline/inline-variable/inline-variable.test.ts

--87cec001--2021-08-11--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--c233bfb2--2021-08-11--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml

--9ea3d25e--2021-08-11--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--5ac1b36f--2021-08-11--Nicolas Carlo
1	1	.travis.yml

--321e3330--2021-08-11--Nicolas Carlo
4	0	CHANGELOG.md

--974be3f2--2021-08-11--Nicolas Carlo
--69d827ab--2021-08-11--Nicolas Carlo
0	1	.husky/.gitignore

--7f0ac38e--2021-08-11--Nicolas Carlo
1	1	package.json

--a7271700--2021-08-11--dependabot[bot]
1	1	package.json
36	2	yarn.lock

--52c21801--2021-08-11--Nicolas Carlo
--29f22d5d--2021-08-11--Nicolas Carlo
--01b96a2f--2021-08-11--Nicolas Carlo
1	1	package.json
1	1	src/test/run-contract-tests.ts
10	10	yarn.lock

--887a3b46--2021-08-11--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--0d94113e--2021-08-11--Nicolas Carlo
1	1	package.json

--d931ce83--2021-08-11--Nicolas Carlo
--5843e7a6--2021-08-11--Nicolas Carlo
--ed478a82--2021-08-11--Nicolas Carlo
--73e4c8de--2021-08-11--Nicolas Carlo
--eeb4f612--2021-08-11--Nicolas Carlo
4	1	src/editor/adapters/vue-vscode-editor.ts

--4f4cb193--2021-08-11--Nicolas Carlo
20	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
40	0	src/refactorings/turn-into-getter/turn-into-getter.ts

--17516c08--2021-08-11--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--d357d585--2021-08-11--Nicolas Carlo
--ea339fad--2021-08-11--Nicolas Carlo
--75eaefa8--2021-08-11--dependabot[bot]
3	3	yarn.lock

--85d2c250--2021-08-03--Nicolas Carlo
31	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
1	1	src/refactorings/turn-into-getter/turn-into-getter.ts

--6efe6dae--2021-08-03--Nicolas Carlo
9	1	src/ast/identity.return-statements.test.ts
3	4	src/ast/identity.ts

--4f32fe26--2021-08-03--Nicolas Carlo
18	0	src/ast/identity.return-statements.test.ts
6	0	src/ast/identity.ts

--e7a26be1--2021-08-03--Nicolas Carlo
9	0	src/ast/identity.return-statements.test.ts
15	1	src/ast/identity.ts

--1f74d215--2021-08-03--Nicolas Carlo
1	1	src/ast/domain.ts

--ec6ec82a--2021-08-03--Nicolas Carlo
36	0	src/ast/identity.return-statements.test.ts
5	0	src/ast/identity.ts

--c002d5a9--2021-08-02--Nicolas Carlo
26	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
21	3	src/refactorings/turn-into-getter/turn-into-getter.ts

--6fa462b5--2021-08-02--Nicolas Carlo
21	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
15	5	src/refactorings/turn-into-getter/turn-into-getter.ts

--b35bf8da--2021-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--6a8ff917--2021-08-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--51d81313--2021-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--684c08f5--2021-08-01--dependabot[bot]
1	1	package.json
9	9	yarn.lock

--6580866d--2021-08-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--8c314fc9--2021-07-30--Nicolas Carlo
--3fdc089d--2021-07-30--allcontributors[bot]
9	0	.all-contributorsrc

--ff1415cc--2021-07-30--allcontributors[bot]
2	1	README.md

--90e02cb4--2021-07-30--Nicolas Carlo
4	0	CHANGELOG.md
16	1	src/ast/domain.ts
6	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
96	56	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--20125580--2021-07-30--Nicolas Carlo
12	13	src/ast/scope.ts

--73912104--2021-07-30--Nicolas Carlo
3	1	src/ast/scope.ts

--895dc15b--2021-07-29--Nicolas Carlo
72	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
6	0	src/refactorings/turn-into-getter/turn-into-getter.ts

--81272eec--2021-07-29--Nicolas Carlo
21	1	src/refactorings/turn-into-getter/turn-into-getter.test.ts
29	4	src/refactorings/turn-into-getter/turn-into-getter.ts

--a5fc53d2--2021-07-25--Nicolas Carlo
75	0	gitignore/playground.test.js
4	0	src/editor/error-reason.ts
17	0	src/refactorings/turn-into-getter/index.ts
33	0	src/refactorings/turn-into-getter/turn-into-getter.test.ts
34	0	src/refactorings/turn-into-getter/turn-into-getter.ts

--be2ce505--2021-07-22--Nicolas Carlo
0	108	REFACTORINGS.md

--f2dfcc94--2021-07-22--Nicolas Carlo
--57ebb30d--2021-07-22--allcontributors[bot]
9	0	.all-contributorsrc

--d33dfc19--2021-07-22--allcontributors[bot]
4	1	README.md

--abbbeb77--2021-07-22--Nicolas Carlo
--1dd61ac9--2021-07-22--Ikko Ashimine
1	1	CONTRIBUTING.md

--22b3551f--2021-07-21--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--4b2db9b0--2021-07-21--Nicolas Carlo
1	1	CHANGELOG.md

--f9244ebb--2021-07-21--Nicolas Carlo
--e44c44c9--2021-07-21--allcontributors[bot]
9	0	.all-contributorsrc

--6e3d82a9--2021-07-21--allcontributors[bot]
2	1	README.md

--275948fd--2021-07-21--Nicolas Carlo
4	0	CHANGELOG.md
6	0	src/refactorings/inline/inline-function/inline-function.test.ts
17	8	src/refactorings/inline/inline-function/inline-function.ts

--9819a76b--2021-07-21--Nicolas Carlo
5	5	CHANGELOG.md

--2f718e13--2021-07-20--Nicolas Carlo
--623b3bd0--2021-07-20--allcontributors[bot]
9	0	.all-contributorsrc

--a14d811f--2021-07-20--allcontributors[bot]
2	1	README.md

--fc4e6f55--2021-07-20--Nicolas Carlo
--979ce963--2021-07-20--Nicolas Carlo
1	1	src/refactorings/toggle-braces/index.ts

--5c37c2c3--2021-07-20--Nicolas Carlo
15	0	CHANGELOG.md

--03e68027--2021-07-20--Nicolas Carlo
10	88	REFACTORINGS.md
-	-	docs/demo/add-braces-to-arrow-function.gif
-	-	docs/demo/add-braces-to-if-statement.gif
-	-	docs/demo/add-braces-to-jsx-attribute.gif
-	-	docs/demo/remove-braces-from-arrow-function.gif
-	-	docs/demo/remove-braces-from-if-statement.gif
-	-	docs/demo/remove-braces-from-jsx-attribute.gif
-	-	docs/demo/toggle-braces.gif

--0491dd90--2021-07-20--Nicolas Carlo
0	170	package.json
1	14	src/extension.ts
0	69	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts
0	74	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
0	20	src/refactorings/add-braces-to-arrow-function/index.ts
0	130	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
0	88	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
0	20	src/refactorings/add-braces-to-if-statement/index.ts
0	74	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
0	50	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
0	20	src/refactorings/react/add-braces-to-jsx-attribute/index.ts
0	20	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
0	111	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts
0	51	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts
0	20	src/refactorings/remove-braces-from-arrow-function/index.ts
0	133	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts
0	135	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts
0	20	src/refactorings/remove-braces-from-if-statement/index.ts
0	160	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts
0	110	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts

--f2482b2d--2021-07-20--Nicolas Carlo
0	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--452a5d57--2021-07-20--Nicolas Carlo
13	0	src/refactorings/toggle-braces/toggle-braces.test.ts
18	1	src/refactorings/toggle-braces/toggle-braces.ts

--e10145df--2021-07-20--Nicolas Carlo
2	1	src/refactorings/toggle-braces/toggle-braces.test.ts
65	31	src/refactorings/toggle-braces/toggle-braces.ts

--c9d09be9--2021-07-20--Nicolas Carlo
1	0	jest.config.js
1	38	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
60	0	src/test/custom-matchers.ts

--f7b9be98--2021-07-20--Nicolas Carlo
13	5	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--44dd4d21--2021-07-20--Nicolas Carlo
31	0	package.json
3	1	src/extension.ts

--0e9ba114--2021-07-20--Nicolas Carlo
17	5	src/refactorings/toggle-braces/index.ts

--67efe129--2021-07-20--Nicolas Carlo
16	0	src/refactorings/toggle-braces/toggle-braces.test.ts
5	2	src/refactorings/toggle-braces/toggle-braces.ts

--8b084410--2021-07-19--Nicolas Carlo
61	5	src/refactorings/toggle-braces/toggle-braces.test.ts
20	3	src/refactorings/toggle-braces/toggle-braces.ts

--68e701ac--2021-07-19--Nicolas Carlo
4	4	src/refactorings/toggle-braces/toggle-braces.ts

--24b4a262--2021-07-19--Nicolas Carlo
56	0	src/refactorings/toggle-braces/toggle-braces.test.ts
33	3	src/refactorings/toggle-braces/toggle-braces.ts

--d0c4f6fc--2021-07-19--Nicolas Carlo
9	23	src/refactorings/toggle-braces/toggle-braces.ts

--4cfacaa0--2021-07-19--Nicolas Carlo
38	18	src/refactorings/toggle-braces/toggle-braces.ts

--f1302312--2021-07-19--Nicolas Carlo
60	35	src/refactorings/toggle-braces/toggle-braces.ts

--3b4800b1--2021-07-18--Nicolas Carlo
1	4	src/refactorings/toggle-braces/toggle-braces.ts

--59c585a8--2021-07-18--Nicolas Carlo
20	0	src/ast/identity.ts
1	20	src/refactorings/toggle-braces/toggle-braces.ts

--d2b1fdef--2021-07-18--Nicolas Carlo
22	0	src/ast/identity.ts
1	20	src/refactorings/toggle-braces/toggle-braces.ts

--5f128f0c--2021-07-18--Nicolas Carlo
2	2	src/refactorings/toggle-braces/toggle-braces.ts

--8df2d2e0--2021-07-18--Nicolas Carlo
5	0	src/editor/selection.ts
4	14	src/refactorings/toggle-braces/toggle-braces.ts

--582d6d3d--2021-07-18--Nicolas Carlo
6	1	src/ast/domain.ts
2	6	src/refactorings/toggle-braces/toggle-braces.ts

--0b3cbe20--2021-07-18--Nicolas Carlo
136	7	src/refactorings/toggle-braces/toggle-braces.test.ts
102	38	src/refactorings/toggle-braces/toggle-braces.ts

--e03f75a9--2021-07-18--Nicolas Carlo
6	1	src/ast/domain.ts
2	6	src/refactorings/toggle-braces/toggle-braces.ts

--006aa729--2021-07-18--Nicolas Carlo
2	3	src/refactorings/toggle-braces/toggle-braces.ts

--cf8dca5a--2021-07-18--Nicolas Carlo
6	1	src/refactorings/toggle-braces/toggle-braces.ts

--4b7ca13a--2021-07-18--Nicolas Carlo
2	14	src/refactorings/toggle-braces/toggle-braces.ts

--29a8ece6--2021-07-18--Nicolas Carlo
2	4	src/refactorings/toggle-braces/toggle-braces.ts

--b58daba2--2021-07-18--Nicolas Carlo
21	0	src/refactorings/toggle-braces/toggle-braces.test.ts
42	2	src/refactorings/toggle-braces/toggle-braces.ts

--03d94d06--2021-07-18--Nicolas Carlo
42	0	src/refactorings/toggle-braces/toggle-braces.test.ts
30	13	src/refactorings/toggle-braces/toggle-braces.ts

--9e4e8043--2021-07-18--Nicolas Carlo
4	0	src/editor/error-reason.ts
20	0	src/refactorings/toggle-braces/index.ts
116	0	src/refactorings/toggle-braces/toggle-braces.test.ts
88	0	src/refactorings/toggle-braces/toggle-braces.ts

--1236408d--2021-07-06--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--ce933545--2021-07-06--Nicolas Carlo
--bbde60f9--2021-07-06--Nicolas Carlo
--d4ffd07c--2021-07-06--Nicolas Carlo
1	1	package.json
158	73	yarn.lock

--d79f5368--2021-07-06--Nicolas Carlo
--97ca8410--2021-07-06--Nicolas Carlo
1	1	README.md

--205c9717--2021-07-06--Nicolas Carlo
10	0	CHANGELOG.md
3	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
12	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--220345a5--2021-07-06--allcontributors[bot]
9	0	.all-contributorsrc

--fca6efa7--2021-07-06--allcontributors[bot]
2	1	README.md

--fefc88ba--2021-07-06--Nicolas Carlo
5	3	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--537a6f06--2021-07-06--Nicolas Carlo
4	8	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--95d0e162--2021-07-06--Nicolas Carlo
3	3	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--e3f22604--2021-07-06--Nicolas Carlo
4	4	src/refactorings/inline/inline-variable/find-inlinable-code.ts

--9a7060c9--2021-07-02--Nicolas Carlo
1	0	CHANGELOG.md
5	0	package.json

--9c5602cd--2021-07-02--Nicolas Carlo
--a6fa69c8--2021-07-02--Nicolas Carlo
--812e0f22--2021-07-02--Nicolas Carlo
--4a7b4b3c--2021-07-02--Nicolas Carlo
4	1	src/ast/identity.ts
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--f4aa44e1--2021-07-02--Nicolas Carlo
4	1	CHANGELOG.md
2	1	src/ast/identity.ts
8	4	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
2	4	src/refactorings/extract/extract-variable/extract-variable.ts
10	0	src/refactorings/extract/extract-variable/occurrence.ts
6	2	src/refactorings/extract/extract-variable/variable.ts

--be27934f--2021-07-01--dependabot[bot]
1	1	package.json
14	197	yarn.lock

--62c7e859--2021-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--5c7679d1--2021-07-01--Nicolas Carlo
--92285e1b--2021-07-01--Nicolas Carlo
--a522e2ec--2021-07-01--Nicolas Carlo
--49d35c5b--2021-07-01--Nicolas Carlo
--b6141024--2021-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--bd732962--2021-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--c30980dc--2021-07-01--dependabot[bot]
1	1	package.json
34	47	yarn.lock

--0d195b69--2021-07-01--dependabot[bot]
1	1	package.json
75	167	yarn.lock

--75a6fc0c--2021-07-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--650ef988--2021-06-20--Nicolas Carlo
10	1	CHANGELOG.md
1	1	package.json

--f1c33243--2021-06-20--Nicolas Carlo
3	1	.vscodeignore

--46e54b40--2021-06-20--Nicolas Carlo
1	1	CHANGELOG.md

--64373b5d--2021-06-20--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--968421c6--2021-06-20--Nicolas Carlo
16	0	CONTRIBUTING.md

--10bff657--2021-06-20--Nicolas Carlo
--49a23d04--2021-06-20--allcontributors[bot]
9	0	.all-contributorsrc

--51634dc5--2021-06-20--allcontributors[bot]
2	1	README.md

--07f19d49--2021-06-20--Nicolas Carlo
4	0	CHANGELOG.md
9	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
13	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--34bd8c3b--2021-06-20--Nicolas Carlo
3	3	package.json
169	54	yarn.lock

--2e3c0f7b--2021-06-19--Nicolas Carlo
--e4416cd2--2021-06-19--allcontributors[bot]
9	0	.all-contributorsrc

--e73e65cf--2021-06-19--allcontributors[bot]
2	1	README.md

--7964bb4d--2021-06-19--Nicolas Carlo
1	0	CHANGELOG.md
54	2	src/ast/scope.test.ts
5	2	src/ast/scope.ts
10	1	src/refactorings/inline/inline-variable/inline-variable.test.ts

--4c0c4d18--2021-06-19--Nicolas Carlo
7	0	src/ast/identity.ts

--9087eb36--2021-06-09--Nicolas Carlo
--1f36bb56--2021-06-09--dependabot[bot]
4	11	yarn.lock

--319ba7b0--2021-06-05--Nicolas Carlo
--4b6a55d9--2021-06-01--Nicolas Carlo
--186eda83--2021-06-01--Nicolas Carlo
--562e954d--2021-06-01--Nicolas Carlo
--ca803872--2021-06-01--dependabot[bot]
1	1	package.json
4	9	yarn.lock

--3eaa0373--2021-06-01--dependabot[bot]
1	1	package.json
13	1	yarn.lock

--02e6c683--2021-06-01--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--d3cd6053--2021-06-01--dependabot[bot]
1	1	package.json
15	9	yarn.lock

--bca26f27--2021-05-31--Nicolas Carlo
--f92672fe--2021-05-28--dependabot[bot]
3	3	yarn.lock

--d517aac5--2021-05-28--Nicolas Carlo
--a727bc04--2021-05-28--Nicolas Carlo
--2736c198--2021-05-28--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--a9022298--2021-05-27--dependabot[bot]
1	1	package.json
17	78	yarn.lock

--76b7f7c1--2021-05-27--Nicolas Carlo
1	1	package.json

--1e85d379--2021-05-27--Nicolas Carlo
2	2	package.json
8	8	yarn.lock

--014eacca--2021-05-27--Nicolas Carlo
1	1	package.json
76	6	yarn.lock

--cd2a2bea--2021-05-27--Nicolas Carlo
--42386ad6--2021-05-27--Nicolas Carlo
--3cad0bbc--2021-05-27--dependabot[bot]
1	1	package.json
5	6	yarn.lock

--1e1858da--2021-05-27--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--b9d60f9c--2021-05-27--Nicolas Carlo
1	1	package.json
5	10	yarn.lock

--b0202670--2021-05-27--Nicolas Carlo
--e05e8b6b--2021-05-27--Nicolas Carlo
--2fa6cc69--2021-05-27--Nicolas Carlo
--8405773a--2021-05-27--Nicolas Carlo
--8ffcf96d--2021-05-27--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--562ce9d6--2021-05-27--dependabot[bot]
1	1	package.json
4	4	yarn.lock

--bb10d4eb--2021-05-27--dependabot[bot]
1	1	package.json
33	33	yarn.lock

--1959d7a4--2021-05-27--dependabot[bot]
1	1	package.json
34	27	yarn.lock

--1e5d7e34--2021-05-27--Nicolas Carlo
--b0534e4c--2021-05-27--Nicolas Carlo
--d90abcec--2021-05-25--dependabot[bot]
25	51	yarn.lock

--89530edf--2021-05-21--Nicolas Carlo
--7b9829b0--2021-05-21--allcontributors[bot]
9	0	.all-contributorsrc

--8a0748c8--2021-05-21--allcontributors[bot]
2	1	README.md

--d3bd4181--2021-05-21--Nicolas Carlo
4	0	CHANGELOG.md
5	0	src/ast/domain.ts
37	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--d53265e1--2021-05-20--Nicolas Carlo
6	1	src/refactorings/extract/extract-variable/extract-variable.ts

--81f90a6a--2021-05-20--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--b5a1e695--2021-05-20--Nicolas Carlo
0	1	src/refactorings/extract/extract-variable/occurrence.ts

--3e3482b1--2021-05-20--Nicolas Carlo
3	0	CHANGELOG.md
16	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.ts
5	2	src/refactorings/extract/extract-variable/occurrence.ts

--380b3500--2021-05-20--Nicolas Carlo
4	0	CHANGELOG.md
11	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
2	1	src/refactorings/extract/extract-variable/extract-variable.ts

--d486bc70--2021-05-09--Nicolas Carlo
--407a6254--2021-05-08--dependabot[bot]
3	3	yarn.lock

--d79a8d2e--2021-05-07--Nicolas Carlo
--7bfab769--2021-05-07--dependabot-preview[bot]
3	3	yarn.lock

--0f3dbec7--2021-05-01--Nicolas Carlo
1	1	package.json
90	1	yarn.lock

--85117832--2021-05-01--Nicolas Carlo
--3f54de44--2021-05-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--4a15f826--2021-05-01--Nicolas Carlo
--f4dafb38--2021-05-01--Nicolas Carlo
--b62a4c7a--2021-05-01--Nicolas Carlo
--8509c1d2--2021-05-01--Nicolas Carlo
--cf9f49d9--2021-05-01--Nicolas Carlo
--1c7c7429--2021-05-01--dependabot-preview[bot]
1	1	package.json
16	14	yarn.lock

--29722582--2021-05-01--Nicolas Carlo
--3fc656d8--2021-05-01--Nicolas Carlo
--de980dad--2021-05-01--dependabot-preview[bot]
1	1	package.json
9	9	yarn.lock

--42c7eb41--2021-05-01--dependabot-preview[bot]
1	1	package.json
17	72	yarn.lock

--57d45eab--2021-05-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--99414cab--2021-05-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--7428f415--2021-05-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--710f2509--2021-05-01--dependabot-preview[bot]
1	1	package.json
6	6	yarn.lock

--a912bf20--2021-04-29--dependabot-preview[bot]
51	0	.github/dependabot.yml

--652611df--2021-04-29--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--726b2a2d--2021-04-29--Nicolas Carlo
4	0	CHANGELOG.md
22	6	src/extension.ts

--001d31bc--2021-04-29--Nicolas Carlo
1	0	CHANGELOG.md
6	3	README.md
11	4	package.json
9	2	src/action-providers.ts
16	1	src/vscode-configuration.ts

--e3ef1b62--2021-04-22--Nicolas Carlo
--f3710794--2021-04-22--Nicolas Carlo
4	0	CHANGELOG.md

--2f909283--2021-04-22--Nicolas Carlo
37	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--705b2e3c--2021-04-17--Nicolas Carlo
19	44	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--53ebaf1d--2021-04-17--Nicolas Carlo
11	0	src/ast/transformation.ts
3	3	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--b1fc1d91--2021-04-17--Nicolas Carlo
5	5	src/ast/scope.ts

--4e8e124d--2021-04-17--Nicolas Carlo
3	0	src/ast/domain.ts
3	3	src/ast/scope.ts
3	3	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--490c6392--2021-04-17--Nicolas Carlo
2	2	src/ast/scope.ts
20	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
17	5	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--9b5d50e4--2021-04-16--Nicolas Carlo
22	1	src/ast/scope.ts
5	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
8	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--c8801075--2021-04-16--Nicolas Carlo
33	3	src/ast/scope.ts
20	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
24	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--a6ee972c--2021-04-16--Nicolas Carlo
16	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
29	0	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--59ab8f19--2021-04-17--Nicolas Carlo
1	0	CHANGELOG.md
12	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
15	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--40bbe8f8--2021-04-16--Nicolas Carlo
4	0	CHANGELOG.md
4	0	src/refactorings/extract/extract-variable/extract-variable.non-extractable.test.ts
1	0	src/refactorings/extract/extract-variable/extract-variable.ts

--4dade884--2021-04-15--Nicolas Carlo
1	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--04b48400--2021-04-15--Nicolas Carlo
2	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--cb49d55a--2021-04-15--Nicolas Carlo
0	18	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--b214c071--2021-04-15--Nicolas Carlo
8	9	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--9e57482f--2021-04-15--Nicolas Carlo
34	20	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--60095e4e--2021-04-15--Nicolas Carlo
6	5	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--d33a28a2--2021-04-15--Nicolas Carlo
13	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--b9337904--2021-04-15--Nicolas Carlo
3	15	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--b9bd7442--2021-04-15--Nicolas Carlo
19	0	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--29db98a9--2021-04-15--Nicolas Carlo
2	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--2d046cb1--2021-04-15--Nicolas Carlo
10	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--bb227e22--2021-04-15--Nicolas Carlo
6	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--457b3a00--2021-04-15--Nicolas Carlo
35	32	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--dc1e51ec--2021-04-15--Nicolas Carlo
7	3	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--1bafec08--2021-04-15--Nicolas Carlo
12	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--6959c2fa--2021-04-15--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--5ebe0266--2021-04-15--Nicolas Carlo
2	0	CHANGELOG.md
10	0	src/ast/identity.ts
15	0	src/refactorings/extract-generic-type/extract-generic-type.function.test.ts

--c25134d0--2021-04-15--Nicolas Carlo
1	1	CHANGELOG.md

--fa3b85b1--2021-04-08--Nicolas Carlo
2	0	CHANGELOG.md
11	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
9	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--548ac710--2021-04-08--Nicolas Carlo
11	0	src/editor/adapters/in-memory-editor.test.ts
4	0	src/editor/adapters/in-memory-editor.ts

--695b6992--2021-04-08--Nicolas Carlo
23	0	src/editor/adapters/in-memory-editor.test.ts
4	0	src/editor/adapters/in-memory-editor.ts

--02ec80c9--2021-04-08--Nicolas Carlo
1	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--2d8b3470--2021-04-08--Nicolas Carlo
4	3	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--8badad90--2021-04-08--Nicolas Carlo
3	1	src/editor/selection.ts

--09cb9bf2--2021-04-08--Nicolas Carlo
10	2	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--c92225ba--2021-04-08--Nicolas Carlo
10	0	CHANGELOG.md
15	0	src/refactorings/extract/extract-type/extract-type.test.ts
22	0	src/refactorings/extract/extract-type/extract-type.ts

--b48bd44b--2021-04-08--Nicolas Carlo
1	0	CHANGELOG.md

--5aead5d2--2021-04-08--Nicolas Carlo
6	0	src/refactorings/extract/extract-type/extract-type.test.ts

--9d22ec5f--2021-04-08--Nicolas Carlo
6	0	src/refactorings/extract/extract-type/extract-type.test.ts
4	1	src/refactorings/extract/extract-type/extract-type.ts

--2e96c7dc--2021-04-08--Nicolas Carlo
6	0	src/refactorings/extract/extract-type/extract-type.test.ts
41	8	src/refactorings/extract/extract-type/extract-type.ts

--1add218d--2021-04-08--Nicolas Carlo
0	6	README.md

--a7104b83--2021-04-03--Nicolas Carlo
--3c2bbe3b--2021-04-03--Nicolas Carlo
--3c6c58b5--2021-04-03--allcontributors[bot]
2	1	.all-contributorsrc

--30d3a646--2021-04-03--allcontributors[bot]
1	1	README.md

--d20ea667--2021-04-03--allcontributors[bot]
9	0	.all-contributorsrc

--2014237c--2021-04-03--allcontributors[bot]
4	1	README.md

--000ecc77--2021-04-03--Nicolas Carlo
11	0	CHANGELOG.md
4	0	src/editor/error-reason.ts
12	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
11	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--46853802--2021-04-03--Nicolas Carlo
5	5	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--069367c2--2021-04-03--Nicolas Carlo
9	5	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--9eaf51c6--2021-04-03--Nicolas Carlo
9	2	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--e992fedc--2021-04-03--Nicolas Carlo
4	0	CHANGELOG.md
11	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
2	1	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--8200cd1f--2021-04-03--Nicolas Carlo
5	1	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--83365511--2021-04-03--Nicolas Carlo
2	2	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--74aedffe--2021-04-03--Nicolas Carlo
5	2	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--63397dec--2021-04-03--Nicolas Carlo
1	1	package.json

--cfbb9080--2021-04-03--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--dfab8dc4--2021-04-03--Nicolas Carlo
2	3	CHANGELOG.md

--06c8b220--2021-04-02--Nicolas Carlo
--c5d8b812--2021-04-02--Nicolas Carlo
--930e699f--2021-04-02--allcontributors[bot]
10	0	.all-contributorsrc

--a6d3060f--2021-04-02--allcontributors[bot]
2	1	README.md

--287ef0ef--2021-04-01--Nicolas Carlo
26	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
22	18	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--da120262--2021-04-01--Nicolas Carlo
5	5	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--d9336230--2021-04-01--Nicolas Carlo
1	4	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--b38e32be--2021-04-01--Nicolas Carlo
3	3	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--a5a717de--2021-04-01--Nicolas Carlo
11	19	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--9054d81c--2021-04-01--Nicolas Carlo
1	9	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--c13efabc--2021-04-01--Nicolas Carlo
5	11	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--b646af1c--2021-04-01--Nicolas Carlo
24	11	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--408a6310--2021-04-01--Nicolas Carlo
1	4	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--b170c15a--2021-04-01--Nicolas Carlo
24	3	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
2	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--35dbd713--2021-04-01--Nicolas Carlo
1	4	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts

--dd007094--2021-04-01--Nicolas Carlo
9	9	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts

--ec6b669b--2021-04-01--Nicolas Carlo
--3143a633--2021-04-01--Nicolas Carlo
--3da968ff--2021-04-01--allcontributors[bot]
2	1	.all-contributorsrc

--4a42b0c9--2021-04-01--allcontributors[bot]
1	1	README.md

--d4a2b1ac--2021-04-01--Nicolas Carlo
16	0	CHANGELOG.md

--a0137ca3--2021-04-01--Nicolas Carlo
21	1	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
4	0	src/refactorings/extract/extract-variable/occurrence.ts

--7b91dd27--2021-04-01--Nicolas Carlo
2	2	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
11	0	src/refactorings/extract/extract-variable/occurrence.ts

--6c222dce--2021-03-25--Nicolas Carlo
4	9	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--01480302--2021-03-25--Nicolas Carlo
8	1	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--61338948--2021-03-25--Nicolas Carlo
4	0	src/editor/selection.ts
1	1	src/refactorings/extract/extract-type/extract-type.test.ts

--ac36c080--2021-03-25--Nicolas Carlo
19	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--f7553381--2021-03-11--Nicolas Carlo
4	0	CHANGELOG.md
12	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.ts
14	1	src/refactorings/extract/extract-variable/occurrence.ts

--2444c6c0--2021-02-05--Nicolas Carlo
5	0	src/editor/selection.ts

--7111316b--2021-02-05--Nicolas Carlo
4	0	src/editor/position.ts

--3cf69197--2021-02-05--Nicolas Carlo
7	15	src/editor/adapters/in-memory-editor.ts
23	0	src/editor/editor-contract-test.ts

--de0f54e1--2021-02-05--Nicolas Carlo
10	10	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--89fe9645--2021-02-04--Nicolas Carlo
15	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
16	0	src/refactorings/extract/extract-variable/occurrence.ts
22	1	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--150f3779--2021-02-04--Nicolas Carlo
23	0	src/ast/scope.ts

--30a4a876--2021-02-04--Nicolas Carlo
5	1	src/array.ts

--cbd85f17--2021-03-31--Nicolas Carlo
--6728af5c--2021-03-29--dependabot-preview[bot]
3	3	yarn.lock

--1d51046a--2021-03-23--Zak Miller
100	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
81	26	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--c2784dec--2021-03-18--Nicolas Carlo
--faa27188--2021-03-18--Nicolas Carlo
2	2	package.json
384	1158	yarn.lock

--d49df681--2021-03-18--Nicolas Carlo
260	278	yarn.lock

--df5898ac--2021-03-18--Nicolas Carlo
1	0	.husky/.gitignore
4	0	.husky/pre-commit
2	6	package.json

--15389e37--2021-03-18--Nicolas Carlo
--6987759f--2021-03-18--Nicolas Carlo
2	2	package.json

--e0075123--2021-03-18--Nicolas Carlo
2	2	package.json

--2e268396--2021-03-18--Nicolas Carlo
2	2	package.json

--a39a9185--2021-03-18--Nicolas Carlo
1	0	CHANGELOG.md
13	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
1	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--0908ec9f--2021-03-18--Nicolas Carlo
2	2	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts

--b98c7681--2021-03-18--Nicolas Carlo
4	0	CHANGELOG.md
17	1	src/refactorings/extract/extract-type/extract-type.test.ts
5	1	src/refactorings/extract/extract-type/extract-type.ts

--732f3310--2021-03-18--Nicolas Carlo
10	3	src/refactorings/extract/extract-type/extract-type.ts

--1173bbec--2021-03-09--Nicolas Carlo
4	0	CHANGELOG.md

--a4a06654--2021-03-09--Nicolas Carlo
--46a638e5--2021-03-09--allcontributors[bot]
9	0	.all-contributorsrc

--c3b78002--2021-03-09--allcontributors[bot]
2	1	README.md

--3a21acec--2021-03-09--Nicolas Carlo
--eb4fc339--2021-03-08--Nicolas Carlo
--3add5073--2021-03-08--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--ce0ce3c3--2021-03-08--Nicolas Carlo
4	0	CHANGELOG.md
3	3	src/refactorings/extract/index.ts

--e8025029--2021-03-08--dependabot-preview[bot]
17	17	yarn.lock

--2d38bde6--2021-03-07--Christina Braun
9	1	src/ast/transformation.test.ts
60	54	src/ast/transformation.ts

--b1a000b5--2021-03-04--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--f7139a9f--2021-03-04--Nicolas Carlo
22	788	README.md
728	0	REFACTORINGS.md

--5772d20d--2021-03-04--Nicolas Carlo
--14b48570--2021-03-04--Nicolas Carlo
--607c10d2--2021-03-04--Nicolas Carlo
2	2	src/refactorings/extract/index.ts

--ac2d40fc--2021-03-04--Nicolas Carlo
20	4	README.md
-	-	docs/demo/extract-type.gif

--9ac4fec9--2021-03-04--Nicolas Carlo
8	0	CHANGELOG.md

--4ff2a256--2021-03-04--Nicolas Carlo
4	4	src/refactorings/extract-generic-type/extract-generic-type.ts
2	1	src/refactorings/extract-generic-type/index.ts

--66444dab--2021-03-04--Nicolas Carlo
23	0	package.json
2	1	src/extension.ts
5	5	src/refactorings/extract-generic-type/extract-generic-type.ts
17	0	src/refactorings/extract-generic-type/index.ts

--154101b2--2021-03-04--Nicolas Carlo
122	0	src/refactorings/extract-generic-type/extract-generic-type.function.test.ts
290	0	src/refactorings/extract-generic-type/extract-generic-type.interface.test.ts
28	0	src/refactorings/extract-generic-type/extract-generic-type.test.ts
267	0	src/refactorings/extract-generic-type/extract-generic-type.ts
0	122	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts
0	290	src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts
0	28	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
0	267	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--6744c6fc--2021-03-04--Nicolas Carlo
0	12	src/refactorings/extract/extract-type/index.ts

--79e75e0b--2021-03-04--Nicolas Carlo
2	2	src/refactorings/extract/index.ts

--30f5b623--2021-03-04--Nicolas Carlo
36	1	src/refactorings/extract/extract-type/extract-type.test.ts
6	1	src/refactorings/extract/extract-type/extract-type.ts

--030b18ae--2021-03-01--Nicolas Carlo
--ef84060d--2021-03-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--1628607e--2021-03-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--f7396ae3--2021-02-26--Nicolas Carlo
16	0	src/editor/position.test.ts
58	0	src/editor/position.ts

--16f35350--2021-02-25--Nicolas Carlo
2	2	src/refactorings/extract/extract-type/extract-type.ts

--c628cd3a--2021-02-09--Nicolas Carlo
4	0	CHANGELOG.md

--9b6624b1--2021-02-09--Nicolas Carlo
4	2	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--e248cb97--2021-02-09--Nicolas Carlo
6	8	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--89de5d09--2021-02-09--Nicolas Carlo
--59249cbe--2021-02-09--Nicolas Carlo
--78871bbb--2021-02-09--allcontributors[bot]
2	1	.all-contributorsrc

--fc8b00da--2021-02-09--allcontributors[bot]
1	1	README.md

--29244906--2021-02-05--Iuliu Pop
1	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--a626d265--2021-02-05--Iuliu Pop
2	2	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--def9c6a3--2021-02-04--Iuliu Pop
6	3	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--5d03ebe4--2021-02-04--Iuliu Pop
7	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts

--e15f6155--2021-02-04--Nicolas Carlo
4	0	CHANGELOG.md

--40f89e3f--2021-02-04--Nicolas Carlo
--9ff96c15--2021-02-04--Nicolas Carlo
--fa3148db--2021-02-04--allcontributors[bot]
2	1	.all-contributorsrc

--e696922c--2021-02-04--allcontributors[bot]
1	1	README.md

--c08cc97b--2021-02-04--Nicolas Carlo
9	0	src/refactorings/extract/extract-type/extract-type.test.ts
13	2	src/refactorings/extract/extract-type/extract-type.ts

--6df8a42d--2021-02-04--Nicolas Carlo
8	0	src/refactorings/extract/extract-type/extract-type.test.ts
2	1	src/refactorings/extract/extract-type/extract-type.ts

--cdf783a4--2021-01-28--Nicolas Carlo
24	6	src/refactorings/extract/extract-variable/occurrence.ts

--b766ccce--2021-01-28--Nicolas Carlo
17	3	src/refactorings/extract/extract-variable/occurrence.ts
35	16	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--35b5ea35--2021-01-28--Nicolas Carlo
1	3	src/refactorings/extract/extract-variable/extract-variable.ts
19	5	src/refactorings/extract/extract-variable/occurrence.ts
5	10	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--df7c4684--2021-02-01--Nicolas Carlo
--7f499428--2021-02-01--Nicolas Carlo
--443900ef--2021-02-01--Iuliu Pop
2	1	README.md
-	-	docs/demo/split-multiple-declarations.gif

--29c8d7ef--2021-02-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--c0c83883--2021-02-01--dependabot-preview[bot]
1	1	package.json
9	14	yarn.lock

--1b722c7e--2021-02-01--Nicolas Carlo
--5628ab9c--2021-02-01--Nicolas Carlo
--2e2d1aa2--2021-02-01--Iuliu Pop
3	3	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--da234754--2021-02-01--Iuliu Pop
2	2	src/refactorings/split-multiple-declarations/index.ts
4	1	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--e5f183bc--2021-02-01--Iuliu Pop
0	6	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--78fb7a94--2021-02-01--Iuliu Pop
7	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--8e80b6fc--2021-02-01--Iuliu Pop
8	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--134821f1--2021-02-01--Iuliu Pop
6	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--4c368261--2021-02-01--Iuliu Pop
12	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts

--75e84e16--2021-02-01--Iuliu Pop
6	5	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--5dbb0df1--2021-02-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--448d0485--2021-02-01--Nicolas Carlo
--8ef2b4b5--2021-02-01--Nicolas Carlo
--3a5ea68d--2021-02-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--42361d02--2021-02-01--dependabot-preview[bot]
1	1	package.json
8	22	yarn.lock

--f68f8724--2021-02-01--dependabot-preview[bot]
1	1	package.json
27	1	yarn.lock

--2386ee9e--2021-01-28--Nicolas Carlo
4	0	src/editor/error-reason.ts
38	0	src/refactorings/extract/extract-type/extract-type.test.ts
53	0	src/refactorings/extract/extract-type/extract-type.ts
12	0	src/refactorings/extract/extract-type/index.ts

--3c0dcfc1--2021-01-28--Nicolas Carlo
1	0	src/ast/scope.ts

--367277ae--2021-01-24--Iuliu Pop
6	1	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts
19	3	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--c1e64603--2021-01-24--Nicolas Carlo
1	0	.github/FUNDING.yml

--ec41b951--2021-01-23--Iuliu Pop
8	4	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--59f93c75--2021-01-23--Nicolas Carlo
1	0	.travis.yml

--21030a69--2021-01-22--Nicolas Carlo
6	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--864e7321--2021-01-22--Iuliu Pop
15	0	README.md

--8f2ffdc2--2021-01-22--Iuliu Pop
26	0	package.json

--fcdc0e24--2021-01-22--Iuliu Pop
3	1	src/extension.ts

--2bb3c9d8--2021-01-22--Iuliu Pop
1	1	src/extension.ts

--15df3241--2021-01-22--Iuliu Pop
4	0	src/editor/error-reason.ts
20	0	src/refactorings/split-multiple-declarations/index.ts
33	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts
34	0	src/refactorings/split-multiple-declarations/split-multiple-declarations.ts

--1be60d87--2021-01-22--Nicolas Carlo
--e617f049--2021-01-22--Nicolas Carlo
--69b67196--2021-01-22--Nicolas Carlo
--cbe0c621--2021-01-22--allcontributors[bot]
9	0	.all-contributorsrc

--bdf8d6d0--2021-01-22--allcontributors[bot]
21	20	README.md

--cd54e886--2021-01-22--Nicolas Carlo
--af247c8b--2021-01-21--Nicolas Carlo
48	7	CONTRIBUTING.md
-	-	docs/contributing/contract-tests-debugger.png
-	-	docs/contributing/contract-tests-report.png
-	-	docs/contributing/how-tests-work.png
16	0	docs/contributing/how-tests-work.svg

--dbc9e52e--2021-01-21--Nicolas Carlo
0	1	.travis.yml

--d7ede1f1--2021-01-21--Iuliu Pop
1	1	CONTRIBUTING.md

--f70eeddd--2021-01-21--Iuliu Pop
1	1	docs/adr/0006-create-generator-to-bootstrap-new-refactorings.md

--eb36ac7e--2021-01-21--Nicolas Carlo
10	0	.travis.yml

--7d3c0bfe--2021-01-21--Nicolas Carlo
1	1	.travis.yml

--5ecf924d--2021-01-21--Nicolas Carlo
1	0	.travis.yml

--4852e3b8--2021-01-21--Nicolas Carlo
1	0	package.json

--5f28d2bb--2021-01-21--Nicolas Carlo
2	1	src/test/mocha-test-runner.ts

--807ded3f--2021-01-21--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.contract.test.ts
1	1	src/editor/editor-contract-test.ts

--03334e6a--2021-01-21--Nicolas Carlo
1	1	docs/adr/0002-no-integration-test.md
37	0	docs/adr/0010-integration-tests.md

--cbe4e079--2021-01-20--Nicolas Carlo
67	0	src/editor/adapters/vscode-editor.contract.test.ts
17	1	src/editor/adapters/vscode-editor.ts

--3ae51cc0--2021-01-20--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts

--9615d928--2021-01-20--Nicolas Carlo
9	5	src/editor/adapters/vscode-editor.ts

--81930695--2021-01-20--Nicolas Carlo
2	2	src/editor/editor-contract-test.ts

--e8ce9062--2021-01-20--Nicolas Carlo
3	1	src/editor/editor-contract-test.ts

--7a9cd688--2021-01-20--Nicolas Carlo
2	0	package.json
45	1	yarn.lock

--e3df9a5a--2021-01-20--Nicolas Carlo
5	2	src/editor/editor-contract-test.ts

--3ab7f907--2021-01-20--Nicolas Carlo
1	1	src/editor/editor-contract-test.ts

--6f364a9f--2021-01-20--Nicolas Carlo
1	1	tsconfig.json

--f7d92714--2021-01-17--Nicolas Carlo
4	3	src/editor/adapters/in-memory-editor.contract.test.ts
21	21	src/editor/editor-contract-test.ts

--e74c2036--2021-01-17--Nicolas Carlo
1	2	src/editor/adapters/in-memory-editor.contract.test.ts
21	24	src/editor/editor-contract-test.ts

--3e8d8800--2021-01-17--Nicolas Carlo
24	12	src/editor/adapters/in-memory-editor.contract.test.ts
96	68	src/editor/editor-contract-test.ts

--8a04ce67--2021-01-17--Nicolas Carlo
2	0	package.json
81	3	yarn.lock

--34639320--2021-01-16--Nicolas Carlo
51	0	src/editor/adapters/in-memory-editor.contract.test.ts
0	51	src/editor/adapters/in-memory-editor.test.ts

--2d3f18b5--2021-01-16--Nicolas Carlo
13	0	.vscode/launch.json
1	1	jest.config.js
2	0	package.json
35	0	src/test/mocha-test-runner.ts
25	0	src/test/run-contract-tests.ts

--2b97407d--2021-01-16--Nicolas Carlo
4	0	package.json
2	1	tsconfig.json
362	11	yarn.lock

--f9011e36--2021-01-11--Nicolas Carlo
-	-	docs/demo/move-to-existing-file.gif
-	-	docs/demo/moving-to-existing-file.gif

--008e869b--2021-01-11--Nicolas Carlo
1	1	.github/workflows/ovsx-deploy.yml
1	1	.github/workflows/vscode-deploy.yml

--e2305f3b--2021-01-10--Nicolas Carlo
6	1	CHANGELOG.md
1	1	package.json

--c3df17d0--2021-01-10--Nicolas Carlo
1	1	package.json

--0e983a74--2021-01-10--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--33fba7db--2021-01-09--Nicolas Carlo
1	0	CHANGELOG.md

--5614f829--2021-01-09--Nicolas Carlo
0	13	src/array-helpers.ts
13	0	src/array.ts
1	1	src/ast/identity.ts
1	1	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
1	1	src/refactorings/extract/extract-generic-type/extract-generic-type.ts
1	1	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
1	1	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--9a749632--2021-01-09--Nicolas Carlo
8	0	src/assert.ts
38	0	src/ast/scope.test.ts
8	3	src/ast/scope.ts
13	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
1	1	src/refactorings/extract-class/extract-class-command.ts
0	8	src/refactorings/extract-class/utils/assert.ts

--6ca16d22--2021-01-07--Nicolas Carlo
4	0	CHANGELOG.md
2	1	src/ast/scope.ts
14	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--cbbd6ebd--2021-01-07--Nicolas Carlo
--50ecbbe2--2021-01-07--Nicolas Carlo
4	0	CHANGELOG.md

--e50c11b6--2021-01-07--Nicolas Carlo
20	0	README.md
-	-	docs/demo/moving-to-existing-file.gif

--bd62c6aa--2021-01-07--Nicolas Carlo
5	0	package.json

--dff49a4f--2021-01-07--Nicolas Carlo
14	3	src/ast/scope.ts
20	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--48a339f0--2021-01-03--Nicolas Carlo
6	0	src/editor/error-reason.ts
16	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
24	6	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--7678ea15--2021-01-03--Nicolas Carlo
31	1	src/ast/scope.ts

--a1c87c51--2021-01-03--Nicolas Carlo
3	3	src/ast/scope.ts

--636038fb--2020-12-30--Nicolas Carlo
10	12	src/ast/scope.ts
4	4	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
7	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--87fdb449--2020-12-30--Nicolas Carlo
24	0	src/ast/domain.ts
6	21	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--17219f18--2020-12-30--Nicolas Carlo
3	1	src/ast/scope.ts
1	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--4f3f1a3d--2020-12-30--Nicolas Carlo
10	0	src/ast/domain.ts
9	9	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--3ba01ebf--2020-12-30--Nicolas Carlo
32	1	src/ast/scope.ts
25	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
42	11	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--a8527517--2020-12-30--Nicolas Carlo
1	1	src/editor/path.test.ts

--fa8c0709--2020-12-30--Nicolas Carlo
9	0	src/editor/path.test.ts
10	5	src/editor/path.ts

--686a81c9--2020-12-26--Nicolas Carlo
36	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
23	5	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--6490933d--2020-12-26--Nicolas Carlo
8	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--9f7dd20f--2020-12-26--Nicolas Carlo
8	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--b7ade7e7--2020-12-26--Nicolas Carlo
26	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
6	0	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--51655899--2020-12-26--Nicolas Carlo
25	1	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--ed33636a--2020-12-26--Nicolas Carlo
27	1	src/editor/editor-contract-test.ts

--b15c7f9d--2020-12-26--Nicolas Carlo
0	1	src/editor/adapters/vscode-editor.ts
0	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--3fa64417--2020-12-26--Nicolas Carlo
3	3	README.md
3	2	src/editor/adapters/vscode-editor.ts

--76eb8468--2020-12-26--Nicolas Carlo
6	23	src/action-providers.ts
26	0	src/vscode-configuration.ts

--8805c1a8--2020-12-26--Nicolas Carlo
22	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--bb79679f--2020-12-26--Nicolas Carlo
4	0	src/editor/error-reason.ts
10	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
6	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--841cb32e--2020-12-26--Nicolas Carlo
5	2	src/editor/adapters/attempting-editor.ts
4	1	src/editor/adapters/in-memory-editor.ts
5	4	src/editor/adapters/vscode-editor.ts
4	1	src/editor/editor.ts
7	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--df0bb689--2020-12-26--Nicolas Carlo
1	0	src/editor/adapters/vscode-editor.ts

--450fd2d9--2020-12-25--Nicolas Carlo
7	1	src/editor/adapters/vscode-editor.ts
2	0	src/editor/editor.ts

--879c3f62--2020-12-25--Nicolas Carlo
18	0	src/editor/path.test.ts
17	1	src/editor/path.ts

--1a0fb9db--2020-12-24--Nicolas Carlo
1	0	src/editor/adapters/vscode-editor.ts
6	0	src/editor/path.test.ts
4	0	src/editor/path.ts

--086ddf35--2020-12-24--Nicolas Carlo
8	0	src/editor/path.test.ts
12	2	src/editor/path.ts

--da6cd24c--2020-12-24--Nicolas Carlo
6	6	src/editor/adapters/attempting-editor.ts
7	7	src/editor/adapters/in-memory-editor.ts
10	10	src/editor/adapters/vscode-editor.ts
6	5	src/editor/editor.ts
25	13	src/editor/path.test.ts
19	5	src/editor/path.ts
6	6	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
2	2	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--a5989e0c--2020-12-24--Nicolas Carlo
7	8	src/editor/adapters/vscode-editor.ts

--22de1144--2020-12-24--Nicolas Carlo
27	0	src/editor/path.test.ts
12	0	src/editor/path.ts

--3174d263--2020-12-24--Nicolas Carlo
0	4	package.json
0	1	src/editor/adapters/vscode-editor.ts
10	1	src/extension.ts

--26ca26fd--2020-12-24--Nicolas Carlo
1	1	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
1	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--0c152fbf--2020-12-24--Nicolas Carlo
9	0	src/editor/path.test.ts
4	0	src/editor/path.ts

--17d80d30--2020-12-24--Nicolas Carlo
1	4	src/editor/editor.ts
5	0	src/editor/path.ts

--a2da5c35--2020-12-24--Nicolas Carlo
1	4	src/editor/adapters/vscode-editor.ts

--9be69318--2020-12-24--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts

--faf7d2c3--2020-12-24--Nicolas Carlo
2	2	src/editor/adapters/vscode-editor.ts

--ac8522fd--2020-12-24--Nicolas Carlo
5	3	src/editor/adapters/vscode-editor.ts
3	1	src/editor/editor.ts
4	4	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
4	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--668f4a86--2020-12-24--Nicolas Carlo
1	1	src/refactorings/move-to-existing-file/index.ts

--5dcf35fe--2020-12-24--Nicolas Carlo
26	0	package.json
2	0	src/extension.ts

--e6ca9ac3--2020-12-12--Nicolas Carlo
22	5	src/editor/adapters/vscode-editor.ts

--b3768b56--2020-12-12--Nicolas Carlo
2	2	src/editor/adapters/attempting-editor.ts
1	1	src/editor/adapters/in-memory-editor.ts
11	4	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts
2	1	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--737ef9aa--2020-12-10--Nicolas Carlo
1	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
7	4	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--ac14bc88--2020-12-10--Nicolas Carlo
4	0	src/editor/adapters/attempting-editor.ts
4	0	src/editor/adapters/in-memory-editor.ts
6	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts

--b6c19942--2020-12-10--Nicolas Carlo
16	16	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--33756d6e--2020-12-09--Nicolas Carlo
1	1	src/editor/editor.ts

--52ad951d--2020-12-09--Nicolas Carlo
1	0	src/editor/adapters/vscode-editor.ts

--c1176e54--2020-12-09--Nicolas Carlo
30	5	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--9832bc97--2020-12-09--Nicolas Carlo
5	1	src/editor/adapters/attempting-editor.ts
4	4	src/editor/adapters/in-memory-editor.ts
4	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts
1	1	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--8dae581f--2020-12-09--Nicolas Carlo
11	7	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--e8af9431--2020-12-09--Nicolas Carlo
6	1	src/editor/adapters/attempting-editor.ts
9	9	src/editor/adapters/in-memory-editor.ts
6	1	src/editor/adapters/vscode-editor.ts
4	0	src/editor/editor.ts
2	1	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--fde361d3--2020-12-09--Nicolas Carlo
13	0	src/editor/adapters/in-memory-editor.ts
3	2	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts

--6bb442fd--2020-12-08--Nicolas Carlo
4	0	src/editor/error-reason.ts
17	0	src/refactorings/move-to-existing-file/index.ts
48	0	src/refactorings/move-to-existing-file/move-to-existing-file.test.ts
59	0	src/refactorings/move-to-existing-file/move-to-existing-file.ts

--ea5c91b3--2021-01-01--Nicolas Carlo
--3cce863f--2021-01-01--dependabot-preview[bot]
1	1	package.json
129	218	yarn.lock

--48e1ffb4--2021-01-01--Nicolas Carlo
--a76c64cf--2021-01-01--Nicolas Carlo
--aba9afa6--2021-01-01--Nicolas Carlo
--4676920a--2021-01-01--Nicolas Carlo
--c4f1cafb--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--d1f0f002--2021-01-01--Nicolas Carlo
--4efee3e3--2021-01-01--Nicolas Carlo
--31a6603d--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--4aa8f1d0--2021-01-01--Nicolas Carlo
--8e5312c6--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--61c5eaee--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--b3eb961a--2021-01-01--Nicolas Carlo
--888022bc--2021-01-01--Nicolas Carlo
--9b091064--2021-01-01--dependabot-preview[bot]
1	1	package.json
106	106	yarn.lock

--4321d580--2021-01-01--dependabot-preview[bot]
1	1	package.json
8	33	yarn.lock

--0da4d67e--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--66aa7999--2021-01-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--1e6292ee--2021-01-01--dependabot-preview[bot]
1	1	package.json
6	42	yarn.lock

--47deecaf--2020-12-21--Nicolas Carlo
--42907853--2020-12-21--dependabot-preview[bot]
23	9	yarn.lock

--f43fb2f5--2020-12-12--Nicolas Carlo
2	3	package.json
12	179	yarn.lock

--4fae3fd4--2020-12-11--Nicolas Carlo
--917624fd--2020-12-10--dependabot-preview[bot]
3	3	yarn.lock

--2583c254--2020-12-07--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--7ab3c20f--2020-12-07--Nicolas Carlo
2	0	CHANGELOG.md
9	2	src/ast/scope.ts
4	0	src/editor/error-reason.ts
23	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
28	3	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--ecaffddc--2020-12-01--Nicolas Carlo
--be546d34--2020-12-01--dependabot-preview[bot]
1	1	package.json
5	23	yarn.lock

--b8c93345--2020-12-01--Nicolas Carlo
--016c4a25--2020-12-01--Nicolas Carlo
--2aceb9a9--2020-12-01--Nicolas Carlo
--90b43f22--2020-12-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--dcf3a0da--2020-12-01--dependabot-preview[bot]
1	1	package.json
383	323	yarn.lock

--ef3400bd--2020-12-01--Nicolas Carlo
--6607857c--2020-12-01--Nicolas Carlo
--b6125b57--2020-12-01--dependabot-preview[bot]
1	1	package.json
1	6	yarn.lock

--f2a67434--2020-12-01--dependabot-preview[bot]
1	1	package.json
6	23	yarn.lock

--46802674--2020-12-01--Nicolas Carlo
--4533030e--2020-12-01--Nicolas Carlo
--02ccb9e7--2020-12-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--898ea1fa--2020-12-01--dependabot-preview[bot]
1	1	package.json
35	19	yarn.lock

--47d849be--2020-12-01--dependabot-preview[bot]
1	1	package.json
16	16	yarn.lock

--d40ba66d--2020-11-29--Nicolas Carlo
38	0	CHANGELOG.md

--147f8ef5--2020-11-29--Nicolas Carlo
6	1	src/ast/scope.ts
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
22	3	src/refactorings/extract/extract-variable/variable.ts

--18c237ed--2020-11-29--Nicolas Carlo
6	10	src/refactorings/extract/extract-variable/occurrence.ts
18	18	src/refactorings/extract/extract-variable/variable.ts

--021af2cd--2020-11-28--Nicolas Carlo
12	0	CHANGELOG.md
9	6	src/ast/scope.ts
7	11	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts

--95ba141c--2020-11-28--Nicolas Carlo
1	1	CHANGELOG.md

--34e14478--2020-11-28--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--be009867--2020-11-28--Nicolas Carlo
--3423e98d--2020-11-28--Nicolas Carlo
7	1	CHANGELOG.md
4	0	README.md

--618894df--2020-11-28--Nicolas Carlo
140	1	README.md

--127c79e8--2020-11-28--Nicolas Carlo
5	7	README.md
-	-	docs/demo/extension.gif

--9851adcd--2020-11-28--Nicolas Carlo
86	107	src/extension.ts

--89066b77--2020-11-28--Nicolas Carlo
0	30	package.json

--45584fd1--2020-11-26--Nicolas Carlo
11	1	src/action-providers.ts

--0b8de8c2--2020-11-25--Nicolas Carlo
170	0	package.json

--8eff048a--2020-11-25--Nicolas Carlo
4	0	CHANGELOG.md
26	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
22	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--06e6175c--2020-11-19--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--6462b828--2020-11-19--Nicolas Carlo
2	0	CHANGELOG.md
6	3	README.md
5	0	package.json

--88fef3f3--2020-11-19--Nicolas Carlo
50	0	src/refactorings/rename-symbol/rename-symbol.test.ts
13	0	src/refactorings/rename-symbol/rename-symbol.ts

--bec7e534--2020-11-17--Nicolas Carlo
--425ef02f--2020-11-18--allcontributors[bot]
9	0	.all-contributorsrc

--39ad2620--2020-11-18--allcontributors[bot]
2	1	README.md

--aa5708e4--2020-11-17--Nicolas Carlo
6	0	CHANGELOG.md
109	1	src/refactorings/rename-symbol/rename-symbol.test.ts
51	2	src/refactorings/rename-symbol/rename-symbol.ts

--acf84ba5--2020-11-17--Nicolas Carlo
52	0	src/editor/adapters/attempting-editor.test.ts
67	0	src/editor/adapters/attempting-editor.ts
0	52	src/editor/attempting-editor.test.ts
0	67	src/editor/attempting-editor.ts
1	1	src/refactorings/extract/index.ts
1	1	src/refactorings/inline/index.ts

--214f772c--2020-11-17--Nicolas Carlo
4	0	src/editor/adapters/in-memory-editor.ts
4	0	src/editor/adapters/vscode-editor.ts
4	0	src/editor/attempting-editor.ts
1	0	src/editor/editor.ts

--5db61ba1--2020-11-17--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts
1	1	src/editor/adapters/vscode-editor.ts
2	2	src/editor/attempting-editor.ts
1	1	src/editor/editor.ts
4	4	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts
7	7	src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts
7	7	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
13	13	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
1	1	src/refactorings/extract/extract-variable/occurrence.ts
1	1	src/refactorings/extract/replacement-strategy.ts
2	2	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
2	2	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts

--7e337154--2020-11-17--Nicolas Carlo
9	1	src/editor/adapters/vue-vscode-editor.ts

--5c247171--2020-11-17--Nicolas Carlo
3	2	src/editor/adapters/in-memory-editor.ts
3	1	src/editor/adapters/vscode-editor.ts
1	1	src/editor/attempting-editor.ts
7	1	src/editor/editor.ts

--20c6ee7c--2020-11-16--Nicolas Carlo
1	0	src/extension.ts
2	0	src/refactorings/extract-class/extract-class-action-provider.ts
2	0	src/refactorings/extract-class/extract-class-command.ts
2	0	src/refactorings/extract-class/extract-class.ts

--7f3683dc--2020-11-16--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/extract-class/ClassNode.ts
0	452	src/refactorings/extract-class/ClassRefactor.test.ts
0	131	src/refactorings/extract-class/ClassRefactor.ts
0	67	src/refactorings/extract-class/ExtractClassActionProvider.ts
0	152	src/refactorings/extract-class/ExtractClassCommand.ts
0	9	src/refactorings/extract-class/TextMatcher.test.ts
0	19	src/refactorings/extract-class/TextMatcher.ts
0	16	src/refactorings/extract-class/TypescriptClassNode.test.ts
0	161	src/refactorings/extract-class/TypescriptClassNode.ts
0	96	src/refactorings/extract-class/UmlNotation.test.ts
0	98	src/refactorings/extract-class/UmlNotation.ts
3	0	src/refactorings/extract-class/class-name-matcher.ts
17	0	src/refactorings/extract-class/class-node.ts
452	0	src/refactorings/extract-class/class-refactor.test.ts
131	0	src/refactorings/extract-class/class-refactor.ts
0	3	src/refactorings/extract-class/classNameMatcher.ts
67	0	src/refactorings/extract-class/extract-class-action-provider.ts
152	0	src/refactorings/extract-class/extract-class-command.ts
8	0	src/refactorings/extract-class/format-ts.ts
0	8	src/refactorings/extract-class/formatTs.ts
6	0	src/refactorings/extract-class/parse-class-declaration.ts
0	6	src/refactorings/extract-class/parseClassDeclaration.ts
9	0	src/refactorings/extract-class/text-matcher.test.ts
19	0	src/refactorings/extract-class/text-matcher.ts
16	0	src/refactorings/extract-class/typescript-class-node.test.ts
161	0	src/refactorings/extract-class/typescript-class-node.ts
96	0	src/refactorings/extract-class/uml-notation.test.ts
98	0	src/refactorings/extract-class/uml-notation.ts

--f6b2d1fe--2020-11-16--Nicolas Carlo
12	12	src/refactorings/extract-class/ClassNode.ts
278	278	src/refactorings/extract-class/ClassRefactor.test.ts
75	67	src/refactorings/extract-class/UmlNotation.test.ts
88	77	src/refactorings/extract-class/UmlNotation.ts
7	4	src/refactorings/extract-class/utils/assert.ts

--31d04715--2020-11-16--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--2c2ccdac--2020-11-16--Nicolas Carlo
--83d68412--2020-11-17--allcontributors[bot]
9	0	.all-contributorsrc

--86b67f97--2020-11-17--allcontributors[bot]
2	2	README.md

--c4cede6e--2020-11-16--Nicolas Carlo
28	0	CHANGELOG.md
7	2	src/ast/scope.ts
18	0	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts

--cac6d258--2020-11-13--Nicolas Carlo
1	1	jest.config.js

--ebfdab79--2020-11-13--Nicolas Carlo
1	1	src/refactorings/negate-expression/negate-expression.ts

--4b3f1209--2020-11-12--Nicolas Carlo
1	0	CHANGELOG.md
3	1	src/refactorings/negate-expression/index.ts
2	3	src/refactorings/negate-expression/negate-expression.ts

--f498ead6--2020-11-12--Nicolas Carlo
13	0	CHANGELOG.md
5	1	src/refactorings/inline/inline-variable/find-inlinable-code.ts
7	0	src/refactorings/inline/inline-variable/inline-variable.test.ts

--04fa63eb--2020-11-05--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--259780c1--2020-11-05--Nicolas Carlo
1	0	CHANGELOG.md
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
1	5	src/refactorings/extract/extract-variable/occurrence.ts

--bf971686--2020-10-23--Nicolas Carlo
4	0	CHANGELOG.md
5	0	src/refactorings/flip-ternary/flip-ternary.test.ts
4	1	src/refactorings/flip-ternary/flip-ternary.ts

--27cd05b7--2020-11-01--Nicolas Carlo
--6f8ea0fb--2020-11-01--Nicolas Carlo
--146b03e8--2020-11-01--Nicolas Carlo
--d6236bf5--2020-11-01--dependabot-preview[bot]
1	1	package.json
29	41	yarn.lock

--0cf9d4bb--2020-11-01--Nicolas Carlo
--864f1318--2020-11-01--Nicolas Carlo
--66cee687--2020-11-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--8376b77d--2020-11-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--329603cd--2020-11-01--Nicolas Carlo
--2ddd180a--2020-11-01--Nicolas Carlo
--ec693cdf--2020-11-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--a4809e19--2020-11-01--dependabot-preview[bot]
1	1	package.json
9	10	yarn.lock

--9b4a1b03--2020-11-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--184d851a--2020-11-01--dependabot-preview[bot]
1	1	package.json
32	69	yarn.lock

--60aad0ee--2020-10-22--Nicolas Carlo
40	0	docs/adr/0009-use-custom-github-actions-to-deploy.md

--2883dafc--2020-10-22--Nicolas Carlo
1	1	package.json

--3015ae94--2020-10-22--Nicolas Carlo
1	1	package.json

--2658b9fd--2020-10-22--Nicolas Carlo
1	0	package.json
40	1	yarn.lock

--eb9d0443--2020-10-22--Nicolas Carlo
8	1	CHANGELOG.md
1	1	package.json

--1fa09f84--2020-10-22--Nicolas Carlo
21	0	.github/workflows/ovsx-deploy.yml

--774be7d1--2020-10-22--Nicolas Carlo
3	2	package.json

--6351c9df--2020-10-22--Nicolas Carlo
1	1	.github/workflows/vscode-deploy.yml
2	1	package.json

--99267e02--2020-10-22--Nicolas Carlo
3	1	.github/workflows/vscode-deploy.yml

--d8063939--2020-10-22--Nicolas Carlo
1	1	.github/workflows/vscode-deploy.yml

--198513ef--2020-10-22--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--78356664--2020-10-22--Nicolas Carlo
21	0	.github/workflows/vscode-deploy.yml

--2f19cda6--2020-10-22--Nicolas Carlo
4	0	CHANGELOG.md

--98713d9f--2020-10-17--Nicolas Carlo
1	1	yarn.lock

--8e758cc6--2020-10-17--Nicolas Carlo
1	1	package.json

--4724ad96--2020-10-22--Nicolas Carlo
--dda30a32--2020-10-20--justerest
16	0	src/refactorings/extract-class/TypescriptClassNode.test.ts
7	3	src/refactorings/extract-class/TypescriptClassNode.ts

--3a7396b6--2020-10-17--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--6c3fc079--2020-10-17--Nicolas Carlo
2	2	README.md

--a16097d4--2020-10-17--Nicolas Carlo
16	0	README.md
-	-	docs/demo/extract-class.gif
159	145	src/refactorings/extract-class/ClassRefactor.test.ts

--f5d75ef7--2020-10-16--Nicolas Carlo
4	0	CHANGELOG.md

--96fa728c--2020-10-16--Nicolas Carlo
26	0	package.json

--cf7f9100--2020-10-16--Nicolas Carlo
1	1	src/refactorings/extract-class/extract-class.test.ts
2	2	src/refactorings/extract-class/extract-class.ts

--ced1a348--2020-10-16--Nicolas Carlo
--15623955--2020-10-16--Nicolas Carlo
--13676c87--2020-10-16--Nicolas Carlo
--22802be5--2020-10-17--allcontributors[bot]
10	0	.all-contributorsrc

--8532db4f--2020-10-17--allcontributors[bot]
2	1	README.md

--ef045103--2020-10-14--Nicolas Carlo
--47169c83--2020-10-14--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--bee84490--2020-10-14--Nicolas Carlo
--a3b38111--2020-10-14--Nicolas Carlo
--bcfb662c--2020-10-14--Nicolas Carlo
2	1	package.json

--ee4e1ffe--2020-10-14--dependabot-preview[bot]
1	1	package.json
35	302	yarn.lock

--f2cee63d--2020-10-14--Nicolas Carlo
--4b70bacc--2020-10-14--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--79931485--2020-10-14--Nicolas Carlo
--377a01ce--2020-10-14--Nicolas Carlo
2	2	src/refactorings/extract/extract-variable/variable.ts

--c8ffffc2--2020-10-14--Nicolas Carlo
1	1	package.json
379	331	yarn.lock

--5a520631--2020-10-01--dependabot-preview[bot]
1	1	package.json
4	12	yarn.lock

--12fee539--2020-10-14--Nicolas Carlo
--eab245dd--2020-10-12--Nicolas Carlo
--c35fb9b1--2020-10-12--Nicolas Carlo
24	0	CHANGELOG.md

--a38ae10a--2020-10-11--Nicolas Carlo
7	3	src/ast/identity.ts
7	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.ts

--690332f0--2020-10-11--Nicolas Carlo
13	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.ts
15	3	src/refactorings/extract/extract-variable/occurrence.ts

--c6a27824--2020-09-10--Nicolas Carlo
2	2	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts

--959cbb51--2020-09-10--Nicolas Carlo
6	3	src/refactorings/extract/extract-variable/extract-variable.ts

--d9fcc2e4--2020-09-10--Nicolas Carlo
7	7	src/refactorings/extract/extract-variable/occurrence.ts

--ac83ec3e--2020-09-10--Nicolas Carlo
6	0	src/refactorings/extract/extract-variable/destructure-strategy.ts
30	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
2	0	src/refactorings/extract/extract-variable/extract-variable.ts
21	1	src/refactorings/extract/extract-variable/occurrence.ts

--82b44c39--2020-10-11--Nicolas Carlo
4	0	CHANGELOG.md
6	1	src/refactorings/extract/extract-variable/variable.ts

--b5550e55--2020-10-06--justerest
16	3	src/extension.ts
2	1	src/refactorings/extract-class/EXTRACT_CLASS_COMMAND.ts
3	3	src/refactorings/extract-class/ExtractClassActionProvider.ts
3	6	src/refactorings/extract-class/index.ts

--79f0477c--2020-10-06--justerest
2	1	package.json
4	0	src/editor/error-reason.ts
3	0	src/extension.ts
17	0	src/refactorings/extract-class/ClassNode.ts
438	0	src/refactorings/extract-class/ClassRefactor.test.ts
131	0	src/refactorings/extract-class/ClassRefactor.ts
1	0	src/refactorings/extract-class/EXTRACT_CLASS_COMMAND.ts
67	0	src/refactorings/extract-class/ExtractClassActionProvider.ts
152	0	src/refactorings/extract-class/ExtractClassCommand.ts
9	0	src/refactorings/extract-class/TextMatcher.test.ts
19	0	src/refactorings/extract-class/TextMatcher.ts
157	0	src/refactorings/extract-class/TypescriptClassNode.ts
88	0	src/refactorings/extract-class/UmlNotation.test.ts
87	0	src/refactorings/extract-class/UmlNotation.ts
3	0	src/refactorings/extract-class/classNameMatcher.ts
31	0	src/refactorings/extract-class/extract-class.test.ts
34	0	src/refactorings/extract-class/extract-class.ts
8	0	src/refactorings/extract-class/formatTs.ts
20	0	src/refactorings/extract-class/index.ts
6	0	src/refactorings/extract-class/parseClassDeclaration.ts
5	0	src/refactorings/extract-class/utils/assert.ts
1	1	tsconfig.json
78	0	yarn.lock

--e6259f5f--2020-10-01--Nicolas Carlo
--ac5336be--2020-10-01--Nicolas Carlo
--7411f932--2020-10-01--Nicolas Carlo
--f09384ef--2020-10-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--e8776e53--2020-10-01--dependabot-preview[bot]
1	1	package.json
17	9	yarn.lock

--40fe8a72--2020-10-01--dependabot-preview[bot]
1	1	package.json
13	25	yarn.lock

--7306a550--2020-10-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--8800f66a--2020-10-01--dependabot-preview[bot]
1	1	package.json
132	2	yarn.lock

--e5aeddaf--2020-09-10--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--47c75d7b--2020-09-10--Nicolas Carlo
--9d330a43--2020-09-10--allcontributors[bot]
9	0	.all-contributorsrc

--22ded0a9--2020-09-10--allcontributors[bot]
4	1	README.md

--9e77f316--2020-09-10--Nicolas Carlo
27	0	CHANGELOG.md

--0cfe0576--2020-09-10--Nicolas Carlo
36	0	src/ast/transformation.test.ts
8	1	src/ast/transformation.ts
9	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts

--967a68d5--2020-09-10--Nicolas Carlo
1	1	src/action-providers.ts
1	1	src/ast/export-default-workaround.ts
4	4	src/ast/transformation.ts
1	1	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts

--586a25b6--2020-09-10--Nicolas Carlo
23	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
4	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts

--060133af--2020-09-05--Nicolas Carlo
--92b88ee5--2020-09-05--Nicolas Carlo
--d9c04f58--2020-09-05--allcontributors[bot]
9	0	.all-contributorsrc

--54fe6b67--2020-09-05--allcontributors[bot]
2	2	README.md

--ddeaf8b9--2020-09-05--Nicolas Carlo
4	0	CHANGELOG.md

--50df34bf--2020-09-05--Nicolas Carlo
1	1	README.md

--f4e44bdc--2020-09-05--Nicolas Carlo
2	1	package.json

--aa886154--2020-09-05--Nicolas Carlo
132	0	package.json

--a3082626--2020-09-05--Nicolas Carlo
4	7	src/action-providers.ts

--1fc48864--2020-09-05--Nicolas Carlo
32	23	src/editor/adapters/vue-vscode-editor.ts

--9185b732--2020-09-05--Nicolas Carlo
3	6	src/refactorings/extract/index.ts
3	6	src/refactorings/inline/index.ts

--e49a9fd9--2020-09-05--Nicolas Carlo
52	0	src/editor/attempting-editor.test.ts
63	0	src/editor/attempting-editor.ts
10	19	src/refactorings/extract/index.ts
9	18	src/refactorings/inline/index.ts

--1d3b9240--2020-09-05--Nicolas Carlo
4	4	src/refactorings/extract/index.ts
5	5	src/refactorings/inline/index.ts

--e359d769--2020-09-05--Nicolas Carlo
13	12	src/editor/adapters/vue-vscode-editor.ts

--6b05a7d1--2020-09-05--Nicolas Carlo
37	21	src/editor/adapters/vue-vscode-editor.ts

--5f3d58e2--2020-09-05--Nicolas Carlo
12	0	src/editor/adapters/vue-vscode-editor.ts

--f370b4a0--2020-09-05--Nicolas Carlo
20	25	src/editor/adapters/vue-vscode-editor.ts

--a05567c6--2020-09-05--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts
18	2	src/editor/adapters/vue-vscode-editor.ts

--d9a7f749--2020-09-05--Nicolas Carlo
8	7	src/editor/adapters/vscode-editor.ts

--114bca4b--2020-09-05--Nicolas Carlo
1	2	src/editor/adapters/create-vscode-editor.ts
19	146	src/editor/adapters/vue-vscode-editor.ts

--0f21e8da--2020-09-05--Nicolas Carlo
26	14	src/editor/adapters/vue-vscode-editor.ts

--78dadfa5--2020-09-05--Nicolas Carlo
2	2	src/editor/adapters/vue-vscode-editor.ts
4	0	src/editor/position.ts
1	1	src/refactorings/move-statement-up/move-statement-up.ts

--089e575c--2020-09-05--Nicolas Carlo
6	10	src/editor/adapters/vue-vscode-editor.ts

--79c2970a--2020-09-05--Nicolas Carlo
2	2	src/editor/adapters/vue-vscode-editor.ts

--f53a4eeb--2020-09-05--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts

--be8633b7--2020-09-05--Nicolas Carlo
4	15	src/action-providers.ts
3	9	src/commands.ts
16	0	src/editor/adapters/create-vscode-editor.ts

--bb5aac6b--2020-09-05--Nicolas Carlo
0	2	src/commands.ts

--44498aae--2020-09-05--Nicolas Carlo
22	7	src/action-providers.ts

--4d3c9ea8--2020-09-05--Nicolas Carlo
1	5	src/action-providers.ts

--29bbf38a--2020-09-04--Nicolas Carlo
2	1	package.json
8	0	src/commands.ts
2	1	src/extension.ts

--3c79ffc3--2020-09-04--Nicolas Carlo
168	0	src/editor/adapters/vue-vscode-editor.ts

--a97df019--2020-09-04--Nicolas Carlo
2	1	.vscode/settings.json

--9683cbaa--2020-09-02--Nicolas Carlo
--dffa5bc5--2020-09-02--Nicolas Carlo
3	3	package.json
691	589	yarn.lock

--32512d51--2020-09-02--Nicolas Carlo
8	1	jest.config.js

--136959e1--2020-09-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--ef7ad456--2020-09-02--Nicolas Carlo
2	4	_templates/refactoring/new/test.ejs.t

--97f730b3--2020-09-02--Nicolas Carlo
--d37a7c15--2020-09-01--Nicolas Carlo
--fb06fd12--2020-09-01--Nicolas Carlo
2	1	package.json

--f9475587--2020-09-01--dependabot-preview[bot]
1	1	package.json
95	63	yarn.lock

--61e6807d--2020-09-01--Nicolas Carlo
--dc0dfb1d--2020-09-01--Nicolas Carlo
--869a72bd--2020-09-01--dependabot-preview[bot]
1	1	package.json
54	65	yarn.lock

--eb0d1373--2020-09-01--Nicolas Carlo
--f00ed56a--2020-09-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--73821905--2020-09-01--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--7f0ed5a7--2020-09-01--dependabot-preview[bot]
1	1	package.json
4	9	yarn.lock

--c4e3eb79--2020-08-28--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--1e4fd6cd--2020-08-27--Nicolas Carlo
2	1	CHANGELOG.md

--c81c9c01--2020-08-27--Nicolas Carlo
38	0	src/refactorings/move-statement-down/move-statement-down.test.ts
36	0	src/refactorings/move-statement-up/move-statement-up.test.ts
20	0	src/refactorings/move-statement-up/move-statement-up.ts

--1e781c0f--2020-08-27--Nicolas Carlo
11	11	src/refactorings/move-statement-down/move-statement-down.ts
26	0	src/refactorings/move-statement-up/move-statement-up.test.ts
2	22	src/refactorings/move-statement-up/move-statement-up.ts

--9e65f619--2020-08-27--Nicolas Carlo
40	0	src/refactorings/move-statement-down/move-statement-down.test.ts
14	2	src/refactorings/move-statement-down/move-statement-down.ts
40	0	src/refactorings/move-statement-up/move-statement-up.test.ts
14	2	src/refactorings/move-statement-up/move-statement-up.ts

--558a8115--2020-08-26--Nicolas Carlo
1	1	src/refactorings/lift-up-conditional/index.ts

--1f9e114b--2020-08-26--Nicolas Carlo
1	1	src/refactorings/move-statement-down/move-statement-down.test.ts

--3ffd9a37--2020-08-26--Nicolas Carlo
2	0	CHANGELOG.md
4	4	README.md
-	-	docs/demo/bubble-up-if-statement.gif
-	-	docs/demo/lift-up-conditional.gif
22	22	package.json
1	1	src/editor/error-reason.ts
3	3	src/extension.ts
0	287	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
0	121	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
0	21	src/refactorings/bubble-up-if-statement/index.ts
18	0	src/refactorings/lift-up-conditional/index.ts
287	0	src/refactorings/lift-up-conditional/lift-up-conditional.test.ts
121	0	src/refactorings/lift-up-conditional/lift-up-conditional.ts

--bb8f633e--2020-08-26--Nicolas Carlo
1	0	CHANGELOG.md

--62269bfc--2020-08-26--Nicolas Carlo
2	2	src/refactorings/move-statement-up/move-statement-up.ts

--756b7ad9--2020-08-26--Nicolas Carlo
24	0	src/refactorings/move-statement-down/move-statement-down.test.ts
14	3	src/refactorings/move-statement-down/move-statement-down.ts

--b136c05e--2020-08-26--Nicolas Carlo
8	0	src/refactorings/move-statement-up/move-statement-up.test.ts
5	1	src/refactorings/move-statement-up/move-statement-up.ts

--27f4996c--2020-08-26--Nicolas Carlo
4	6	src/refactorings/move-statement-up/move-statement-up.ts

--d333b225--2020-08-26--Nicolas Carlo
16	0	src/refactorings/move-statement-up/move-statement-up.test.ts
9	2	src/refactorings/move-statement-up/move-statement-up.ts

--f8c6c492--2020-08-26--Nicolas Carlo
1	3	.vscode/settings.json

--b8772f34--2020-08-21--Nicolas Carlo
8	11	src/editor/selection.ts
10	10	src/refactorings/inline/find-exported-id-names.ts
39	44	src/refactorings/inline/inline-function/find-param-matching-id.ts
40	40	src/refactorings/inline/inline-function/inline-function.ts
4	8	src/refactorings/inline/inline-variable/find-inlinable-code.test.ts
71	71	src/refactorings/inline/inline-variable/find-inlinable-code.ts
6	6	src/refactorings/inline/inline-variable/inline-variable.ts
15	15	src/refactorings/move-statement-down/move-statement-down.ts
14	14	src/refactorings/move-statement-up/move-statement-up.ts

--08ac7816--2020-08-21--Nicolas Carlo
--2d4e30e3--2020-08-21--Nicolas Carlo
9	0	CONTRIBUTING.md

--43eb026d--2020-08-21--Nicolas Carlo
2	7	src/action-providers.ts
2	12	src/commands.ts
2	39	src/types.ts

--386b31f7--2020-08-21--Nicolas Carlo
3	11	src/extension.ts
2	2	src/refactorings/split-if-statement/index.ts
22	40	src/refactorings/split-if-statement/split-if-statement.test.ts
3	6	src/refactorings/split-if-statement/split-if-statement.ts

--2657dd70--2020-08-21--Nicolas Carlo
3	2	src/extension.ts
2	2	src/refactorings/split-declaration-and-initialization/index.ts
19	35	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
3	6	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--d3f4a04d--2020-08-21--Nicolas Carlo
3	6	src/extension.ts
2	2	src/refactorings/simplify-ternary/index.ts
18	39	src/refactorings/simplify-ternary/simplify-ternary.test.ts
3	6	src/refactorings/simplify-ternary/simplify-ternary.ts

--bcc972e5--2020-08-21--Nicolas Carlo
2	2	src/extension.ts
2	2	src/refactorings/replace-binary-with-assignment/index.ts
18	35	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.test.ts
3	6	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--794aa75f--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/remove-redundant-else/index.ts
25	48	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
3	6	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--c4f0a553--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/remove-dead-code/index.ts
26	40	src/refactorings/remove-dead-code/remove-dead-code.test.ts
3	6	src/refactorings/remove-dead-code/remove-dead-code.ts

--56e02d7f--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/remove-braces-from-if-statement/index.ts
33	53	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts
3	6	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts

--bda1bf75--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/remove-braces-from-arrow-function/index.ts
40	52	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts
3	6	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--ecf697f1--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
47	66	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts
3	6	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts

--e836e4ff--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
32	49	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
2	6	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
2	2	src/refactorings/react/convert-to-pure-component/index.ts

--7df86473--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
28	48	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
3	6	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
2	2	src/refactorings/react/add-braces-to-jsx-attribute/index.ts

--3e16ef86--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/negate-expression/index.ts
69	92	src/refactorings/negate-expression/negate-expression.test.ts
2	5	src/refactorings/negate-expression/negate-expression.ts

--1724bec8--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/move-statement-up/index.ts
42	76	src/refactorings/move-statement-up/move-statement-up.test.ts
2	5	src/refactorings/move-statement-up/move-statement-up.ts

--161e41fc--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/move-statement-down/index.ts
36	75	src/refactorings/move-statement-down/move-statement-down.test.ts
2	5	src/refactorings/move-statement-down/move-statement-down.ts

--68a4fb46--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/merge-with-previous-if-statement/index.ts
38	70	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
3	6	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--019e7490--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
2	2	src/refactorings/merge-if-statements/index.ts
36	64	src/refactorings/merge-if-statements/merge-if-statements.test.ts
3	6	src/refactorings/merge-if-statements/merge-if-statements.ts

--fd1beae4--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
6	14	src/refactorings/inline/index.ts
28	43	src/refactorings/inline/inline-function/inline-function.test.ts
2	5	src/refactorings/inline/inline-function/inline-function.ts
33	47	src/refactorings/inline/inline-variable/inline-variable.array-pattern.test.ts
39	56	src/refactorings/inline/inline-variable/inline-variable.object-pattern.test.ts
110	123	src/refactorings/inline/inline-variable/inline-variable.test.ts
2	5	src/refactorings/inline/inline-variable/inline-variable.ts

--4b11af54--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
16	35	src/refactorings/flip-ternary/flip-ternary.test.ts
3	2	src/refactorings/flip-ternary/flip-ternary.ts
2	2	src/refactorings/flip-ternary/index.ts

--d0de9059--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
14	28	src/refactorings/flip-if-else/flip-if-else.test.ts
3	2	src/refactorings/flip-if-else/flip-if-else.ts
2	2	src/refactorings/flip-if-else/index.ts

--015e366f--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
15	31	src/refactorings/extract-interface/extract-interface.test.ts
3	6	src/refactorings/extract-interface/extract-interface.ts
2	2	src/refactorings/extract-interface/index.ts

--d0bdc7e0--2020-08-21--Nicolas Carlo
1	1	src/extension.ts
24	40	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts
36	57	src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts
10	30	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
3	6	src/refactorings/extract/extract-generic-type/extract-generic-type.ts
28	43	src/refactorings/extract/extract-variable/extract-variable.basic-extraction.test.ts
22	39	src/refactorings/extract/extract-variable/extract-variable.extractable-jsx.test.ts
23	49	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
15	33	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
27	38	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
61	120	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
110	95	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
13	25	src/refactorings/extract/extract-variable/extract-variable.non-extractable.test.ts
2	5	src/refactorings/extract/extract-variable/extract-variable.ts
13	19	src/refactorings/extract/extract-variable/extract-variable.variable-name.test.ts
5	11	src/refactorings/extract/index.ts

--82d21619--2020-08-21--Nicolas Carlo
25	0	src/editor/adapters/in-memory-editor.test.ts
29	9	src/editor/adapters/in-memory-editor.ts

--d030b1c2--2020-08-21--Nicolas Carlo
12	6	src/editor/adapters/in-memory-editor.ts

--58422869--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
24	35	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
3	6	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
2	2	src/refactorings/convert-to-template-literal/index.ts

--d2e0af11--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
23	49	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
3	6	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
2	2	src/refactorings/convert-ternary-to-if-else/index.ts

--f8fd518a--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
18	29	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
3	6	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
2	2	src/refactorings/convert-switch-to-if-else/index.ts

--1ac05883--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
37	61	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
3	6	src/refactorings/convert-let-to-const/convert-let-to-const.ts
2	2	src/refactorings/convert-let-to-const/index.ts

--724363e3--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
42	70	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
3	6	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
2	2	src/refactorings/convert-if-else-to-ternary/index.ts

--25530b43--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
19	31	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
3	6	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
2	2	src/refactorings/convert-if-else-to-switch/index.ts

--5f8d208f--2020-08-20--Nicolas Carlo
1	1	_templates/refactoring/new/test.ejs.t
2	2	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
1	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
1	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
1	1	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts

--92066ed2--2020-08-20--Nicolas Carlo
3	3	src/extension.ts
0	52	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.test.ts
0	58	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.ts
0	20	src/refactorings/convert-function-declaration-to-arrow-function/index.ts
52	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts
58	0	src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts
20	0	src/refactorings/convert-to-arrow-function/index.ts

--0ac29904--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
12	29	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.test.ts
4	7	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.ts
2	2	src/refactorings/convert-function-declaration-to-arrow-function/index.ts

--192cb550--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
19	36	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
3	6	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
2	2	src/refactorings/convert-for-to-foreach/index.ts

--89ec5d40--2020-08-20--Nicolas Carlo
1	1	src/extension.ts
31	48	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
3	6	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
2	2	src/refactorings/bubble-up-if-statement/index.ts

--32ed2953--2020-08-20--Nicolas Carlo
3	6	_templates/refactoring/new/refactoring.ejs.t
12	25	_templates/refactoring/new/test.ejs.t

--5a9afdbc--2020-08-20--Nicolas Carlo
5	2	src/extension.ts
25	46	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
3	6	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
2	2	src/refactorings/add-braces-to-if-statement/index.ts

--2c5eb7d2--2020-08-20--Nicolas Carlo
14	19	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts

--a42f6a33--2020-08-20--Nicolas Carlo
1	2	src/extension.ts
12	23	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts
4	6	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
2	2	src/refactorings/add-braces-to-arrow-function/index.ts

--a58435ef--2020-08-20--Nicolas Carlo
11	2	src/commands.ts
10	2	src/extension.ts
3	17	src/refactorings/rename-symbol/index.ts

--24ec7ece--2020-08-20--Nicolas Carlo
13	11	src/action-providers.ts

--3cb8cf30--2020-08-20--Nicolas Carlo
2	2	src/commands.ts
2	2	src/extension.ts

--e77ccab9--2020-08-20--Nicolas Carlo
1	3	src/commands.ts

--cbb2ddfc--2020-08-20--Nicolas Carlo
29	0	src/types.ts

--382e5903--2020-08-20--Nicolas Carlo
8	6	src/action-providers.ts
2	2	src/commands.ts
2	2	src/refactorings/add-braces-to-arrow-function/index.ts
2	2	src/refactorings/add-braces-to-if-statement/index.ts
2	2	src/refactorings/bubble-up-if-statement/index.ts
2	2	src/refactorings/convert-for-to-foreach/index.ts
2	2	src/refactorings/convert-function-declaration-to-arrow-function/index.ts
2	2	src/refactorings/convert-if-else-to-switch/index.ts
2	2	src/refactorings/convert-if-else-to-ternary/index.ts
2	2	src/refactorings/convert-let-to-const/index.ts
2	2	src/refactorings/convert-switch-to-if-else/index.ts
2	2	src/refactorings/convert-ternary-to-if-else/index.ts
2	2	src/refactorings/convert-to-template-literal/index.ts
2	2	src/refactorings/extract-interface/index.ts
2	2	src/refactorings/extract/index.ts
2	2	src/refactorings/flip-if-else/index.ts
2	2	src/refactorings/flip-ternary/index.ts
2	2	src/refactorings/inline/index.ts
2	2	src/refactorings/merge-if-statements/index.ts
2	2	src/refactorings/merge-with-previous-if-statement/index.ts
2	2	src/refactorings/move-statement-down/index.ts
2	2	src/refactorings/move-statement-up/index.ts
2	2	src/refactorings/negate-expression/index.ts
2	2	src/refactorings/react/add-braces-to-jsx-attribute/index.ts
2	2	src/refactorings/react/convert-to-pure-component/index.ts
2	2	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
2	2	src/refactorings/remove-braces-from-arrow-function/index.ts
2	2	src/refactorings/remove-braces-from-if-statement/index.ts
2	2	src/refactorings/remove-dead-code/index.ts
2	2	src/refactorings/remove-redundant-else/index.ts
2	2	src/refactorings/rename-symbol/index.ts
2	2	src/refactorings/replace-binary-with-assignment/index.ts
2	2	src/refactorings/simplify-ternary/index.ts
2	2	src/refactorings/split-declaration-and-initialization/index.ts
2	2	src/refactorings/split-if-statement/index.ts
10	6	src/types.ts

--e6830079--2020-08-20--Nicolas Carlo
1	1	src/types.ts

--875bbe96--2020-08-20--Nicolas Carlo
1	1	src/types.ts

--145376e3--2020-08-20--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts

--d2aa4a4e--2020-08-20--Nicolas Carlo
3	14	src/commands.ts
13	18	src/refactorings/extract/index.ts
13	18	src/refactorings/inline/index.ts

--3178a186--2020-08-20--Nicolas Carlo
15	0	src/editor/adapters/in-memory-editor.test.ts
18	1	src/editor/adapters/in-memory-editor.ts

--0c4de987--2020-08-20--Nicolas Carlo
5	3	src/editor/adapters/in-memory-editor.test.ts
180	186	src/editor/editor-contract-test.ts

--c610e930--2020-08-20--Nicolas Carlo
5	1	src/editor/adapters/in-memory-editor.ts
8	0	src/editor/adapters/vscode-editor.ts
2	0	src/editor/editor.ts

--53beee81--2020-08-20--Nicolas Carlo
13	0	README.md
-	-	docs/demo/convert-to-arrow-function.gif

--0b78cf38--2020-08-20--Nicolas Carlo
2	2	package.json
2	2	src/refactorings/convert-function-declaration-to-arrow-function/index.ts

--74608bd2--2020-08-20--Nicolas Carlo
11	0	CHANGELOG.md

--5e95bc9e--2020-08-20--Nicolas Carlo
--9710c068--2020-08-13--Nicolas Carlo
1	1	src/editor/adapters/vscode-editor.ts

--aa22fab8--2020-08-13--Nicolas Carlo
10	1	src/refactorings/move-statement-down/move-statement-down.test.ts
5	0	src/refactorings/move-statement-down/move-statement-down.ts

--b6eab183--2020-08-13--Nicolas Carlo
23	0	src/refactorings/move-statement-down/move-statement-down.test.ts
0	15	src/refactorings/move-statement-down/move-statement-down.ts

--d91c5ff1--2020-08-13--Nicolas Carlo
36	0	src/refactorings/move-statement-up/move-statement-up.test.ts
6	0	src/refactorings/move-statement-up/move-statement-up.ts

--436801eb--2020-08-13--Nicolas Carlo
23	0	src/refactorings/move-statement-up/move-statement-up.test.ts
0	12	src/refactorings/move-statement-up/move-statement-up.ts

--2e2bd054--2020-08-13--Nicolas Carlo
4	0	src/editor/selection.ts

--1ae228fa--2020-08-13--Nicolas Carlo
--286779bd--2020-08-06--Nicolas Carlo
--c31b141f--2020-08-06--dependabot-preview[bot]
1	1	package.json
144	690	yarn.lock

--1ada11ad--2020-08-06--Nicolas Carlo
--f696eb31--2020-08-06--dependabot-preview[bot]
1	1	package.json
268	217	yarn.lock

--06e174bf--2020-08-06--Nicolas Carlo
--6bc8b102--2020-08-02--Nicolas Carlo
--26d359a8--2020-08-01--dependabot-preview[bot]
1	1	package.json
8	54	yarn.lock

--b5ecdf97--2020-08-01--Nicolas Carlo
--cc889876--2020-08-01--dependabot-preview[bot]
1	1	package.json
614	474	yarn.lock

--9f7411d2--2020-08-01--dependabot-preview[bot]
1	1	package.json
78	141	yarn.lock

--ddbcf97d--2020-08-01--Nicolas Carlo
--3f8c2311--2020-08-01--Nicolas Carlo
--d3c9fb4e--2020-08-01--dependabot-preview[bot]
1	1	package.json
6	6	yarn.lock

--273c1f9b--2020-08-01--dependabot-preview[bot]
1	1	package.json
8	51	yarn.lock

--2bc07936--2020-07-30--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--562ab4ce--2020-07-30--Nicolas Carlo
--30e2579b--2020-07-29--dependabot-preview[bot]
6	6	yarn.lock

--0d46f198--2020-07-18--Nicolas Carlo
4	5	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--60fb0d70--2020-07-18--Nicolas Carlo
2	1	src/ast/transformation.ts
1	13	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--5a681a30--2020-07-18--Nicolas Carlo
1	0	CHANGELOG.md
55	0	src/ast/export-default-workaround.ts
1	0	src/ast/index.ts
49	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
7	1	src/refactorings/extract/extract-variable/extract-variable.ts

--505c76d7--2020-07-18--Nicolas Carlo
1	0	src/ast/index.ts

--ebb2f9e1--2020-07-19--Nicolas Carlo
--d0979d15--2020-07-17--dependabot[bot]
3	3	yarn.lock

--67a15c38--2020-07-11--Nicolas Carlo
1	3	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--24edb69c--2020-07-11--Nicolas Carlo
12	0	src/ast/domain.ts
3	15	src/refactorings/flip-if-else/flip-if-else.ts

--7a40ffe7--2020-07-11--Nicolas Carlo
9	9	src/refactorings/flip-if-else/flip-if-else.ts

--0da7b92b--2020-07-11--Nicolas Carlo
31	0	CHANGELOG.md
14	0	src/refactorings/flip-if-else/flip-if-else.test.ts
26	6	src/refactorings/flip-if-else/flip-if-else.ts

--1695f6a0--2020-07-11--Nicolas Carlo
7	3	src/refactorings/flip-if-else/flip-if-else.test.ts

--620eaf1f--2020-07-11--Nicolas Carlo
15	18	src/refactorings/negate-expression/negate-expression.ts

--efc766be--2020-07-11--Nicolas Carlo
18	0	CHANGELOG.md
6	0	src/refactorings/negate-expression/negate-expression.test.ts
8	0	src/refactorings/negate-expression/negate-expression.ts

--94bced6d--2020-07-11--Nicolas Carlo
0	5	src/refactorings/negate-expression/negate-expression.test.ts

--a2e7900e--2020-07-11--Nicolas Carlo
2	6	src/refactorings/negate-expression/negate-expression.test.ts

--1843ef7b--2020-07-11--Nicolas Carlo
0	3	src/refactorings/merge-if-statements/merge-if-statements.ts
2	1	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts
0	3	src/refactorings/remove-dead-code/remove-dead-code.ts
4	5	src/refactorings/simplify-ternary/simplify-ternary.ts

--ad9e41e2--2020-07-11--Nicolas Carlo
4	0	CHANGELOG.md
4	7	src/action-providers.ts

--3e107e54--2020-07-11--Nicolas Carlo
42	3	src/refactorings/negate-expression/negate-expression.ts

--522781e2--2020-07-11--Nicolas Carlo
7	0	src/action-providers.ts

--93e7fc79--2020-07-11--Nicolas Carlo
1	1	src/action-providers.ts
1	1	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--79e6df70--2020-07-11--Nicolas Carlo
4	0	CHANGELOG.md
1	12	src/ast/identity.ts
10	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts

--d3cd8165--2020-07-10--Nicolas Carlo
1	1	README.md

--5c0c22b0--2020-07-10--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--c32c4b92--2020-07-10--Nicolas Carlo
--09b7e10a--2020-07-10--Nicolas Carlo
2	0	CHANGELOG.md

--b0fe3527--2020-07-10--Nicolas Carlo
26	5	src/ast/scope.ts
59	2	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
8	13	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--70bbff26--2020-07-09--Nicolas Carlo
2	73	src/refactorings/extract/extract-variable/extract-variable.ts
74	0	src/refactorings/extract/extract-variable/variable-declaration-modification.ts

--b2225d1c--2020-07-09--Nicolas Carlo
4	11	src/refactorings/extract/extract-variable/extract-variable.ts
8	0	src/refactorings/extract/extract-variable/occurrence.ts

--719fe309--2020-07-09--Nicolas Carlo
1	2	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
3	7	src/refactorings/extract/extract-variable/extract-variable.ts

--88296d86--2020-07-09--Nicolas Carlo
26	7	src/refactorings/extract/extract-variable/extract-variable.ts
0	34	src/refactorings/extract/extract-variable/occurrence.ts

--37d416e0--2020-07-09--Nicolas Carlo
39	2	src/refactorings/extract/extract-variable/extract-variable.ts
16	36	src/refactorings/extract/extract-variable/occurrence.ts

--99dbe4cd--2020-07-09--Nicolas Carlo
29	9	src/refactorings/extract/extract-variable/extract-variable.ts

--d60edf77--2020-07-09--Nicolas Carlo
15	1	src/ast/scope.ts
8	5	src/refactorings/extract/extract-variable/occurrence.ts

--6468386d--2020-07-09--Nicolas Carlo
3	18	src/refactorings/extract/extract-variable/extract-variable.ts
19	2	src/refactorings/extract/extract-variable/occurrence.ts

--74520960--2020-07-09--Nicolas Carlo
4	0	src/editor/selection.ts
5	7	src/refactorings/extract/extract-variable/extract-variable.ts
4	4	src/refactorings/extract/extract-variable/occurrence.ts

--7e1b78c0--2020-07-09--Nicolas Carlo
30	0	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
20	1	src/refactorings/extract/extract-variable/extract-variable.ts

--62f46e9b--2020-07-09--Nicolas Carlo
6	6	CHANGELOG.md

--0e55d155--2020-07-08--Nicolas Carlo
--47b70498--2020-07-08--Nicolas Carlo
--379813ff--2020-07-08--Nicolas Carlo
8	7	src/action-providers.ts
2	2	src/ast/domain.ts
2	2	src/ast/identity.ts
5	5	src/ast/scope.ts
2	2	src/ast/switch.test.ts
1	1	src/ast/transformation.ts
3	3	src/editor/adapters/in-memory-editor.ts
12	9	src/editor/editor-contract-test.ts
3	3	src/extension.ts
1	1	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
1	1	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
2	2	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
1	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
1	1	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
4	4	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
8	10	src/refactorings/extract-interface/extract-interface.ts
5	5	src/refactorings/extract/extract-generic-type/extract-generic-type.ts
2	2	src/refactorings/extract/extract-variable/extract-variable.ts
2	4	src/refactorings/extract/extract-variable/occurrence.ts
2	2	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/inline/find-exported-id-names.ts
4	4	src/refactorings/inline/inline-function/find-param-matching-id.ts
4	4	src/refactorings/inline/inline-function/inline-function.ts
2	2	src/refactorings/inline/inline-variable/find-inlinable-code.ts
1	1	src/refactorings/inline/inline-variable/inline-variable.ts
1	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
1	1	src/refactorings/negate-expression/negate-expression.ts
1	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
1	1	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts
1	1	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts
1	1	src/tests-helpers.ts

--9152793f--2020-07-06--Nicolas Carlo
4	1	.vscode/settings.json

--fb3b851b--2020-07-06--Nicolas Carlo
4	0	CHANGELOG.md
1	1	src/action-providers.ts

--c12ce6f6--2020-07-03--Nicolas Carlo
--ac66688e--2020-07-02--dependabot-preview[bot]
1	1	package.json
79	83	yarn.lock

--729ad230--2020-07-02--dependabot-preview[bot]
1	1	package.json
34	75	yarn.lock

--2bf67767--2020-07-02--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--74389763--2020-07-02--Nicolas Carlo
--7b000ef2--2020-07-02--Nicolas Carlo
--431b372c--2020-07-02--Nicolas Carlo
--74a4dd32--2020-07-02--dependabot-preview[bot]
1	1	package.json
30	46	yarn.lock

--bc467761--2020-07-02--dependabot-preview[bot]
1	1	package.json
4	4	yarn.lock

--6683c094--2020-07-02--dependabot-preview[bot]
1	1	package.json
9	9	yarn.lock

--14fc6f67--2020-06-30--Nicolas Carlo
1	0	.all-contributorsrc

--6447f059--2020-06-30--Nicolas Carlo
--56364ff0--2020-06-30--Nicolas Carlo
--06445138--2020-06-30--allcontributors[bot]
12	0	.all-contributorsrc

--3936b84d--2020-06-30--allcontributors[bot]
14	14	README.md

--c56bbb0a--2020-06-30--Nicolas Carlo
140	0	.all-contributorsrc
21	13	README.md

--49084fe8--2020-06-30--Nicolas Carlo
2	0	package.json
106	2	yarn.lock

--82df32fd--2020-06-30--Nicolas Carlo
--6ccc2db4--2020-06-30--Oliver Joseph Ash
3	8	yarn.lock

--ef237df1--2020-06-30--Oliver Joseph Ash
4	43	yarn.lock

--f8fe6dcb--2020-06-30--Oliver Joseph Ash
3	3	package.json
61	3	yarn.lock

--fd19ad33--2020-06-30--Oliver Joseph Ash
2	0	CONTRIBUTING.md

--9bd2e4c8--2020-06-29--Oliver Joseph Ash
11	1	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.test.ts

--3d02dee7--2020-06-29--Oliver Joseph Ash
22	0	package.json

--ecb429ba--2020-06-29--Oliver Joseph Ash
3	0	src/extension.ts

--d816dd38--2020-06-29--Oliver Joseph Ash
4	0	src/editor/error-reason.ts
59	0	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.test.ts
61	0	src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.ts
20	0	src/refactorings/convert-function-declaration-to-arrow-function/index.ts

--878598a5--2020-06-18--Nicolas Carlo
--bd5aa370--2020-06-18--Nicolas Carlo
9	0	CHANGELOG.md
-	-	docs/demo/extract-substring.gif

--52d1395a--2020-06-18--Nicolas Carlo
0	12	src/ast/domain.ts
2	0	src/ast/index.ts
33	0	src/ast/template-literal.ts
1	20	src/refactorings/extract/extract-variable/occurrence.ts

--636a1683--2020-06-18--Nicolas Carlo
10	0	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
24	0	src/refactorings/extract/extract-variable/occurrence.ts

--30591846--2020-06-18--Nicolas Carlo
88	0	src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts
0	42	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--e47092d3--2020-06-18--Nicolas Carlo
19	0	src/editor/selection.ts

--ffed582f--2020-06-18--Nicolas Carlo
20	0	src/editor/selection.test.ts

--8e67dd17--2020-06-18--Nicolas Carlo
22	16	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
3	0	src/refactorings/extract/extract-variable/occurrence.ts
2	1	src/refactorings/extract/extract-variable/parts.test.ts

--a8abeb9c--2020-06-13--Nicolas Carlo
16	0	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
13	0	src/refactorings/extract/extract-variable/parts.test.ts

--b0a963bc--2020-06-12--Nicolas Carlo
12	1	src/refactorings/extract/extract-variable/parts.test.ts
1	1	src/refactorings/extract/extract-variable/parts.ts

--836d6fb7--2020-06-12--Nicolas Carlo
1	1	src/refactorings/extract/extract-variable/parts.test.ts

--7f40e4a1--2020-06-12--Nicolas Carlo
4	4	src/refactorings/extract/extract-variable/occurrence.ts
4	4	src/refactorings/extract/extract-variable/parts.test.ts
3	3	src/refactorings/extract/extract-variable/parts.ts

--d47c68b5--2020-06-12--Nicolas Carlo
18	0	src/refactorings/extract/extract-variable/parts.test.ts

--92554e59--2020-06-12--Nicolas Carlo
1	28	src/refactorings/extract/extract-variable/occurrence.ts
33	0	src/refactorings/extract/extract-variable/parts.ts

--5295ab56--2020-06-12--Nicolas Carlo
30	14	src/refactorings/extract/extract-variable/occurrence.ts

--3f31f20f--2020-06-12--Nicolas Carlo
3	4	src/refactorings/extract/extract-variable/occurrence.ts

--0a0c61b5--2020-06-12--Nicolas Carlo
5	4	src/refactorings/extract/extract-variable/occurrence.ts

--02e813e0--2020-06-11--Nicolas Carlo
4	3	src/refactorings/extract/extract-variable/occurrence.ts

--32d01722--2020-06-11--Nicolas Carlo
3	6	src/refactorings/extract/extract-variable/occurrence.ts

--35776d62--2020-06-11--Nicolas Carlo
4	4	src/refactorings/extract/extract-variable/occurrence.ts

--654b2d25--2020-06-10--Nicolas Carlo
5	2	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
3	7	src/refactorings/extract/extract-variable/occurrence.ts

--9fec26db--2020-06-10--Nicolas Carlo
5	2	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
20	3	src/refactorings/extract/extract-variable/occurrence.ts

--a20bc408--2020-06-10--Nicolas Carlo
13	12	src/refactorings/extract/extract-variable/occurrence.ts

--05cde7f4--2020-06-05--Nicolas Carlo
9	2	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts

--0bfcbe61--2020-06-05--Nicolas Carlo
1	1	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts

--2cef5dc9--2020-06-05--Nicolas Carlo
1	1	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts

--f93319ad--2020-06-05--Nicolas Carlo
84	0	src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts
0	52	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--c552f756--2020-06-05--Nicolas Carlo
24	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
27	1	src/refactorings/extract/extract-variable/occurrence.ts

--438bc9bd--2020-06-05--Nicolas Carlo
2	8	src/refactorings/extract/extract-variable/occurrence.ts

--abf1b2c4--2020-06-04--Nicolas Carlo
7	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--df4199a4--2020-06-04--Nicolas Carlo
0	1	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--954b779e--2020-06-04--Nicolas Carlo
2	3	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--bed80b8b--2020-06-04--Nicolas Carlo
9	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
24	7	src/refactorings/extract/extract-variable/occurrence.ts

--c2b74a3c--2020-06-04--Nicolas Carlo
5	3	src/refactorings/extract/extract-variable/occurrence.ts

--c5e6fc8d--2020-06-04--Nicolas Carlo
14	12	src/refactorings/extract/extract-variable/occurrence.ts

--71d6064a--2020-06-04--Nicolas Carlo
7	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
2	2	src/refactorings/extract/extract-variable/extract-variable.ts
75	1	src/refactorings/extract/extract-variable/occurrence.ts

--ecc17037--2020-06-04--Nicolas Carlo
1	1	src/ast/transformation.ts

--693c3b22--2020-06-04--Nicolas Carlo
2	9	src/refactorings/extract/extract-variable/occurrence.ts

--5d68b195--2020-06-04--Nicolas Carlo
0	11	src/refactorings/extract/extract-variable/occurrence.ts

--ff8b3ab8--2020-06-04--Nicolas Carlo
4	4	src/refactorings/extract/extract-variable/occurrence.ts

--e0e8a808--2020-06-04--Nicolas Carlo
8	4	src/refactorings/extract/extract-variable/occurrence.ts
14	17	src/refactorings/extract/extract-variable/variable.ts

--205b31cc--2020-06-04--Nicolas Carlo
4	0	src/editor/selection.ts

--74f0c6b7--2020-06-13--Nicolas Carlo
1	1	README.md

--90afa0c0--2020-06-13--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--0d2b5d18--2020-06-13--Nicolas Carlo
2	1	CHANGELOG.md

--e5a1d1fa--2020-06-13--Nicolas Carlo
1	0	README.md
-	-	docs/demo/convert-let-to-const.gif

--6cf58c0a--2020-06-13--Nicolas Carlo
13	16	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--7ffa30f6--2020-06-13--Nicolas Carlo
3	1	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--0a7a8620--2020-06-13--Nicolas Carlo
1	1	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--74534c65--2020-06-13--Nicolas Carlo
2	2	README.md
1	1	package.json
1	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
2	2	src/refactorings/convert-let-to-const/index.ts

--9225c89b--2020-06-13--Nicolas Carlo
2	1	README.md

--5dc341f8--2020-06-13--Nicolas Carlo
0	1	README.md
1	1	package.json
3	3	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
2	2	src/refactorings/convert-let-to-const/index.ts

--efe1ebbe--2020-06-13--Nicolas Carlo
--1308cc2f--2020-06-10--Nicolas Carlo
4	0	CHANGELOG.md
2	1	src/ast/scope.ts
7	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
2	1	src/refactorings/extract/extract-variable/extract-variable.ts

--b4cfe1dd--2020-06-10--Nicolas Carlo
4	4	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--ea5c9592--2020-06-05--Nick Ebbitt
12	0	README.md

--6b954414--2020-06-05--Nick Ebbitt
--c98bfd34--2020-06-05--Nick Ebbitt
8	2	src/ast/transformation.ts
1	2	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--2a27a7ff--2020-06-04--Nick Ebbitt
2	2	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--676b35a7--2020-06-04--Nick Ebbitt
11	11	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--6ea4da79--2020-06-04--Nick Ebbitt
46	56	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
24	39	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--3326ba97--2020-06-02--Nicolas Carlo
1	0	CHANGELOG.md
5	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
6	4	src/refactorings/remove-dead-code/remove-dead-code.ts

--a8c9e201--2020-06-02--Nicolas Carlo
1	0	CHANGELOG.md
13	0	src/refactorings/flip-if-else/flip-if-else.test.ts
8	1	src/refactorings/flip-if-else/flip-if-else.ts

--75e1ae78--2020-05-31--Nick Ebbitt
1	2	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--86d5ba27--2020-05-31--Nick Ebbitt
1	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--a4b22e59--2020-05-31--Nick Ebbitt
1	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--ea4f9314--2020-05-31--Nick Ebbitt
1	1	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--dfe956cf--2020-05-31--Nick Ebbitt
1	1	src/refactorings/convert-let-to-const/index.ts

--0ad3cc6b--2020-05-31--Nick Ebbitt
1	1	package.json

--ffde0fe7--2020-05-29--Nicolas Carlo
1	1	README.md

--5ee2ccbb--2020-05-28--Nicolas Carlo
6	14	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--87c9088a--2020-05-28--Nicolas Carlo
33	97	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--b54c26fe--2020-05-28--Nicolas Carlo
47	16	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--15adf132--2020-05-28--Nicolas Carlo
4	0	CHANGELOG.md

--79468d73--2020-05-28--Nicolas Carlo
2	1	README.md

--843dbd28--2020-05-28--Nicolas Carlo
19	0	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts

--a4b061a4--2020-05-28--Nicolas Carlo
10	4	src/ast/transformation.ts

--7ad9a291--2020-05-28--Nicolas Carlo
18	0	src/ast/transformation.test.ts
12	6	src/ast/transformation.ts

--8f84843d--2020-05-28--Nicolas Carlo
24	0	src/ast/transformation.test.ts
24	1	src/ast/transformation.ts

--2ea4b2ae--2020-05-28--Nicolas Carlo
1	7	src/ast/transformation.ts

--316b145a--2020-05-26--Nick Ebbitt
22	0	package.json
3	0	src/extension.ts
98	7	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
45	1	src/refactorings/convert-let-to-const/convert-let-to-const.ts

--82e995cb--2020-05-18--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--384e6e9b--2020-05-18--Nicolas Carlo
--01c76543--2020-05-18--Nicolas Carlo
4	0	CHANGELOG.md
1	1	README.md

--b52a5058--2020-05-18--Nicolas Carlo
0	2	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--4e54be57--2020-05-18--Nicolas Carlo
8	4	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts

--39085579--2020-05-18--Nicolas Carlo
52	0	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts

--a544e701--2020-05-18--Nicolas Carlo
37	2	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts

--6574a9ed--2020-05-18--Nicolas Carlo
1	8	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts
1	8	src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts

--fc6fb0fc--2020-05-18--Nicolas Carlo
54	0	src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts
0	28	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts

--0bef9dc4--2020-05-18--Nicolas Carlo
318	0	src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts
1	292	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts

--210e653d--2020-05-18--Nicolas Carlo
7	14	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts

--0982c582--2020-05-18--Nicolas Carlo
221	215	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts

--d78b73dd--2020-05-18--Nicolas Carlo
10	4	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
68	10	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--3000bc9d--2020-05-17--Nicolas Carlo
20	1	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
53	0	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--5e50eaf8--2020-05-17--Nicolas Carlo
9	3	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--f6b27cb1--2020-05-17--Nicolas Carlo
7	5	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--9ee2c89c--2020-05-17--Nicolas Carlo
12	4	src/refactorings/extract/extract-generic-type/extract-generic-type.ts

--389c4f10--2020-05-17--Nick Ebbitt
12	0	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts

--b1263d11--2020-05-17--Nick Ebbitt
6	0	src/editor/error-reason.ts
57	0	src/refactorings/convert-let-to-const/convert-let-to-const.test.ts
44	0	src/refactorings/convert-let-to-const/convert-let-to-const.ts
17	0	src/refactorings/convert-let-to-const/index.ts

--ef75a989--2020-05-01--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--b2a32b00--2020-05-01--Nicolas Carlo
11	0	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts

--112cacda--2020-05-01--Nicolas Carlo
--84abe067--2020-04-30--delaaxe
1	2	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--e361031f--2020-04-30--delaaxe
26	3	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
28	4	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--82e510b7--2020-04-30--Nicolas Carlo
4	4	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--6bc47894--2020-04-30--Nicolas Carlo
5	0	CHANGELOG.md
12	0	README.md
-	-	docs/demo/convert-switch-to-if-else.gif

--a1087b5c--2020-04-30--Nicolas Carlo
22	0	package.json

--55c8e68b--2020-04-30--Nicolas Carlo
2	1	README.md

--28162b6c--2020-04-30--Nicolas Carlo
0	1	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--cc4ee9f6--2020-04-30--Nicolas Carlo
2	2	src/refactorings/convert-switch-to-if-else/index.ts

--f2272eb0--2020-04-30--Nicolas Carlo
--edad4f05--2020-04-29--Nicolas Carlo
2	4	_templates/refactoring/new/index.ejs.t
31	47	_templates/refactoring/new/index.js
15	12	_templates/refactoring/new/refactoring.ejs.t

--5bdfabf0--2020-04-26--delaaxe
3	0	src/extension.ts

--c96d202a--2020-04-25--delaaxe
2	1	src/refactorings/convert-switch-to-if-else/index.ts

--435f4727--2020-04-25--delaaxe
1	1	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts

--0921fe64--2020-04-25--delaaxe
47	4	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
51	22	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--702fec58--2020-04-25--delaaxe
150	1	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
62	13	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts

--837a64f3--2020-04-25--Nicolas Carlo
3	4	src/refactorings/extract/extract-variable/extract-variable.ts

--852dfdbe--2020-04-25--Nicolas Carlo
5	1	src/refactorings/extract/extract-variable/extract-variable.ts

--b2504c98--2020-04-25--Nicolas Carlo
22	0	CHANGELOG.md
9	2	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
1	0	src/refactorings/extract/extract-variable/extract-variable.ts

--170c0b02--2020-04-25--Nicolas Carlo
180	0	src/refactorings/extract/extract-variable/extract-variable.extractable-jsx.test.ts
1	149	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts

--185fa6fa--2020-04-25--delaaxe
4	0	src/editor/error-reason.ts
47	0	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts
94	0	src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts
20	0	src/refactorings/convert-switch-to-if-else/index.ts

--531bf43e--2020-04-24--Nicolas Carlo
--d62eb0a7--2020-04-24--Nicolas Carlo
1	1	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
1	1	src/refactorings/extract/extract-generic-type/extract-generic-type.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
1	1	src/refactorings/extract/extract-variable/extract-variable.ts
30	0	src/refactorings/extract/replacement-strategy.ts
0	30	src/replacement-strategy.ts

--c36d6d24--2020-04-24--Nicolas Carlo
9	0	CHANGELOG.md
14	0	README.md
-	-	docs/demo/extract-generic-type.gif

--8e4921f9--2020-04-24--Nicolas Carlo
-	-	docs/demo/bubble-up-if-statement.gif
-	-	docs/demo/convert-if-else-to-switch.gif
-	-	docs/demo/merge-if-statements-else-if.gif
-	-	docs/demo/merge-if-with-previous-if-statement.gif

--348e1e33--2020-04-24--Nicolas Carlo
8	8	package.json
2	2	src/extension.ts
0	42	src/refactorings/inline-variable-or-function/find-exported-id-names.ts
0	120	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
0	542	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
0	271	src/refactorings/inline-variable-or-function/find-param-matching-id.ts
0	60	src/refactorings/inline-variable-or-function/index.ts
0	602	src/refactorings/inline-variable-or-function/inline-function.test.ts
0	322	src/refactorings/inline-variable-or-function/inline-function.ts
0	175	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts
0	216	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts
0	456	src/refactorings/inline-variable-or-function/inline-variable.test.ts
0	125	src/refactorings/inline-variable-or-function/inline-variable.ts
42	0	src/refactorings/inline/find-exported-id-names.ts
60	0	src/refactorings/inline/index.ts
271	0	src/refactorings/inline/inline-function/find-param-matching-id.ts
602	0	src/refactorings/inline/inline-function/inline-function.test.ts
322	0	src/refactorings/inline/inline-function/inline-function.ts
120	0	src/refactorings/inline/inline-variable/find-inlinable-code.test.ts
542	0	src/refactorings/inline/inline-variable/find-inlinable-code.ts
175	0	src/refactorings/inline/inline-variable/inline-variable.array-pattern.test.ts
216	0	src/refactorings/inline/inline-variable/inline-variable.object-pattern.test.ts
456	0	src/refactorings/inline/inline-variable/inline-variable.test.ts
125	0	src/refactorings/inline/inline-variable/inline-variable.ts

--b98e43f4--2020-04-24--Nicolas Carlo
0	343	src/refactorings/extract-generic-type/extract-generic-type.test.ts
0	186	src/refactorings/extract-generic-type/extract-generic-type.ts
0	85	src/refactorings/extract-variable/extract-variable.basic-extraction.test.ts
0	235	src/refactorings/extract-variable/extract-variable.extractable-objects.test.ts
0	692	src/refactorings/extract-variable/extract-variable.extractable.test.ts
0	325	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
0	53	src/refactorings/extract-variable/extract-variable.non-extractable.test.ts
0	180	src/refactorings/extract-variable/extract-variable.ts
0	57	src/refactorings/extract-variable/extract-variable.variable-name.test.ts
0	158	src/refactorings/extract-variable/occurrence.ts
0	96	src/refactorings/extract-variable/variable.ts
0	58	src/refactorings/extract.ts
343	0	src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts
186	0	src/refactorings/extract/extract-generic-type/extract-generic-type.ts
85	0	src/refactorings/extract/extract-variable/extract-variable.basic-extraction.test.ts
235	0	src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts
692	0	src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts
325	0	src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts
53	0	src/refactorings/extract/extract-variable/extract-variable.non-extractable.test.ts
180	0	src/refactorings/extract/extract-variable/extract-variable.ts
57	0	src/refactorings/extract/extract-variable/extract-variable.variable-name.test.ts
158	0	src/refactorings/extract/extract-variable/occurrence.ts
96	0	src/refactorings/extract/extract-variable/variable.ts
58	0	src/refactorings/extract/index.ts

--191aef6b--2020-04-24--Nicolas Carlo
0	36	package.json
0	4	src/extension.ts
0	12	src/refactorings/extract-generic-type/index.ts
0	12	src/refactorings/extract-variable/index.ts

--9a1f80b9--2020-04-24--Nicolas Carlo
21	0	package.json

--b5eb707b--2020-04-24--Nicolas Carlo
1	1	src/extension.ts
4	7	src/refactorings/extract-generic-type/extract-generic-type.ts
4	9	src/refactorings/extract-generic-type/index.ts

--0801ee7a--2020-04-24--Nicolas Carlo
2	1	package.json
2	0	src/extension.ts
58	0	src/refactorings/extract.ts

--33f85c56--2020-04-24--Nicolas Carlo
0	4	src/editor/error-reason.ts
1	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
1	1	src/refactorings/extract-generic-type/extract-generic-type.ts

--359a6907--2020-04-23--Nicolas Carlo
32	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
1	0	src/refactorings/extract-generic-type/extract-generic-type.ts

--e153c9e9--2020-04-23--Nicolas Carlo
5	24	src/refactorings/extract-generic-type/extract-generic-type.ts

--83391fd4--2020-04-23--Nicolas Carlo
6	3	src/refactorings/extract-generic-type/extract-generic-type.ts

--961a307e--2020-04-23--Nicolas Carlo
25	25	src/refactorings/extract-generic-type/extract-generic-type.ts

--c557253b--2020-04-23--Nicolas Carlo
9	6	src/refactorings/extract-generic-type/extract-generic-type.ts

--1fb2a98e--2020-04-23--Nicolas Carlo
17	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
5	0	src/refactorings/extract-generic-type/extract-generic-type.ts

--12d9e8f7--2020-04-23--Nicolas Carlo
20	11	src/refactorings/extract-generic-type/extract-generic-type.test.ts
26	11	src/refactorings/extract-generic-type/extract-generic-type.ts

--48786a3b--2020-04-23--Nicolas Carlo
51	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
21	6	src/refactorings/extract-generic-type/extract-generic-type.ts

--6b4c12a5--2020-04-23--Nicolas Carlo
38	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
24	19	src/refactorings/extract-generic-type/extract-generic-type.ts

--30cbc340--2020-04-23--Nicolas Carlo
27	21	src/ast/identity.ts
3	3	src/ast/scope.ts
5	5	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
1	1	src/refactorings/extract-generic-type/extract-generic-type.ts
1	1	src/refactorings/extract-variable/extract-variable.ts
3	3	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
1	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
2	2	src/refactorings/remove-dead-code/remove-dead-code.ts
3	3	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
1	1	src/refactorings/simplify-ternary/simplify-ternary.ts

--f04256d8--2020-04-23--Nicolas Carlo
12	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
49	12	src/refactorings/extract-generic-type/extract-generic-type.ts

--e6c08ae7--2020-04-22--Nicolas Carlo
3	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
9	5	src/refactorings/extract-generic-type/extract-generic-type.ts

--24f4a2c6--2020-04-21--Nicolas Carlo
1	0	src/refactorings/extract-generic-type/extract-generic-type.ts

--1932560a--2020-04-21--Nicolas Carlo
14	0	package.json
3	1	src/extension.ts

--214623b3--2020-04-21--Nicolas Carlo
16	2	src/refactorings/extract-generic-type/extract-generic-type.test.ts
4	0	src/refactorings/extract-generic-type/extract-generic-type.ts

--cadc3492--2020-04-21--Nicolas Carlo
61	59	src/refactorings/extract-generic-type/extract-generic-type.test.ts

--d4692487--2020-04-21--Nicolas Carlo
19	0	src/refactorings/extract-generic-type/extract-generic-type.test.ts
2	0	src/refactorings/extract-generic-type/extract-generic-type.ts

--e1a89ffd--2020-04-21--Nicolas Carlo
1	1	src/refactorings/extract-generic-type/extract-generic-type.ts

--00f03b7c--2020-04-21--Nicolas Carlo
53	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
10	3	src/refactorings/extract-generic-type/extract-generic-type.ts

--864ad21b--2020-04-21--Nicolas Carlo
44	45	src/refactorings/extract-generic-type/extract-generic-type.ts

--361800da--2020-04-21--Nicolas Carlo
7	3	src/ast/transformation.ts

--d13fe89e--2020-04-21--Nicolas Carlo
5	0	src/ast/identity.ts
9	0	src/editor/selection.ts
25	0	src/refactorings/extract-generic-type/extract-generic-type.test.ts
45	2	src/refactorings/extract-generic-type/extract-generic-type.ts

--a3094e8b--2020-04-21--Nicolas Carlo
14	21	src/refactorings/extract-variable/extract-variable.ts

--66c8240a--2020-04-21--Nicolas Carlo
4	3	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
8	31	src/refactorings/extract-variable/extract-variable.ts
30	0	src/replacement-strategy.ts

--a302641b--2020-04-17--Nicolas Carlo
30	21	src/refactorings/extract-generic-type/extract-generic-type.ts
2	2	src/refactorings/extract-generic-type/index.ts

--da282881--2020-02-06--Nicolas Carlo
3	2	src/refactorings/extract-generic-type/extract-generic-type.ts

--8a461f4b--2020-02-06--Nicolas Carlo
17	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
9	2	src/refactorings/extract-generic-type/extract-generic-type.ts

--36e41992--2020-02-06--Nicolas Carlo
13	1	src/refactorings/extract-generic-type/extract-generic-type.test.ts
17	1	src/refactorings/extract-generic-type/extract-generic-type.ts

--37506588--2020-04-17--Nicolas Carlo
4	0	src/editor/error-reason.ts
47	0	src/refactorings/extract-generic-type/extract-generic-type.test.ts
31	0	src/refactorings/extract-generic-type/extract-generic-type.ts
17	0	src/refactorings/extract-generic-type/index.ts

--2abc82f9--2020-04-10--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--8332afc2--2020-04-10--Nicolas Carlo
--f846ca81--2020-04-10--Nicolas Carlo
4	0	CHANGELOG.md
6	2	README.md
-	-	docs/demo/convert-to-template-literal.gif

--e2787207--2020-04-10--Nicolas Carlo
7	82	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
16	185	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--c1411ede--2020-04-10--Nicolas Carlo
14	0	README.md
-	-	docs/demo/convert-to-template-literal.gif
22	0	package.json
3	0	src/editor/error-reason.ts
3	0	src/extension.ts
200	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
232	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
21	0	src/refactorings/convert-to-template-literal/index.ts

--3865976c--2020-04-10--Nicolas Carlo
3	2	README.md

--894e2ac9--2020-04-10--Nicolas Carlo
4	0	CHANGELOG.md
24	22	src/action-providers.ts

--91a8f704--2020-04-10--Nicolas Carlo
3	3	src/action-providers.ts

--8173dcf1--2020-04-09--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--c71ce3a5--2020-04-09--Nicolas Carlo
10	0	CHANGELOG.md
5	1	src/ast/identity.ts
11	0	src/refactorings/extract-variable/extract-variable.extractable-objects.test.ts

--47691089--2020-04-09--Nicolas Carlo
6	0	.prettierrc.js

--29a36b74--2020-04-09--Nicolas Carlo
224	0	src/refactorings/extract-variable/extract-variable.extractable-objects.test.ts
0	224	src/refactorings/extract-variable/extract-variable.extractable-objects.ts

--61099010--2020-04-02--Nicolas Carlo
7	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts

--23923c34--2020-04-02--Nicolas Carlo
7	159	src/refactorings/extract-variable/extract-variable.ts
158	0	src/refactorings/extract-variable/occurrence.ts

--394f0c8f--2020-04-02--Nicolas Carlo
47	50	src/refactorings/extract-variable/extract-variable.ts

--2c10267c--2020-04-02--Nicolas Carlo
3	9	src/refactorings/extract-variable/extract-variable.ts

--99f6f4e0--2020-04-02--Nicolas Carlo
7	87	src/refactorings/extract-variable/extract-variable.ts
96	0	src/refactorings/extract-variable/variable.ts

--6967db2b--2020-04-02--Nicolas Carlo
224	0	src/refactorings/extract-variable/extract-variable.extractable-objects.ts
0	187	src/refactorings/extract-variable/extract-variable.extractable.test.ts

--dab096fe--2020-04-02--Nicolas Carlo
28	0	CHANGELOG.md

--82bdc1a4--2020-04-02--Nicolas Carlo
29	19	src/refactorings/extract-variable/extract-variable.ts

--276729ca--2020-04-02--Nicolas Carlo
11	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
10	2	src/refactorings/extract-variable/extract-variable.ts

--159bb836--2020-04-02--Nicolas Carlo
35	50	src/refactorings/extract-variable/extract-variable.ts

--ff172952--2020-04-02--Nicolas Carlo
19	11	src/refactorings/extract-variable/extract-variable.ts

--7e945227--2020-04-02--Nicolas Carlo
11	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
23	1	src/refactorings/extract-variable/extract-variable.ts

--32545911--2020-03-30--Nicolas Carlo
28	1	CHANGELOG.md
1	1	package.json

--6aa076f8--2020-03-30--Nicolas Carlo
1	0	README.md

--6f7ac2ae--2020-03-30--Nicolas Carlo
3	0	README.md
-	-	docs/demo/remove-braces-from-if-statement.gif

--c4c51333--2020-03-30--Nicolas Carlo
1	0	CHANGELOG.md
22	0	package.json

--389d8b3f--2020-03-30--Nicolas Carlo
--10e4b26d--2020-03-30--Nicolas Carlo
34	65	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts

--a86ff9ed--2020-03-29--Nicolas Carlo
1	1	src/refactorings/remove-braces-from-if-statement/index.ts
4	8	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts

--7385b7fb--2020-03-29--Nicolas Carlo
--28d2e02a--2020-03-29--Nicolas Carlo
4	0	CHANGELOG.md
0	12	README.md
-	-	docs/demo/convert-to-template-literal.gif
0	22	package.json
0	3	src/editor/error-reason.ts
0	3	src/extension.ts
0	200	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
0	232	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
0	21	src/refactorings/convert-to-template-literal/index.ts

--57870590--2020-03-29--Nicolas Carlo
36	0	package.json

--0e07f1f3--2020-03-27--Nicolas Carlo
--58932bb9--2020-03-27--Nicolas Carlo
1	0	CHANGELOG.md

--1d48a2f1--2020-03-27--Nicolas Carlo
10	4	src/action-providers.ts

--4e7fa85c--2020-03-27--Nicolas Carlo
12	28	src/action-providers.ts
1	2	src/types.ts

--e817f912--2020-03-27--Nicolas Carlo
9	43	src/action-providers.ts
13	49	src/types.ts

--be9c7e77--2020-03-27--Nicolas Carlo
1	1	src/refactorings/remove-dead-code/index.ts
1	7	src/refactorings/remove-dead-code/remove-dead-code.ts

--a0cf2d69--2020-03-27--Nicolas Carlo
67	16	src/refactorings/remove-dead-code/remove-dead-code.ts

--84343acb--2020-03-27--Nicolas Carlo
7	1	src/refactorings/remove-dead-code/remove-dead-code.test.ts

--6d0018d7--2020-03-27--Nicolas Carlo
20	3	src/extension.ts

--ade3aff3--2020-03-27--Nicolas Carlo
5	0	CHANGELOG.md
1	1	README.md
32	0	docs/adr/0008-don-t-propose-quick-fix-for-react-convert-to-pure-component.md
-	-	docs/adr/assets/0008-flame-chart.png
0	1	src/extension.ts
1	7	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
4	12	src/refactorings/react/convert-to-pure-component/index.ts

--ef7faca9--2020-03-27--Nicolas Carlo
0	20	src/refactorings/merge-if-statements/merge-if-statements.ts

--02a6106b--2020-03-27--Nicolas Carlo
4	0	CHANGELOG.md

--139eaadb--2020-03-27--Nicolas Carlo
10	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts

--cc7a9fea--2020-03-27--Nicolas Carlo
33	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--10f23e03--2020-03-27--Nicolas Carlo
1	1	src/types.ts

--4858bbb1--2020-03-27--Nicolas Carlo
4	4	src/refactorings/merge-if-statements/index.ts
3	3	src/types.ts

--7c04c7a0--2020-03-27--Nicolas Carlo
6	7	src/refactorings/replace-binary-with-assignment/index.ts
1	2	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--9ff0885b--2020-03-26--Nicolas Carlo
7	6	src/refactorings/replace-binary-with-assignment/index.ts
2	1	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--a5de39d0--2020-03-26--Nicolas Carlo
3	4	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--503f5420--2020-03-26--Nicolas Carlo
3	2	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts

--11de002b--2020-03-26--Nicolas Carlo
3	2	src/refactorings/negate-expression/negate-expression.ts

--3e746b77--2020-03-26--Nicolas Carlo
31	23	src/refactorings/negate-expression/negate-expression.test.ts

--c9ef6a9f--2020-03-26--Nicolas Carlo
1	2	src/refactorings/negate-expression/negate-expression.test.ts

--19ded0fc--2020-03-26--Nicolas Carlo
2	3	src/refactorings/negate-expression/index.ts

--865c59ac--2020-03-26--Nicolas Carlo
1	2	src/refactorings/negate-expression/index.ts

--637698e9--2020-03-26--Nicolas Carlo
4	4	src/refactorings/merge-if-statements/index.ts
3	3	src/types.ts

--2d0edfa8--2020-03-26--Nicolas Carlo
1	2	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts

--14cba57b--2020-03-26--Nicolas Carlo
--5c4d2d56--2020-03-26--Nicolas Carlo
--9235b286--2020-03-26--Nicolas Carlo
1	6	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--96083b69--2020-03-26--Nicolas Carlo
1	2	src/refactorings/flip-if-else/flip-if-else.ts

--ee852db3--2020-03-26--Nicolas Carlo
65	11	README.md

--4c03c757--2020-03-26--Nicolas Carlo
0	91	.all-contributorsrc

--76f544a8--2020-03-26--Nicolas Carlo
4	1	README.md

--efdaef1e--2020-03-26--Nicolas Carlo
4	0	CHANGELOG.md
7	0	README.md
14	0	package.json
25	0	src/action-providers.ts

--01d5e35c--2020-03-22--Alexander Rose
1	1	README.md

--3b57af69--2020-03-22--Nicolas Carlo
2	11	src/refactorings/merge-if-statements/merge-if-statements.ts

--f43f8b9c--2020-03-22--Nicolas Carlo
--f7b45745--2020-03-19--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--172fa4f0--2020-03-19--Nicolas Carlo
4	0	CHANGELOG.md
3	1	src/ast/transformation.ts
17	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
16	2	src/refactorings/extract-variable/extract-variable.ts

--d9ccf7df--2020-03-19--Nicolas Carlo
2	2	src/refactorings/extract-variable/extract-variable.extractable.test.ts

--2196bbef--2020-03-18--Nicolas Carlo
--c1c000ed--2020-03-18--Nicolas Carlo
1	4	src/action-providers.ts

--7b2d1dff--2020-03-18--Nicolas Carlo
2	5	src/action-providers.ts

--51cca8c0--2020-03-18--Nicolas Carlo
5	7	src/action-providers.ts

--a2f92147--2020-03-18--Nicolas Carlo
13	7	src/action-providers.ts

--1848d283--2020-03-18--Nicolas Carlo
4	4	src/action-providers.ts

--18c723bf--2020-03-18--Nicolas Carlo
1	5	src/refactorings/simplify-ternary/index.ts

--37060181--2020-03-18--Nicolas Carlo
--2aeeb644--2020-03-12--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--eafb4864--2020-03-12--Nicolas Carlo
4	0	CHANGELOG.md

--cdfa08e8--2020-03-12--Nicolas Carlo
12	0	.all-contributorsrc
2	1	README.md

--0aea60ed--2020-03-12--Nicolas Carlo
--b1a12090--2020-03-12--Nicolas Carlo
1	1	.travis.yml

--b85828ea--2020-03-12--Nicolas Carlo
1	1	.travis.yml

--2b62ba2c--2020-03-12--Nicolas Carlo
1	1	.travis.yml

--f285e47d--2020-03-12--Nicolas Carlo
12	0	src/ast/transformation.ts

--e1e162c8--2020-03-12--Nicolas Carlo
3	3	src/ast/transformation.ts

--140ca7dd--2020-03-12--Nicolas Carlo
5	1	src/array-helpers.ts
5	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--e7ba72c9--2020-03-12--Nicolas Carlo
1	1	package.json
31	255	yarn.lock

--938cc606--2020-03-12--Nicolas Carlo
1	1	src/ast/domain.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
6	4	src/refactorings/extract-interface/extract-interface.ts
1	0	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
2	2	src/refactorings/inline-variable-or-function/find-param-matching-id.ts

--27c9d92f--2020-03-12--Nicolas Carlo
1700	1223	yarn.lock

--9a0d3137--2020-03-12--Nicolas Carlo
1	1	package.json
995	697	yarn.lock

--147d5aa6--2020-03-12--Nicolas Carlo
2	1	src/ast/transformation.ts

--cd65d676--2020-03-12--Nicolas Carlo
4	4	package.json
98	4	yarn.lock

--0ca1f32a--2020-03-12--Nicolas Carlo
8	0	src/ast/transformation.test.ts

--bef377dd--2020-03-09--Tobias Hann
2	2	src/editor/error-reason.ts
2	2	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts
1	1	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts

--41025095--2020-03-09--Tobias Hann
--0389e36d--2020-03-08--Nicolas Carlo
--8c612542--2020-03-08--Nicolas Carlo
--01ef2a5d--2020-03-07--Tobias Hann
1	1	_templates/refactoring/new/error-reason-switch.ejs.t
2	2	_templates/refactoring/new/index.js
80	80	src/editor/error-reason.ts
2	2	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts
1	1	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
2	2	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
1	1	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
1	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
1	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
1	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
1	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
1	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
1	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
1	1	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
1	1	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
1	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
1	1	src/refactorings/extract-interface/extract-interface.test.ts
1	1	src/refactorings/extract-interface/extract-interface.ts
1	1	src/refactorings/extract-variable/extract-variable.basic-extraction.test.ts
1	1	src/refactorings/extract-variable/extract-variable.ts
1	3	src/refactorings/flip-if-else/flip-if-else.test.ts
1	1	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/flip-ternary/flip-ternary.test.ts
1	1	src/refactorings/flip-ternary/flip-ternary.ts
1	1	src/refactorings/inline-variable-or-function/index.ts
3	3	src/refactorings/inline-variable-or-function/inline-function.test.ts
1	1	src/refactorings/inline-variable-or-function/inline-function.ts
4	4	src/refactorings/inline-variable-or-function/inline-variable.test.ts
2	2	src/refactorings/inline-variable-or-function/inline-variable.ts
1	1	src/refactorings/merge-if-statements/merge-if-statements.test.ts
1	1	src/refactorings/merge-if-statements/merge-if-statements.ts
1	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
1	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
2	2	src/refactorings/negate-expression/negate-expression.test.ts
1	1	src/refactorings/negate-expression/negate-expression.ts
1	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
1	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
1	1	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
1	1	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
1	1	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts
1	1	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts
2	2	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts
1	1	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts
1	1	src/refactorings/remove-dead-code/remove-dead-code.test.ts
1	1	src/refactorings/remove-dead-code/remove-dead-code.ts
1	1	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
1	1	src/refactorings/remove-redundant-else/remove-redundant-else.ts
1	1	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.test.ts
1	1	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
1	1	src/refactorings/simplify-ternary/simplify-ternary.test.ts
1	1	src/refactorings/simplify-ternary/simplify-ternary.ts
2	2	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
1	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
2	2	src/refactorings/split-if-statement/split-if-statement.test.ts
1	1	src/refactorings/split-if-statement/split-if-statement.ts

--1774d632--2020-03-07--Tobias Hann
2	2	src/refactorings/merge-if-statements/index.ts
4	11	src/refactorings/merge-if-statements/merge-if-statements.ts

--b478be79--2020-03-07--Tobias Hann
0	7	src/refactorings/merge-if-statements/merge-if-statements.ts

--4bb71654--2020-03-07--Tobias Hann
6	0	README.md
4	0	src/editor/error-reason.ts
3	0	src/extension.ts
20	0	src/refactorings/remove-braces-from-if-statement/index.ts
211	0	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts
116	0	src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts

--05fb757a--2020-03-06--Alexander Rose
2	2	src/refactorings/merge-if-statements/index.ts
2	2	src/refactorings/negate-expression/index.ts
1	1	src/refactorings/negate-expression/negate-expression.ts
2	2	src/refactorings/replace-binary-with-assignment/index.ts

--84ee4de2--2020-02-22--Alexander Rose
1	1	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
2	1	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
2	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
2	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
2	1	src/refactorings/merge-if-statements/merge-if-statements.ts
1	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
2	1	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--9acb598d--2020-02-22--Alexander Rose
4	4	src/action-providers.ts
31	28	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
2	3	src/refactorings/merge-if-statements/index.ts
1	1	src/refactorings/negate-expression/index.ts
2	2	src/refactorings/negate-expression/negate-expression.ts
3	2	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
6	3	src/refactorings/remove-redundant-else/remove-redundant-else.ts
2	4	src/refactorings/replace-binary-with-assignment/index.ts
1	1	src/types.ts

--3a282ee2--2020-02-22--Alexander Rose
1	5	src/refactorings/simplify-ternary/index.ts

--05efcdc6--2020-02-22--Alexander Rose
2	5	src/refactorings/simplify-ternary/index.ts
1	1	src/refactorings/simplify-ternary/simplify-ternary.ts

--8a2220cd--2020-02-21--Alexander Rose
4	11	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
1	1	src/refactorings/add-braces-to-arrow-function/index.ts
1	8	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
1	1	src/refactorings/add-braces-to-if-statement/index.ts
1	8	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
1	1	src/refactorings/bubble-up-if-statement/index.ts
1	8	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
1	1	src/refactorings/convert-for-to-foreach/index.ts
1	8	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
1	1	src/refactorings/convert-if-else-to-switch/index.ts
1	8	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
1	1	src/refactorings/convert-if-else-to-ternary/index.ts
1	9	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
1	1	src/refactorings/convert-ternary-to-if-else/index.ts
6	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
4	12	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
1	1	src/refactorings/convert-to-template-literal/index.ts
1	8	src/refactorings/extract-interface/extract-interface.ts
1	1	src/refactorings/extract-interface/index.ts
1	9	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/flip-if-else/index.ts
1	9	src/refactorings/flip-ternary/flip-ternary.ts
1	1	src/refactorings/flip-ternary/index.ts
6	8	src/refactorings/merge-if-statements/index.ts
1	26	src/refactorings/merge-if-statements/merge-if-statements.ts
1	1	src/refactorings/merge-with-previous-if-statement/index.ts
4	14	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
10	12	src/refactorings/negate-expression/index.ts
48	45	src/refactorings/negate-expression/negate-expression.test.ts
5	15	src/refactorings/negate-expression/negate-expression.ts
4	12	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
1	1	src/refactorings/react/add-braces-to-jsx-attribute/index.ts
1	1	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
4	12	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts
1	1	src/refactorings/remove-braces-from-arrow-function/index.ts
8	12	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts
1	1	src/refactorings/remove-redundant-else/index.ts
1	9	src/refactorings/remove-redundant-else/remove-redundant-else.ts
8	6	src/refactorings/replace-binary-with-assignment/index.ts
5	29	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
1	1	src/refactorings/split-declaration-and-initialization/index.ts
1	12	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
1	1	src/refactorings/split-if-statement/index.ts
1	9	src/refactorings/split-if-statement/split-if-statement.ts

--482cdc3b--2020-02-21--Alexander Rose
22	28	src/refactorings/remove-redundant-else/remove-redundant-else.ts
42	46	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
25	25	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
32	32	src/refactorings/split-if-statement/split-if-statement.ts

--03717f0a--2020-02-21--Alexander Rose
26	26	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
43	39	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
24	21	src/refactorings/flip-if-else/flip-if-else.ts
21	19	src/refactorings/flip-ternary/flip-ternary.ts
28	27	src/refactorings/merge-if-statements/merge-if-statements.ts
24	17	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
36	17	src/refactorings/negate-expression/negate-expression.ts
19	14	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
23	14	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts
31	24	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--1dcacc2b--2020-03-06--Alexander Rose
4	4	src/action-providers.ts

--7c25dbd7--2020-03-06--Alexander Rose
17	14	src/action-providers.ts

--6f556ee4--2020-03-06--Alexander Rose
40	23	src/action-providers.ts
20	3	src/types.ts

--ac11c9f8--2020-03-06--Alexander Rose
3	1	src/action-providers.ts
2	4	src/refactorings/simplify-ternary/index.ts
1	1	src/types.ts

--5d02f6ed--2020-03-02--Alexander Rose
1	1	src/action-providers.ts

--010e6f21--2020-03-02--Alexander Rose
1	1	src/types.ts

--d883dd5c--2020-02-21--Alexander Rose
75	6	src/action-providers.ts
11	2	src/refactorings/simplify-ternary/index.ts
1	9	src/refactorings/simplify-ternary/simplify-ternary.ts
31	7	src/types.ts

--cda8f0d0--2020-02-20--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--2376e0f4--2020-02-20--Nicolas Carlo
1	0	CHANGELOG.md
21	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
6	7	src/refactorings/extract-variable/extract-variable.ts

--907f7237--2020-02-06--Nicolas Carlo
1	1	CHANGELOG.md
21	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
20	1	src/refactorings/extract-variable/extract-variable.ts

--a2ce551b--2020-02-05--Nicolas Carlo
4	0	CHANGELOG.md
7	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
1	1	src/refactorings/extract-variable/extract-variable.ts

--4981bfbf--2020-01-30--Nicolas Carlo
5	2	CHANGELOG.md
1	1	package.json

--c303bc8e--2020-01-30--Nicolas Carlo
--a2fa9847--2020-01-30--Nicolas Carlo
13	0	README.md
-	-	docs/demo/extract-interface.gif

--3c02e102--2020-01-30--Nicolas Carlo
4	0	src/editor/adapters/in-memory-editor.ts
5	0	src/editor/adapters/vscode-editor.ts
1	0	src/editor/editor.ts
4	0	src/editor/position.ts
20	0	src/refactorings/extract-interface/extract-interface.test.ts
27	2	src/refactorings/extract-interface/extract-interface.ts

--3bb51dfd--2020-01-30--Nicolas Carlo
2	1	CHANGELOG.md

--e6f24417--2020-01-30--Nicolas Carlo
14	0	package.json
15	7	src/extension.ts

--9ccf9807--2020-01-30--Nicolas Carlo
1	1	src/refactorings/extract-interface/extract-interface.test.ts

--d29e9864--2020-01-30--Nicolas Carlo
25	7	src/refactorings/extract-interface/extract-interface.ts

--317a0527--2020-01-30--Nicolas Carlo
0	2	src/refactorings/extract-interface/extract-interface.ts

--aae2d785--2020-01-30--Nicolas Carlo
9	6	src/refactorings/extract-interface/extract-interface.ts

--88593f59--2020-01-30--Nicolas Carlo
13	2	src/refactorings/extract-interface/extract-interface.test.ts
22	8	src/refactorings/extract-interface/extract-interface.ts

--928637d0--2020-01-30--Nicolas Carlo
3	2	src/refactorings/extract-interface/extract-interface.test.ts
7	5	src/refactorings/extract-interface/extract-interface.ts

--a85727cd--2020-01-30--Nicolas Carlo
3	1	src/refactorings/extract-interface/extract-interface.test.ts
6	8	src/refactorings/extract-interface/extract-interface.ts

--14d5466f--2020-01-30--Nicolas Carlo
17	7	src/refactorings/extract-interface/extract-interface.ts

--b5731bac--2020-01-27--Nicolas Carlo
13	0	src/refactorings/extract-interface/extract-interface.test.ts
25	4	src/refactorings/extract-interface/extract-interface.ts

--d29f256b--2020-01-30--Nicolas Carlo
1	0	CHANGELOG.md

--2bc609ca--2020-01-29--Nicolas Carlo
--a26fd3ec--2020-01-28--Tim van Cleef
31	2	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
6	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--5557e508--2020-01-25--Nicolas Carlo
8	0	src/refactorings/extract-interface/extract-interface.test.ts
2	12	src/refactorings/extract-interface/extract-interface.ts

--256e8722--2020-01-25--Nicolas Carlo
16	18	src/refactorings/extract-interface/extract-interface.ts

--2ddf3c72--2020-01-25--Nicolas Carlo
20	3	src/refactorings/extract-interface/extract-interface.test.ts
8	8	src/refactorings/extract-interface/extract-interface.ts

--d465631d--2020-01-25--Nicolas Carlo
9	0	src/refactorings/extract-interface/extract-interface.test.ts
8	1	src/refactorings/extract-interface/extract-interface.ts

--5e57508a--2020-01-24--Nicolas Carlo
30	0	src/refactorings/extract-interface/extract-interface.test.ts
2	1	src/refactorings/extract-interface/extract-interface.ts

--08f7cb7a--2020-01-23--Nicolas Carlo
1	0	.vscode/settings.json

--fac9b168--2020-01-23--Nicolas Carlo
33	0	src/refactorings/extract-interface/extract-interface.test.ts
47	1	src/refactorings/extract-interface/extract-interface.ts

--c26329dd--2020-01-23--Nicolas Carlo
23	0	src/refactorings/extract-interface/extract-interface.test.ts
1	0	src/refactorings/extract-interface/extract-interface.ts

--46b0425b--2020-01-21--Nicolas Carlo
4	0	CHANGELOG.md
12	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
11	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--764da6c4--2020-01-21--Nicolas Carlo
1	1	README.md

--0a0cb5f0--2020-01-20--Nicolas Carlo
4	0	src/editor/error-reason.ts
80	0	src/refactorings/extract-interface/extract-interface.test.ts
55	0	src/refactorings/extract-interface/extract-interface.ts
17	0	src/refactorings/extract-interface/index.ts

--2d6a9ac7--2020-01-20--Nicolas Carlo
5	0	CHANGELOG.md

--634fdf23--2020-01-20--Nicolas Carlo
11	0	.all-contributorsrc
2	1	README.md

--326327f7--2020-01-20--Nicolas Carlo
--a189c94e--2020-01-20--Nicolas Carlo
33	35	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts

--b6e5c408--2020-01-20--Nicolas Carlo
14	10	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts

--5bf8ede9--2020-01-20--Nicolas Carlo
2	2	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
1	1	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts

--7005492f--2020-01-20--Nicolas Carlo
--17c38f5e--2020-01-20--Nicolas Carlo
--5a6197c7--2020-01-20--Nicolas Carlo
1	1	README.md
2	2	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
2	2	src/refactorings/react/add-braces-to-jsx-attribute/index.ts

--e5896c14--2020-01-20--Nicolas Carlo
4	6	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts

--8611a901--2020-01-20--Nicolas Carlo
13	0	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts

--ebed22ca--2020-01-20--Tim van Cleef
39	0	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts

--9b3e274e--2020-01-20--Tim van Cleef
11	0	README.md
-	-	docs/demo/remove-braces-from-jsx-attribute.gif

--829d09a1--2020-01-20--Tim van Cleef
5	1	src/editor/error-reason.ts
3	0	src/extension.ts
20	0	src/refactorings/react/remove-braces-from-jsx-attribute/index.ts
89	0	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts
53	0	src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts

--5f5382c6--2020-01-16--Tim van Cleef
26	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts

--ed70e1d6--2020-01-16--Tim van Cleef
12	0	README.md
-	-	docs/demo/add-braces-to-jsx-attribute.gif

--5605596d--2020-01-16--Tim van Cleef
12	1	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
7	6	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts

--46bbede5--2020-01-16--Tim van Cleef
5	1	src/editor/error-reason.ts
3	0	src/extension.ts
47	0	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts
53	0	src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts
20	0	src/refactorings/react/add-braces-to-jsx-attribute/index.ts

--1351a5a5--2020-01-17--Nicolas Carlo
0	9	.vscode/launch.json
1	10	.vscode/tasks.json

--a72c6f98--2020-01-14--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--8efd79a8--2020-01-14--Nicolas Carlo
11	0	README.md
-	-	docs/demo/add-braces-to-if-statement.gif
-	-	docs/demo/simplify-ternary.gif

--4e59026b--2020-01-14--Nicolas Carlo
13	1	src/refactorings/simplify-ternary/simplify-ternary.test.ts

--598bee01--2020-01-14--Nicolas Carlo
5	0	CHANGELOG.md

--cabb337a--2020-01-14--Nicolas Carlo
18	0	CONTRIBUTING.md
-	-	docs/contributing/debugger-build.png
-	-	docs/contributing/debugger-rebuild.png
-	-	docs/contributing/extension-development-host.png

--42ad4cd8--2020-01-14--Nicolas Carlo
12	8	_templates/refactoring/new/index.js

--b1631d34--2020-01-14--Nicolas Carlo
1	1	README.md

--b14f0d59--2020-01-14--Nicolas Carlo
5	1	README.md

--de29089e--2020-01-14--Nicolas Carlo
--027eba07--2020-01-14--Nicolas Carlo
10	0	.all-contributorsrc
2	1	README.md

--f498db0a--2020-01-14--Nicolas Carlo
--fe563f12--2020-01-14--Nicolas Carlo
22	10	package.json

--2f165bdb--2020-01-14--Nicolas Carlo
--0d255570--2020-01-14--Alexander Rose
22	0	package.json
4	0	src/editor/error-reason.ts
3	0	src/extension.ts
17	0	src/refactorings/simplify-ternary/index.ts
76	0	src/refactorings/simplify-ternary/simplify-ternary.test.ts
95	0	src/refactorings/simplify-ternary/simplify-ternary.ts

--4f4b13cf--2020-01-13--Nicolas Carlo
5	5	src/extension.ts

--27d3236c--2020-01-13--Nicolas Carlo
9	9	README.md

--e2062056--2020-01-13--Nicolas Carlo
1	1	README.md

--0b548b65--2020-01-13--Nicolas Carlo
21	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
12	15	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts

--b2005638--2020-01-13--Nicolas Carlo
11	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts

--77931e15--2020-01-13--Nicolas Carlo
11	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
23	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts

--a1c85167--2020-01-13--Nicolas Carlo
16	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts

--6e6d3caf--2020-01-13--Nicolas Carlo
5	5	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts

--e813f82d--2020-01-13--Nicolas Carlo
1	1	src/refactorings/add-braces-to-if-statement/index.ts

--085b066b--2019-12-29--Nicolas Carlo
--210f4cd8--2019-12-28--dependabot[bot]
17	6	yarn.lock

--a845b565--2019-12-27--Guillaume Lagorce
1	1	src/extension.ts

--aae98a31--2019-12-27--Guillaume Lagorce
14	1	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
20	12	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts

--bb911510--2019-12-15--Guillaume Lagorce
10	0	package.json
5	2	src/extension.ts

--14cf23d4--2019-12-15--Guillaume Lagorce
25	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts

--3877c0a6--2019-12-05--Guillaume Lagorce
9	0	README.md
4	0	src/editor/error-reason.ts
54	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts
59	0	src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts
20	0	src/refactorings/add-braces-to-if-statement/index.ts

--6529857f--2019-12-19--Nicolas Carlo
9	2	CHANGELOG.md
1	1	package.json

--6a1e9166--2019-12-19--Nicolas Carlo
1	1	package.json
4	3	yarn.lock

--f2a816aa--2019-12-19--Nicolas Carlo
0	1	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--b1eaa56b--2019-12-19--Nicolas Carlo
1	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--bb7abd0f--2019-12-19--Nicolas Carlo
4	0	CHANGELOG.md
6	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
14	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--0cd65e96--2019-12-19--Nicolas Carlo
--d9d65c85--2019-12-19--Nicolas Carlo
26	0	CHANGELOG.md

--8b8f4b2e--2019-12-19--Nicolas Carlo
35	13	src/refactorings/extract-variable/extract-variable.ts

--99ebb92e--2019-12-19--Nicolas Carlo
1	1	src/ast/transformation.ts
6	6	src/refactorings/extract-variable/extract-variable.extractable.test.ts
11	4	src/refactorings/extract-variable/extract-variable.ts

--4478f8d9--2019-12-19--Nicolas Carlo
--c1032440--2019-12-19--Nicolas Carlo
1	1	.travis.yml

--5ecbcf69--2019-12-18--Nicolas Carlo
4	0	CHANGELOG.md
14	0	README.md
-	-	docs/demo/convert-to-pure-component.gif

--b4445e3b--2019-12-18--Nicolas Carlo
6	2	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts

--b97ba7dd--2019-12-18--Nicolas Carlo
5	7	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--f8fe8227--2019-12-18--Nicolas Carlo
48	0	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
23	6	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts

--f2b34947--2019-12-16--Nicolas Carlo
6	0	package.json
4	0	src/editor/error-reason.ts
3	0	src/extension.ts
92	0	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts
52	0	src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts
20	0	src/refactorings/react/convert-to-pure-component/index.ts

--c02565d1--2019-12-16--Nicolas Carlo
6	1	src/ast/transformation.ts

--de525106--2019-12-13--Nicolas Carlo
3	0	package.json
1480	21	yarn.lock

--fc7b2602--2019-12-16--Nicolas Carlo
5	0	README.md

--3a611afa--2019-12-16--Nicolas Carlo
4	0	CHANGELOG.md
6	6	README.md
0	2	package.json

--650876cd--2019-12-15--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/improvement.md

--c8dd5082--2019-12-16--Nicolas Carlo
76	0	CODE_OF_CONDUCT.md

--8eb5340a--2019-12-12--Nicolas Carlo
12	1	CHANGELOG.md
1	1	package.json

--01b9842b--2019-12-10--Nicolas Carlo
4	0	CHANGELOG.md
4	0	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
7	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--126f7b3d--2019-12-10--Nicolas Carlo
1	0	CHANGELOG.md
19	21	README.md
5	0	package.json
6	0	src/extension.ts

--c7746159--2019-12-09--Nicolas Carlo
26	43	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--9e62a57c--2019-12-09--Nicolas Carlo
28	0	CHANGELOG.md
17	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
57	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--34396c9d--2019-12-09--Nicolas Carlo
12	6	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--927174c4--2019-12-09--Nicolas Carlo
16	0	src/ast/domain.ts
22	0	src/ast/identity.ts
32	62	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--93eab906--2019-12-09--Nicolas Carlo
12	0	src/ast/domain.ts
33	44	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--d77b12a1--2019-12-09--Nicolas Carlo
74	19	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--f4e66e54--2019-12-09--Nicolas Carlo
5	3	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--636dc70e--2019-12-05--Nicolas Carlo
24	0	.all-contributorsrc
4	1	README.md

--55698427--2019-12-05--Nicolas Carlo
0	10	README.md

--fcbaa252--2019-12-05--allcontributors[bot]
10	0	.all-contributorsrc

--95271092--2019-12-05--allcontributors[bot]
2	8	README.md

--4ca09de0--2019-12-04--Nicolas Carlo
13	0	CONTRIBUTING.md
53	39	README.md

--a8cf4fad--2019-12-04--Nicolas Carlo
1	1	CHANGELOG.md
6	6	README.md
2	2	package.json

--49131bcc--2019-12-02--Nicolas Carlo
4	0	CHANGELOG.md
6	6	README.md
2	2	package.json

--b17ee2d5--2019-12-02--Nicolas Carlo
--75cfa452--2019-12-02--Nicolas Carlo
10	7	README.md

--30d6032c--2019-12-03--allcontributors[bot]
24	0	.all-contributorsrc

--0ea27d1a--2019-12-03--allcontributors[bot]
13	0	README.md

--3d53ce39--2019-11-30--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--be050bb1--2019-11-30--Nicolas Carlo
1	0	CHANGELOG.md
9	0	src/refactorings/negate-expression/negate-expression.test.ts
1	1	src/refactorings/negate-expression/negate-expression.ts

--9497a254--2019-11-30--Nicolas Carlo
11	11	src/refactorings/negate-expression/negate-expression.test.ts
6	31	src/refactorings/negate-expression/negate-expression.ts

--0393524c--2019-11-30--Nicolas Carlo
1	0	CHANGELOG.md
18	0	src/refactorings/negate-expression/negate-expression.test.ts
7	4	src/refactorings/negate-expression/negate-expression.ts

--5e5e156a--2019-11-30--Nicolas Carlo
5	5	src/refactorings/extract-variable/extract-variable.ts

--8b889b6a--2019-11-30--Nicolas Carlo
2	2	src/ast/identity.ts
44	28	src/refactorings/extract-variable/extract-variable.ts

--735a07a1--2019-11-30--Nicolas Carlo
4	0	CHANGELOG.md
14	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
11	4	src/refactorings/extract-variable/extract-variable.ts

--c916df05--2019-11-28--Nicolas Carlo
71	15	CHANGELOG.md

--a60493b3--2019-11-28--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--2f6fe10b--2019-11-28--Nicolas Carlo
34	0	CHANGELOG.md

--83c44313--2019-11-28--Nicolas Carlo
6	1	src/ast/identity.ts
31	27	src/refactorings/extract-variable/extract-variable.ts

--44b22411--2019-11-28--Nicolas Carlo
14	14	src/refactorings/extract-variable/extract-variable.extractable.test.ts
18	4	src/refactorings/extract-variable/extract-variable.ts

--ae196f34--2019-11-28--Nicolas Carlo
9	5	src/refactorings/extract-variable/extract-variable.ts

--8a8f25e0--2019-11-28--Nicolas Carlo
42	33	src/editor/adapters/in-memory-editor.ts
3	3	src/editor/adapters/vscode-editor.ts
18	12	src/editor/editor-contract-test.ts
3	3	src/editor/editor.ts
3	3	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
7	7	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--6ca01286--2019-11-28--Nicolas Carlo
4	0	CHANGELOG.md
5	0	src/refactorings/extract-variable/extract-variable.non-extractable.test.ts
1	0	src/refactorings/extract-variable/extract-variable.ts

--f13b1971--2019-11-21--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--060d1bf3--2019-11-20--Nicolas Carlo
1	0	CHANGELOG.md

--90e89287--2019-11-20--Nicolas Carlo
42	0	src/refactorings/move-statement-down/move-statement-down.test.ts
6	2	src/refactorings/move-statement-down/move-statement-down.ts
42	0	src/refactorings/move-statement-up/move-statement-up.test.ts
8	4	src/refactorings/move-statement-up/move-statement-up.ts

--8c9f618d--2019-11-20--Nicolas Carlo
1	0	CHANGELOG.md
18	11	src/refactorings/extract-variable/extract-variable.extractable.test.ts
14	4	src/refactorings/extract-variable/extract-variable.ts

--01dfd280--2019-11-20--Nicolas Carlo
1	0	CHANGELOG.md
14	12	src/editor/adapters/vscode-editor.ts

--3931225c--2019-11-20--Nicolas Carlo
28	16	src/refactorings/extract-variable/extract-variable.ts

--97de8b3f--2019-11-20--Nicolas Carlo
4	0	CHANGELOG.md
7	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
5	1	src/refactorings/extract-variable/extract-variable.ts

--c83d5adc--2019-11-19--Nicolas Carlo
21	0	src/refactorings/move-statement-up/move-statement-up.test.ts
25	2	src/refactorings/move-statement-up/move-statement-up.ts

--3b95c766--2019-11-19--Nicolas Carlo
11	1	src/editor/position.ts
2	15	src/refactorings/move-statement-down/move-statement-down.ts

--8bcab3f4--2019-11-19--Nicolas Carlo
4	1	src/refactorings/move-statement-down/move-statement-down.ts

--d4057b44--2019-11-19--Nicolas Carlo
18	0	src/refactorings/move-statement-down/move-statement-down.test.ts
6	3	src/refactorings/move-statement-down/move-statement-down.ts

--87f8b17c--2019-11-19--Nicolas Carlo
4	0	CHANGELOG.md
11	1	src/refactorings/extract-variable/extract-variable.extractable.test.ts
5	2	src/refactorings/extract-variable/extract-variable.ts

--bc00bdc8--2019-11-18--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--80f0f4c6--2019-11-18--Nicolas Carlo
1	1	package.json

--0b1070c0--2019-11-18--Nicolas Carlo
4	0	CHANGELOG.md
3	1	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
7	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--11bb3372--2019-11-15--Nicolas Carlo
7	2	CHANGELOG.md
1	1	package.json

--9f1f5130--2019-11-15--Nicolas Carlo
1	1	package.json

--58c4d364--2019-11-14--Nicolas Carlo
1	0	CHANGELOG.md
14	5	src/editor/adapters/vscode-editor.ts

--04013fd6--2019-11-14--Nicolas Carlo
4	0	CHANGELOG.md
4	0	src/editor/position.ts
19	2	src/refactorings/move-statement-down/move-statement-down.test.ts
10	3	src/refactorings/move-statement-down/move-statement-down.ts

--57669834--2019-11-13--Nicolas Carlo
1	1	CHANGELOG.md

--87f258e9--2019-11-13--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--5cc121f2--2019-11-11--Nicolas Carlo
21	0	CHANGELOG.md

--82cfb9e4--2019-11-11--Nicolas Carlo
22	16	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--ab81c158--2019-11-11--Nicolas Carlo
32	36	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--9eb235f3--2019-11-11--Nicolas Carlo
35	40	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--8857f0a3--2019-11-11--Nicolas Carlo
22	19	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts

--65790c6f--2019-11-10--Nicolas Carlo
0	28	_templates/refactoring/new/action-provider.ejs.t
0	19	_templates/refactoring/new/command.ejs.t
44	0	_templates/refactoring/new/index.ejs.t
7	6	_templates/refactoring/new/refactoring.ejs.t

--d2172cd7--2019-11-10--Nicolas Carlo
0	2	package.json
0	17	yarn.lock

--a090b731--2019-11-10--Nicolas Carlo
1	4	src/action-providers.ts

--c66d77a5--2019-11-10--Nicolas Carlo
3	2	src/extension.ts

--561e1580--2019-11-10--Nicolas Carlo
16	1	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
23	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
25	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
16	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
16	1	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
25	1	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
24	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
16	1	src/refactorings/flip-if-else/flip-if-else.ts
16	1	src/refactorings/flip-ternary/flip-ternary.ts
25	0	src/refactorings/merge-if-statements/merge-if-statements.ts
21	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
4	4	src/refactorings/negate-expression/index.ts
40	1	src/refactorings/negate-expression/negate-expression.ts
34	1	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts
2	0	src/refactorings/remove-dead-code/remove-dead-code.ts
23	1	src/refactorings/remove-redundant-else/remove-redundant-else.ts
39	4	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts
18	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts
21	1	src/refactorings/split-if-statement/split-if-statement.ts

--43607c10--2019-11-09--Nicolas Carlo
7	7	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--905af88b--2019-11-08--Nicolas Carlo
4	58	src/action-providers.ts
2	15	src/extension.ts
2	2	src/refactorings/add-braces-to-arrow-function/index.ts
2	2	src/refactorings/bubble-up-if-statement/index.ts
2	2	src/refactorings/convert-for-to-foreach/index.ts
2	2	src/refactorings/convert-if-else-to-switch/index.ts
2	2	src/refactorings/convert-if-else-to-ternary/index.ts
2	2	src/refactorings/convert-ternary-to-if-else/index.ts
2	2	src/refactorings/convert-to-template-literal/index.ts
2	2	src/refactorings/flip-if-else/index.ts
2	2	src/refactorings/flip-ternary/index.ts
2	2	src/refactorings/merge-if-statements/index.ts
2	2	src/refactorings/merge-with-previous-if-statement/index.ts
2	2	src/refactorings/negate-expression/index.ts
2	2	src/refactorings/remove-braces-from-arrow-function/index.ts
2	2	src/refactorings/remove-dead-code/index.ts
2	2	src/refactorings/remove-redundant-else/index.ts
2	2	src/refactorings/replace-binary-with-assignment/index.ts
2	2	src/refactorings/split-declaration-and-initialization/index.ts
2	2	src/refactorings/split-if-statement/index.ts
1	19	src/types.ts

--8c9527f7--2019-11-08--Nicolas Carlo
3	2	src/extension.ts
2	2	src/refactorings/split-if-statement/index.ts
14	14	src/refactorings/split-if-statement/split-if-statement.ts

--6f0d3206--2019-11-08--Nicolas Carlo
3	5	src/extension.ts
2	2	src/refactorings/split-declaration-and-initialization/index.ts
13	13	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--28a142ad--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
5	5	src/refactorings/replace-binary-with-assignment/index.ts
13	13	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--344505e8--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
2	2	src/refactorings/remove-redundant-else/index.ts
13	13	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--9836d961--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
2	2	src/refactorings/remove-dead-code/index.ts
32	32	src/refactorings/remove-dead-code/remove-dead-code.ts

--0a48be18--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
2	2	src/refactorings/remove-braces-from-arrow-function/index.ts
12	12	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--d26b9c96--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
5	5	src/refactorings/negate-expression/index.ts
4	3	src/refactorings/negate-expression/negate-expression.test.ts
30	30	src/refactorings/negate-expression/negate-expression.ts

--466f0aea--2019-11-08--Nicolas Carlo
1	0	src/ast/transformation.ts

--c5b557c6--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
2	2	src/refactorings/merge-with-previous-if-statement/index.ts
25	28	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--00e20f2d--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
5	8	src/refactorings/merge-if-statements/index.ts
21	23	src/refactorings/merge-if-statements/merge-if-statements.ts

--85498430--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
12	12	src/refactorings/flip-ternary/flip-ternary.ts
2	2	src/refactorings/flip-ternary/index.ts

--2299238b--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
27	31	src/refactorings/flip-if-else/flip-if-else.ts
2	2	src/refactorings/flip-if-else/index.ts

--6cbcabed--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
37	37	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts
2	2	src/refactorings/convert-to-template-literal/index.ts

--973afb17--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
28	28	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
2	2	src/refactorings/convert-ternary-to-if-else/index.ts

--172788e7--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
31	35	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
2	2	src/refactorings/convert-if-else-to-ternary/index.ts

--06da0f1f--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
25	25	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
2	2	src/refactorings/convert-if-else-to-switch/index.ts

--d9c463fc--2019-11-08--Nicolas Carlo
2	2	src/extension.ts
40	40	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
2	2	src/refactorings/convert-for-to-foreach/index.ts

--9fdb6ab6--2019-11-08--Nicolas Carlo
4	2	src/extension.ts
27	27	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
2	2	src/refactorings/bubble-up-if-statement/index.ts

--e5c06f22--2019-11-08--Nicolas Carlo
14	2	src/extension.ts
11	11	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
2	2	src/refactorings/add-braces-to-arrow-function/index.ts

--204d5a5b--2019-11-08--Nicolas Carlo
2	0	package.json
4	1	src/action-providers.ts
17	0	yarn.lock

--60d752aa--2019-11-08--Nicolas Carlo
56	2	src/action-providers.ts
20	1	src/types.ts

--c852596b--2019-11-08--Nicolas Carlo
13	0	src/ast/transformation.ts

--4fc0f0a9--2019-11-08--Nicolas Carlo
7	4	src/ast/transformation.ts

--f79db35c--2019-11-08--Nicolas Carlo
10	5	src/ast/transformation.ts
4	2	src/refactorings/extract-variable/extract-variable.ts
1	1	src/refactorings/inline-variable-or-function/inline-variable.ts
1	1	src/refactorings/negate-expression/negate-expression.ts

--e19a1be2--2019-11-08--Nicolas Carlo
2	2	src/ast/transformation.ts

--fd80e4ab--2019-11-08--Nicolas Carlo
9	0	.vscode/launch.json

--0119d6b2--2019-11-08--Nicolas Carlo
5	5	src/action-providers.ts
3	3	src/extension.ts
9	5	src/refactorings/add-braces-to-arrow-function/index.ts
10	6	src/refactorings/bubble-up-if-statement/index.ts
10	6	src/refactorings/convert-for-to-foreach/index.ts
10	6	src/refactorings/convert-if-else-to-switch/index.ts
9	5	src/refactorings/convert-if-else-to-ternary/index.ts
9	5	src/refactorings/convert-ternary-to-if-else/index.ts
10	6	src/refactorings/convert-to-template-literal/index.ts
4	2	src/refactorings/extract-variable/index.ts
10	6	src/refactorings/flip-if-else/index.ts
10	6	src/refactorings/flip-ternary/index.ts
4	2	src/refactorings/inline-variable-or-function/index.ts
15	10	src/refactorings/merge-if-statements/index.ts
10	6	src/refactorings/merge-with-previous-if-statement/index.ts
4	2	src/refactorings/move-statement-down/index.ts
4	2	src/refactorings/move-statement-up/index.ts
15	13	src/refactorings/negate-expression/index.ts
9	5	src/refactorings/remove-braces-from-arrow-function/index.ts
10	6	src/refactorings/remove-dead-code/index.ts
10	6	src/refactorings/remove-redundant-else/index.ts
11	7	src/refactorings/rename-symbol/index.ts
13	9	src/refactorings/replace-binary-with-assignment/index.ts
9	5	src/refactorings/split-declaration-and-initialization/index.ts
9	5	src/refactorings/split-if-statement/index.ts
14	6	src/types.ts

--db635510--2019-11-08--Nicolas Carlo
1	9	src/commands.ts
8	4	src/types.ts

--a845633b--2019-11-08--Nicolas Carlo
1	1	src/action-providers.ts
4	1	src/extension.ts
1	1	src/refactorings/add-braces-to-arrow-function/index.ts
1	1	src/refactorings/bubble-up-if-statement/index.ts
1	1	src/refactorings/convert-for-to-foreach/index.ts
1	1	src/refactorings/convert-if-else-to-switch/index.ts
1	1	src/refactorings/convert-if-else-to-ternary/index.ts
1	1	src/refactorings/convert-ternary-to-if-else/index.ts
1	1	src/refactorings/convert-to-template-literal/index.ts
1	1	src/refactorings/extract-variable/index.ts
1	1	src/refactorings/flip-if-else/index.ts
1	1	src/refactorings/flip-ternary/index.ts
1	1	src/refactorings/inline-variable-or-function/index.ts
1	1	src/refactorings/merge-if-statements/index.ts
1	1	src/refactorings/merge-with-previous-if-statement/index.ts
1	1	src/refactorings/move-statement-down/index.ts
1	1	src/refactorings/move-statement-up/index.ts
1	1	src/refactorings/negate-expression/index.ts
1	1	src/refactorings/remove-braces-from-arrow-function/index.ts
1	1	src/refactorings/remove-dead-code/index.ts
1	1	src/refactorings/remove-redundant-else/index.ts
1	1	src/refactorings/rename-symbol/index.ts
1	1	src/refactorings/replace-binary-with-assignment/index.ts
1	1	src/refactorings/split-declaration-and-initialization/index.ts
1	1	src/refactorings/split-if-statement/index.ts

--8a69659b--2019-11-08--Nicolas Carlo
33	47	src/extension.ts
0	12	src/refactorings/add-braces-to-arrow-function/command.ts
1	2	src/refactorings/add-braces-to-arrow-function/index.ts
0	12	src/refactorings/bubble-up-if-statement/command.ts
1	2	src/refactorings/bubble-up-if-statement/index.ts
0	12	src/refactorings/convert-for-to-foreach/command.ts
1	2	src/refactorings/convert-for-to-foreach/index.ts
0	12	src/refactorings/convert-if-else-to-switch/command.ts
1	2	src/refactorings/convert-if-else-to-switch/index.ts
0	12	src/refactorings/convert-if-else-to-ternary/command.ts
1	2	src/refactorings/convert-if-else-to-ternary/index.ts
0	12	src/refactorings/convert-ternary-to-if-else/command.ts
1	2	src/refactorings/convert-ternary-to-if-else/index.ts
0	12	src/refactorings/convert-to-template-literal/command.ts
1	2	src/refactorings/convert-to-template-literal/index.ts
0	12	src/refactorings/extract-variable/command.ts
10	0	src/refactorings/extract-variable/index.ts
0	12	src/refactorings/flip-if-else/command.ts
1	2	src/refactorings/flip-if-else/index.ts
0	12	src/refactorings/flip-ternary/command.ts
1	2	src/refactorings/flip-ternary/index.ts
0	57	src/refactorings/inline-variable-or-function/command.ts
58	0	src/refactorings/inline-variable-or-function/index.ts
0	12	src/refactorings/merge-if-statements/command.ts
1	2	src/refactorings/merge-if-statements/index.ts
0	12	src/refactorings/merge-with-previous-if-statement/command.ts
1	2	src/refactorings/merge-with-previous-if-statement/index.ts
0	12	src/refactorings/move-statement-down/command.ts
10	0	src/refactorings/move-statement-down/index.ts
0	12	src/refactorings/move-statement-up/command.ts
10	0	src/refactorings/move-statement-up/index.ts
0	12	src/refactorings/negate-expression/command.ts
1	2	src/refactorings/negate-expression/index.ts
0	12	src/refactorings/remove-braces-from-arrow-function/command.ts
1	2	src/refactorings/remove-braces-from-arrow-function/index.ts
0	12	src/refactorings/remove-dead-code/command.ts
1	2	src/refactorings/remove-dead-code/index.ts
0	12	src/refactorings/remove-redundant-else/command.ts
1	2	src/refactorings/remove-redundant-else/index.ts
0	19	src/refactorings/rename-symbol/command.ts
22	0	src/refactorings/rename-symbol/index.ts
0	12	src/refactorings/replace-binary-with-assignment/command.ts
1	2	src/refactorings/replace-binary-with-assignment/index.ts
0	12	src/refactorings/split-declaration-and-initialization/command.ts
1	2	src/refactorings/split-declaration-and-initialization/index.ts
0	12	src/refactorings/split-if-statement/command.ts
1	2	src/refactorings/split-if-statement/index.ts

--8fd29db4--2019-11-08--Nicolas Carlo
2	2	src/types.ts

--07a60adf--2019-11-08--Nicolas Carlo
4	3	src/commands.ts
5	1	src/refactorings/add-braces-to-arrow-function/index.ts
5	1	src/refactorings/bubble-up-if-statement/index.ts
5	1	src/refactorings/convert-for-to-foreach/index.ts
5	1	src/refactorings/convert-if-else-to-switch/index.ts
5	1	src/refactorings/convert-if-else-to-ternary/index.ts
5	1	src/refactorings/convert-ternary-to-if-else/index.ts
5	1	src/refactorings/convert-to-template-literal/index.ts
2	1	src/refactorings/flip-if-else/index.ts
2	1	src/refactorings/flip-ternary/index.ts
2	1	src/refactorings/merge-if-statements/index.ts
5	1	src/refactorings/merge-with-previous-if-statement/index.ts
2	1	src/refactorings/negate-expression/index.ts
5	1	src/refactorings/remove-braces-from-arrow-function/index.ts
2	1	src/refactorings/remove-dead-code/index.ts
2	1	src/refactorings/remove-redundant-else/index.ts
5	1	src/refactorings/replace-binary-with-assignment/index.ts
5	1	src/refactorings/split-declaration-and-initialization/index.ts
2	1	src/refactorings/split-if-statement/index.ts
3	0	src/types.ts

--ca72283b--2019-11-07--Nicolas Carlo
1	54	src/action-providers.ts
2	2	src/extension.ts

--e5ff742d--2019-11-07--Nicolas Carlo
2	6	src/extension.ts
0	26	src/refactorings/replace-binary-with-assignment/action-provider.ts
22	0	src/refactorings/replace-binary-with-assignment/index.ts

--87a9de2e--2019-11-07--Nicolas Carlo
5	5	src/extension.ts
0	28	src/refactorings/negate-expression/action-provider.ts
27	0	src/refactorings/negate-expression/index.ts

--be4ddc5a--2019-11-07--Nicolas Carlo
2	2	src/extension.ts
0	25	src/refactorings/merge-if-statements/action-provider.ts
23	0	src/refactorings/merge-if-statements/index.ts

--0cade193--2019-11-07--Nicolas Carlo
3	3	src/extension.ts
0	18	src/refactorings/split-declaration-and-initialization/action-provider.ts
13	0	src/refactorings/split-declaration-and-initialization/index.ts

--368cc44e--2019-11-07--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/remove-redundant-else/action-provider.ts
14	0	src/refactorings/remove-redundant-else/index.ts

--090984a0--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/remove-dead-code/action-provider.ts
14	0	src/refactorings/remove-dead-code/index.ts

--5404c82d--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	18	src/refactorings/remove-braces-from-arrow-function/action-provider.ts
13	0	src/refactorings/remove-braces-from-arrow-function/index.ts

--3138746a--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	19	src/refactorings/merge-with-previous-if-statement/action-provider.ts
14	0	src/refactorings/merge-with-previous-if-statement/index.ts

--74cd5421--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/flip-ternary/action-provider.ts
14	0	src/refactorings/flip-ternary/index.ts

--53dd6028--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/flip-if-else/action-provider.ts
14	0	src/refactorings/flip-if-else/index.ts

--33c67f29--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	19	src/refactorings/convert-to-template-literal/action-provider.ts
14	0	src/refactorings/convert-to-template-literal/index.ts

--2c09e59f--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	18	src/refactorings/convert-ternary-to-if-else/action-provider.ts
13	0	src/refactorings/convert-ternary-to-if-else/index.ts

--1afed651--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	18	src/refactorings/convert-if-else-to-ternary/action-provider.ts
13	0	src/refactorings/convert-if-else-to-ternary/index.ts

--78e2cae1--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	19	src/refactorings/convert-if-else-to-switch/action-provider.ts
14	0	src/refactorings/convert-if-else-to-switch/index.ts

--bf350e3f--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/convert-for-to-foreach/action-provider.ts
14	0	src/refactorings/convert-for-to-foreach/index.ts

--46231a7b--2019-11-06--Nicolas Carlo
2	2	src/extension.ts
0	17	src/refactorings/bubble-up-if-statement/action-provider.ts
14	0	src/refactorings/bubble-up-if-statement/index.ts

--95b50b9f--2019-11-06--Nicolas Carlo
6	5	src/extension.ts
0	18	src/refactorings/add-braces-to-arrow-function/action-provider.ts
13	0	src/refactorings/add-braces-to-arrow-function/index.ts

--e5cfd238--2019-11-06--Nicolas Carlo
3	6	src/action-providers.ts

--86e62142--2019-11-06--Nicolas Carlo
50	0	src/action-providers.ts
13	3	src/extension.ts
0	16	src/refactorings/split-if-statement/action-provider.ts
13	0	src/refactorings/split-if-statement/index.ts
15	0	src/types.ts

--2b94d47a--2019-11-06--Nicolas Carlo
6	6	src/action-providers.ts

--f5e772a0--2019-10-29--Nicolas Carlo
7	26	src/refactorings/split-if-statement/action-provider.ts

--e5bfd7d8--2019-10-29--Nicolas Carlo
7	30	src/refactorings/split-declaration-and-initialization/action-provider.ts

--54b670d4--2019-10-29--Nicolas Carlo
11	26	src/refactorings/replace-binary-with-assignment/action-provider.ts

--08e9b5a8--2019-10-29--Nicolas Carlo
8	26	src/refactorings/remove-redundant-else/action-provider.ts

--74ad1d7d--2019-10-29--Nicolas Carlo
8	26	src/refactorings/remove-dead-code/action-provider.ts

--cf627f06--2019-10-29--Nicolas Carlo
7	30	src/refactorings/remove-braces-from-arrow-function/action-provider.ts

--46ed5091--2019-10-29--Nicolas Carlo
13	26	src/refactorings/negate-expression/action-provider.ts

--0cfbc2f1--2019-10-29--Nicolas Carlo
8	29	src/refactorings/merge-with-previous-if-statement/action-provider.ts

--e2d5d4ef--2019-10-29--Nicolas Carlo
13	28	src/refactorings/merge-if-statements/action-provider.ts

--e14f0d90--2019-10-28--Nicolas Carlo
8	26	src/refactorings/flip-ternary/action-provider.ts

--3b10bb40--2019-10-28--Nicolas Carlo
8	26	src/refactorings/flip-if-else/action-provider.ts

--1fbf77e8--2019-10-28--Nicolas Carlo
7	29	src/refactorings/convert-ternary-to-if-else/action-provider.ts
8	29	src/refactorings/convert-to-template-literal/action-provider.ts

--9a231892--2019-10-28--Nicolas Carlo
7	29	src/refactorings/convert-if-else-to-ternary/action-provider.ts

--8e1abc70--2019-10-28--Nicolas Carlo
6	25	_templates/refactoring/new/action-provider.ejs.t

--3fddabdc--2019-10-28--Nicolas Carlo
8	29	src/refactorings/convert-if-else-to-switch/action-provider.ts

--6307407c--2019-10-28--Nicolas Carlo
8	26	src/refactorings/convert-for-to-foreach/action-provider.ts

--45780cbb--2019-10-28--Nicolas Carlo
8	29	src/refactorings/bubble-up-if-statement/action-provider.ts

--444e992a--2019-10-28--Nicolas Carlo
7	29	src/refactorings/add-braces-to-arrow-function/action-provider.ts

--2230026c--2019-10-28--Nicolas Carlo
47	1	src/action-providers.ts

--623b0cee--2019-10-31--Nicolas Carlo
--3c9e2cc2--2019-10-31--Nicolas Carlo
2	2	src/refactorings/move-statement-down/move-statement-down.test.ts
2	2	src/refactorings/move-statement-up/move-statement-up.test.ts

--517a9659--2019-10-31--Nicolas Carlo
52	0	CHANGELOG.md

--d5acf9c6--2019-10-31--Nicolas Carlo
0	1	src/refactorings/move-statement-down/move-statement-down.ts
1	4	src/refactorings/move-statement-up/move-statement-up.ts

--e600d9b9--2019-10-31--Nicolas Carlo
9	0	src/refactorings/move-statement-down/move-statement-down.test.ts
16	0	src/refactorings/move-statement-down/move-statement-down.ts
9	0	src/refactorings/move-statement-up/move-statement-up.test.ts
16	0	src/refactorings/move-statement-up/move-statement-up.ts

--52d4ac61--2019-10-31--Nicolas Carlo
7	0	src/editor/selection.ts

--04b6b939--2019-10-27--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--06cb0920--2019-10-24--Nicolas Carlo
4	0	CHANGELOG.md
4	1	src/ast/identity.ts
10	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--99ece876--2019-10-19--Nicolas Carlo
--7e661077--2019-10-19--Nicolas Carlo
48	0	CHANGELOG.md

--862712bf--2019-10-19--Nicolas Carlo
48	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
20	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--84075a65--2019-10-19--Nicolas Carlo
8	14	src/refactorings/merge-if-statements/merge-if-statements.ts

--d0cab2a0--2019-10-18--Nicolas Carlo
--0bbd9849--2019-10-18--Nicolas Carlo
7	1	src/refactorings/inline-variable-or-function/inline-variable.ts

--b575917d--2019-10-17--Nicolas Carlo
22	0	CHANGELOG.md

--4dea6320--2019-10-17--Nicolas Carlo
5	0	src/refactorings/inline-variable-or-function/find-exported-id-names.ts
53	0	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
101	1	src/refactorings/inline-variable-or-function/inline-variable.test.ts
21	1	src/refactorings/inline-variable-or-function/inline-variable.ts

--44c5ffce--2019-10-17--Nicolas Carlo
8	1	src/ast/selection.ts

--5951c6f3--2019-09-28--Nicolas Carlo
1	2	src/refactorings/inline-variable-or-function/inline-variable.ts

--bf12b815--2019-10-17--Nicolas Carlo
1	1	CHANGELOG.md

--61e446f2--2019-10-17--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--ed7e0547--2019-09-28--Nicolas Carlo
1	1	CHANGELOG.md

--a24f53f8--2019-09-28--Nicolas Carlo
--e7dfee72--2019-09-27--Nicolas Carlo
30	0	CHANGELOG.md

--b7124461--2019-09-27--Nicolas Carlo
6	1	src/refactorings/extract-variable/extract-variable.ts
23	0	src/refactorings/extract-variable/extract-variable.variable-name.test.ts

--541d3f35--2019-09-27--Nicolas Carlo
1	0	package.json
6	6	src/refactorings/extract-variable/extract-variable.basic-extraction.test.ts
20	20	src/refactorings/extract-variable/extract-variable.extractable.test.ts
14	14	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
6	0	src/refactorings/extract-variable/extract-variable.ts
34	0	src/refactorings/extract-variable/extract-variable.variable-name.test.ts
1	1	yarn.lock

--e4fba87d--2019-09-26--Nicolas Carlo
--1465a5af--2019-09-26--Nicolas Carlo
-	-	docs/demo/inline-variable-destructured-array-pattern.gif
-	-	docs/demo/inline-variable-destructured-patterns.gif

--0a3ca63c--2019-09-26--Nicolas Carlo
21	0	CHANGELOG.md

--16d9c116--2019-09-26--Nicolas Carlo
31	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--a6c1d5db--2019-09-26--Nicolas Carlo
22	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--f199ae2a--2019-09-26--Nicolas Carlo
8	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--50ba6ec7--2019-09-26--Nicolas Carlo
52	12	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
24	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--b3580b47--2019-09-26--Nicolas Carlo
22	14	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--792735e0--2019-09-26--Nicolas Carlo
25	16	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
11	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--072d9034--2019-09-25--Nicolas Carlo
7	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--d3c33812--2019-09-25--Nicolas Carlo
28	0	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
72	0	src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts

--9d84ffff--2019-09-26--Nicolas Carlo
--aa4d205a--2019-09-26--Nicolas Carlo
1	1	README.md

--8258b2db--2019-09-26--Nicolas Carlo
4	0	CHANGELOG.md

--788ca918--2019-09-26--Nicolas Carlo
5	2	src/ast/transformation.ts

--56c75d9b--2019-09-26--Nicolas Carlo
15	4	src/ast/transformation.test.ts

--e34d50ba--2019-09-26--Fabien BERNARD
10	0	src/ast/transformation.test.ts
2	1	src/ast/transformation.ts

--d33d80e2--2019-09-25--Nicolas Carlo
32	0	docs/adr/0007-usage-of-readthenwrite-vs-write.md

--015e2206--2019-09-24--Nicolas Carlo
-	-	docs/demo/inline-variable-destructured-object.gif

--ac9fc5ae--2019-09-22--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--ff3a6682--2019-09-22--Nicolas Carlo
--62b04509--2019-09-22--Nicolas Carlo
1	0	CHANGELOG.md
13	0	README.md
-	-	docs/demo/remove-dead-code.gif

--6dcfc9e3--2019-09-22--Nicolas Carlo
30	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
40	0	src/refactorings/remove-dead-code/remove-dead-code.ts

--54583a32--2019-09-22--Nicolas Carlo
9	0	src/ast/domain.ts
31	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
65	18	src/refactorings/remove-dead-code/remove-dead-code.ts

--921ff744--2019-09-22--Nicolas Carlo
23	9	src/ast/identity.ts
31	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
30	10	src/refactorings/remove-dead-code/remove-dead-code.ts

--279f242e--2019-09-22--Nicolas Carlo
4	1	package.json

--91b17331--2019-09-22--Nicolas Carlo
12	1	src/ast/identity.are-opposite.test.ts
6	1	src/ast/identity.ts
29	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts

--d05c93de--2019-09-21--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--9a9194bb--2019-09-21--Nicolas Carlo
49	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
18	7	src/refactorings/remove-dead-code/remove-dead-code.ts

--f1bf5c5c--2019-09-21--Nicolas Carlo
40	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
14	0	src/refactorings/remove-dead-code/remove-dead-code.ts

--a8a86fec--2019-09-21--Nicolas Carlo
66	1	src/ast/identity.are-opposite.test.ts
20	0	src/ast/identity.ts

--48725b5b--2019-09-21--Nicolas Carlo
75	0	src/ast/identity.are-opposite.test.ts
21	1	src/ast/identity.ts

--21df2e82--2019-09-21--Nicolas Carlo
15	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
2	0	src/refactorings/remove-dead-code/remove-dead-code.ts

--379938b5--2019-09-21--Nicolas Carlo
6	4	src/refactorings/remove-dead-code/remove-dead-code.ts

--96d1b69f--2019-09-21--Nicolas Carlo
0	235	src/ast/domain.ts
240	0	src/ast/identity.ts
1	0	src/ast/index.ts
1	1	src/ast/scope.ts

--91384448--2019-09-21--Nicolas Carlo
15	0	src/ast/domain.ts
8	10	src/refactorings/remove-dead-code/remove-dead-code.ts

--c5821c49--2019-09-21--Nicolas Carlo
52	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
5	1	src/refactorings/remove-dead-code/remove-dead-code.ts

--3b19fcb9--2019-09-21--Nicolas Carlo
9	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
6	0	src/refactorings/remove-dead-code/remove-dead-code.ts

--ad804ced--2019-09-21--Nicolas Carlo
9	2	src/refactorings/remove-dead-code/remove-dead-code.test.ts
5	1	src/refactorings/remove-dead-code/remove-dead-code.ts

--d8ed502e--2019-09-21--Nicolas Carlo
4	0	src/editor/error-reason.ts
35	0	src/refactorings/remove-dead-code/action-provider.ts
12	0	src/refactorings/remove-dead-code/command.ts
45	0	src/refactorings/remove-dead-code/remove-dead-code.test.ts
30	0	src/refactorings/remove-dead-code/remove-dead-code.ts

--d662e38a--2019-09-20--Nicolas Carlo
4	0	CHANGELOG.md
42	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
45	24	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--2acf9daa--2019-09-20--Nicolas Carlo
16	1	src/ast/transformation.ts

--25037914--2019-09-20--Nicolas Carlo
--c362c5be--2019-09-20--Nicolas Carlo
5	1	src/ast/transformation.ts
9	14	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts
1	1	src/refactorings/inline-variable-or-function/find-exported-id-names.ts
2	2	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--c34978fa--2019-09-20--Nicolas Carlo
4	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/convert-for-to-foreach.gif

--3b625bef--2019-09-20--Nicolas Carlo
9	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
21	15	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--23b10941--2019-09-20--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts
2	5	src/refactorings/convert-for-to-foreach/action-provider.ts

--fd683861--2019-09-19--Nicolas Carlo
25	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
1	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--c0e2dbdc--2019-09-19--Nicolas Carlo
18	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
35	5	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--e69b4f26--2019-09-19--Nicolas Carlo
9	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
13	5	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--9aca57e7--2019-09-19--Nicolas Carlo
14	12	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--8df3f3f1--2019-09-19--Nicolas Carlo
11	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
27	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--42003935--2019-09-19--Nicolas Carlo
8	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts

--120138fd--2019-09-19--Nicolas Carlo
72	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
34	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--b0ce93eb--2019-09-19--Nicolas Carlo
11	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
7	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--1efc11c4--2019-09-19--Nicolas Carlo
14	1	src/ast/domain.ts
1	1	src/ast/transformation.ts
9	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
39	1	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--dd091cfa--2019-09-19--Nicolas Carlo
9	3	src/ast/transformation.ts
2	2	src/refactorings/extract-variable/extract-variable.ts
1	1	src/refactorings/inline-variable-or-function/find-exported-id-names.ts
1	1	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
1	1	src/refactorings/inline-variable-or-function/inline-variable.ts
1	1	src/refactorings/negate-expression/negate-expression.ts

--cb063b93--2019-09-19--Nicolas Carlo
2	0	package.json
10	0	yarn.lock

--1ba33707--2019-09-19--Nicolas Carlo
4	0	src/editor/error-reason.ts
38	0	src/refactorings/convert-for-to-foreach/action-provider.ts
12	0	src/refactorings/convert-for-to-foreach/command.ts
47	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts
30	0	src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts

--373edefc--2019-09-18--Nicolas Carlo
--730e89bc--2019-09-18--Nicolas Carlo
1	1	src/refactorings/extract-variable/extract-variable.ts

--7a721730--2019-09-18--Nicolas Carlo
23	0	CHANGELOG.md

--c631a5b1--2019-09-18--Nicolas Carlo
55	30	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
27	8	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
31	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--5a4e5663--2019-09-18--Nicolas Carlo
12	2	src/ast/selection.ts

--f18f6849--2019-09-17--Nicolas Carlo
21	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--183e086c--2019-09-17--Nicolas Carlo
1	0	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
48	5	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
47	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--87f41f72--2019-09-17--Nicolas Carlo
8	1	src/ast/selection.ts
89	54	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
8	12	src/refactorings/inline-variable-or-function/inline-variable.ts

--9653b076--2019-09-16--Nicolas Carlo
66	72	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
12	10	src/refactorings/inline-variable-or-function/inline-variable.ts

--578a0519--2019-09-16--Nicolas Carlo
4	4	src/editor/selection.test.ts
14	2	src/editor/selection.ts
1	1	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
32	28	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
1	1	src/refactorings/inline-variable-or-function/inline-function.ts
21	32	src/refactorings/inline-variable-or-function/inline-variable.ts

--8500496a--2019-09-15--Nicolas Carlo
20	15	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--4f8b21bf--2019-09-15--Nicolas Carlo
7	1	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
2	2	src/refactorings/inline-variable-or-function/inline-variable.ts

--dd6392a2--2019-09-15--Nicolas Carlo
0	1	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
8	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts
5	8	src/refactorings/inline-variable-or-function/inline-variable.ts

--7c66cf0d--2019-09-14--Nicolas Carlo
12	5	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
16	2	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
11	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--dede27f3--2019-09-14--Nicolas Carlo
2	8	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts

--7c1565c1--2019-09-14--Nicolas Carlo
90	0	src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts
0	66	src/refactorings/inline-variable-or-function/inline-variable.test.ts

--77046e96--2019-09-14--Nicolas Carlo
12	0	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
5	8	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
8	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts

--cb629de0--2019-09-14--Nicolas Carlo
75	0	src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts
2	1	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--fc69cdf7--2019-09-14--Nicolas Carlo
25	35	src/refactorings/inline-variable-or-function/find-inlinable-code.ts

--0edb20ce--2019-09-14--Nicolas Carlo
290	0	src/refactorings/inline-variable-or-function/find-inlinable-code.ts
10	287	src/refactorings/inline-variable-or-function/inline-variable.ts

--506a17da--2019-09-14--Nicolas Carlo
37	1	src/ast/scope.ts
1	35	src/refactorings/inline-variable-or-function/inline-variable.ts

--49cbacfc--2019-09-14--Nicolas Carlo
7	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
20	6	src/refactorings/inline-variable-or-function/inline-variable.ts

--7e8f9f2b--2019-09-14--Nicolas Carlo
4	8	src/refactorings/inline-variable-or-function/inline-variable.ts

--9ea4fd7a--2019-09-14--Nicolas Carlo
29	23	src/refactorings/inline-variable-or-function/inline-variable.ts

--864ad1f5--2019-09-14--Nicolas Carlo
2	4	src/refactorings/inline-variable-or-function/inline-variable.ts

--a0bd8041--2019-09-14--Nicolas Carlo
23	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
27	3	src/refactorings/inline-variable-or-function/inline-variable.ts

--e72289c7--2019-09-14--Nicolas Carlo
7	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
7	7	src/refactorings/inline-variable-or-function/inline-variable.ts

--215235e9--2019-09-13--Nicolas Carlo
14	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
16	10	src/refactorings/inline-variable-or-function/inline-variable.ts

--cd7aa00f--2019-09-13--Nicolas Carlo
5	7	src/refactorings/inline-variable-or-function/inline-variable.ts

--ef864235--2019-08-30--Nicolas Carlo
7	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
66	2	src/refactorings/inline-variable-or-function/inline-variable.ts

--8c5337ee--2019-09-16--Nicolas Carlo
33	11	README.md

--5a438a08--2019-09-16--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--54a593da--2019-09-13--Nicolas Carlo
37	0	CHANGELOG.md
27	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
22	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--790c9c78--2019-09-11--Nicolas Carlo
1	0	CHANGELOG.md
27	1	src/ast/transformation.ts
31	0	src/refactorings/inline-variable-or-function/inline-function.test.ts

--d21acb03--2019-09-11--Nicolas Carlo
4	0	CHANGELOG.md
3	1	src/ast/domain.ts
13	0	src/refactorings/flip-if-else/flip-if-else.test.ts

--744435ec--2019-09-11--Nicolas Carlo
--98b09f0c--2019-09-11--Nicolas Carlo
1	0	CHANGELOG.md
17	1	README.md
-	-	docs/demo/merge-if-with-previous-if-statement.gif
-	-	docs/demo/merge-with-previous-if-statement.gif

--60da2a71--2019-09-11--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--dcb117e3--2019-09-11--Nicolas Carlo
118	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
9	4	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--c878f310--2019-09-11--Nicolas Carlo
41	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
11	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--3c3adf65--2019-09-11--Nicolas Carlo
19	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
22	6	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--495b7c5d--2019-09-10--Nicolas Carlo
48	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
8	4	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--0e3f92bc--2019-09-10--Nicolas Carlo
39	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts

--b58e209d--2019-09-10--Nicolas Carlo
52	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
28	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--35eb22d7--2019-09-10--Nicolas Carlo
27	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
18	5	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--deed4cd4--2019-09-10--Nicolas Carlo
27	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
2	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--b5389b4a--2019-09-10--Nicolas Carlo
1	3	src/ast/siblings.ts

--396d0005--2019-09-10--Nicolas Carlo
20	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
12	4	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--677b2d2a--2019-09-10--Nicolas Carlo
5	0	src/ast/domain.ts
0	9	src/ast/if.ts
0	1	src/ast/index.ts
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
1	1	src/refactorings/merge-if-statements/merge-if-statements.ts
1	1	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts
1	1	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--38db33ba--2019-09-10--Nicolas Carlo
4	0	src/editor/error-reason.ts
40	0	src/refactorings/merge-with-previous-if-statement/action-provider.ts
12	0	src/refactorings/merge-with-previous-if-statement/command.ts
72	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts
44	0	src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts

--0cfa22f6--2019-09-10--Nicolas Carlo
9	0	src/ast/if.ts
1	0	src/ast/index.ts
1	3	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts
3	6	src/refactorings/merge-if-statements/merge-if-statements.ts
1	5	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--735c87bd--2019-09-10--Nicolas Carlo
7	0	src/ast/siblings.ts

--cb7bb2db--2019-09-10--Nicolas Carlo
1	1	src/array-helpers.ts
2	0	src/refactorings/flip-if-else/flip-if-else.ts
1	0	src/refactorings/inline-variable-or-function/inline-variable.ts

--a4b670ed--2019-09-10--Nicolas Carlo
--90cf866b--2019-09-10--Nicolas Carlo
1	5	src/editor/error-reason.ts

--e4dd88b6--2019-09-10--Nicolas Carlo
4	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/convert-if-else-to-switch.gif

--bd519c78--2019-09-10--Nicolas Carlo
3	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts

--f49549e1--2019-09-10--Nicolas Carlo
1	1	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--10eacc19--2019-09-10--Nicolas Carlo
17	0	src/ast/switch.test.ts
1	1	src/ast/switch.ts
18	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts

--117b6f17--2019-09-10--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--efca7af8--2019-09-10--Nicolas Carlo
61	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
15	3	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--9616ee53--2019-09-10--Nicolas Carlo
21	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts

--2aa9da56--2019-09-09--Nicolas Carlo
6	3	src/ast/domain.ts
18	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
6	3	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--225cf7cc--2019-09-09--Nicolas Carlo
16	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts

--a9ef7a8d--2019-09-09--Nicolas Carlo
49	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
25	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--acc42aec--2019-09-09--Nicolas Carlo
28	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
2	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--281d05bf--2019-09-09--Nicolas Carlo
4	0	src/editor/error-reason.ts
40	0	src/refactorings/convert-if-else-to-switch/action-provider.ts
12	0	src/refactorings/convert-if-else-to-switch/command.ts
109	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts
90	0	src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts

--152e20a7--2019-09-09--Nicolas Carlo
0	128	src/ast/discriminant.test.ts
0	12	src/ast/discriminant.ts
1	1	src/ast/index.ts
131	0	src/ast/switch.test.ts
21	0	src/ast/switch.ts

--bbbe4010--2019-09-09--Nicolas Carlo
106	18	src/ast/discriminant.test.ts
6	5	src/ast/discriminant.ts

--bc323035--2019-09-09--Nicolas Carlo
40	0	src/ast/discriminant.test.ts
11	0	src/ast/discriminant.ts
1	0	src/ast/index.ts

--9e92db41--2019-09-08--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--2670c24c--2019-09-08--Nicolas Carlo
2	2	CHANGELOG.md

--e299f82c--2019-08-29--Nicolas Carlo
82	69	src/refactorings/inline-variable-or-function/inline-variable.ts

--331296b5--2019-08-29--Nicolas Carlo
3	1	src/ast/selection.ts
4	2	src/refactorings/inline-variable-or-function/inline-variable.ts

--1888bbe6--2019-08-29--Nicolas Carlo
3	1	src/refactorings/inline-variable-or-function/inline-variable.ts

--6287c4c6--2019-08-25--Nicolas Carlo
47	22	src/refactorings/inline-variable-or-function/inline-variable.ts

--f8beb757--2019-08-25--Nicolas Carlo
22	26	src/refactorings/inline-variable-or-function/inline-variable.ts

--a9a8e6cc--2019-08-25--Nicolas Carlo
3	7	src/refactorings/inline-variable-or-function/inline-variable.ts

--b8273e2c--2019-08-25--Nicolas Carlo
92	83	src/refactorings/inline-variable-or-function/inline-variable.ts

--2c1c95e5--2019-08-25--Nicolas Carlo
25	10	src/refactorings/inline-variable-or-function/inline-variable.ts

--10ba11fa--2019-09-08--Nicolas Carlo
24	0	CHANGELOG.md
11	4	src/refactorings/extract-variable/extract-variable.extractable.test.ts
2	1	src/refactorings/extract-variable/extract-variable.ts

--4b4ef95d--2019-09-07--Nicolas Carlo
--c31b0b69--2019-09-07--dependabot[bot]
7	2	yarn.lock

--28a495fe--2019-09-07--Nicolas Carlo
--ec6a7219--2019-09-07--Nicolas Carlo
7	7	README.md

--f77dbfad--2019-09-07--Nicolas Carlo
4	0	CHANGELOG.md
12	0	README.md
-	-	docs/demo/bubble-up-if-statement.gif

--a173f67f--2019-09-07--Nicolas Carlo
11	0	src/ast/domain.ts
24	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
5	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--9a682964--2019-09-07--Nicolas Carlo
14	1	src/ast/siblings.ts
2	12	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--98f3ee85--2019-09-07--Nicolas Carlo
71	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
11	12	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--701ca6fe--2019-09-07--Nicolas Carlo
24	10	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--e1235148--2019-09-07--Nicolas Carlo
28	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
20	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--76396fd8--2019-09-07--Nicolas Carlo
1	0	src/ast/index.ts
22	0	src/ast/siblings.ts

--594d2458--2019-09-07--Nicolas Carlo
9	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--f3645acd--2019-09-05--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--14dbe264--2019-09-05--Nicolas Carlo
26	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
2	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--3ca7fe5d--2019-09-05--Nicolas Carlo
35	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
23	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--538550c4--2019-09-05--Nicolas Carlo
25	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
3	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--14105418--2019-09-05--Nicolas Carlo
10	11	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--38004098--2019-09-05--Nicolas Carlo
19	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
5	1	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--f920a21a--2019-09-05--Nicolas Carlo
14	3	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--117bd377--2019-09-05--Nicolas Carlo
19	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
1	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts

--923b9f20--2019-09-05--Nicolas Carlo
4	0	src/editor/error-reason.ts
38	0	src/refactorings/bubble-up-if-statement/action-provider.ts
57	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts
38	0	src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts
12	0	src/refactorings/bubble-up-if-statement/command.ts

--79333904--2019-09-05--Nicolas Carlo
9	1	src/ast/scope.ts

--b17cb0b5--2019-09-04--Nicolas Carlo
--ccec6644--2019-09-04--Nicolas Carlo
7	0	CHANGELOG.md
5	0	README.md
-	-	docs/demo/merge-if-statements-else-if.gif

--eb1cfc59--2019-09-04--Nicolas Carlo
20	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
3	1	src/refactorings/merge-if-statements/merge-if-statements.ts

--224d62ff--2019-09-04--Nicolas Carlo
16	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--a3ded9f1--2019-09-04--Nicolas Carlo
1	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--4ada5bd7--2019-09-04--Nicolas Carlo
41	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
11	31	src/refactorings/merge-if-statements/merge-if-statements.ts

--952eed24--2019-09-03--Nicolas Carlo
8	3	src/refactorings/merge-if-statements/action-provider.ts
21	5	src/refactorings/merge-if-statements/merge-if-statements.ts

--568eb8d3--2019-09-03--Nicolas Carlo
40	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--ab7676e6--2019-09-03--Nicolas Carlo
15	4	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--3ed21528--2019-09-03--Nicolas Carlo
57	40	src/refactorings/merge-if-statements/merge-if-statements.ts

--602c51b5--2019-09-03--Nicolas Carlo
53	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
30	2	src/refactorings/merge-if-statements/merge-if-statements.ts

--87d3c2a7--2019-09-03--Nicolas Carlo
20	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
9	6	src/refactorings/merge-if-statements/merge-if-statements.ts

--9d246905--2019-09-03--Nicolas Carlo
16	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
27	20	src/refactorings/merge-if-statements/merge-if-statements.ts

--3e2fea22--2019-09-03--Nicolas Carlo
22	16	src/refactorings/merge-if-statements/merge-if-statements.ts

--1efa2f9a--2019-09-03--Nicolas Carlo
1	1	src/refactorings/merge-if-statements/merge-if-statements.test.ts

--39c753cb--2019-08-31--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/feature_request.md

--4eeb4634--2019-08-31--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/improvement.md

--255367df--2019-08-31--Nicolas Carlo
29	0	.github/ISSUE_TEMPLATE/improvement.md

--e7ff8f99--2019-08-31--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/bug_report.md

--d9c79d84--2019-08-25--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--aad9cab9--2019-08-25--Nicolas Carlo
16	1	.vscode/settings.json

--20c6feac--2019-08-22--Nicolas Carlo
-	-	docs/demo/extract-variable-multiple-occurrences.gif

--abf36ca7--2019-08-22--Nicolas Carlo
--9cf74c86--2019-08-22--Nicolas Carlo
4	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/replace-binary-with-assignment.gif

--dfed63d4--2019-08-22--Nicolas Carlo
4	0	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.test.ts
16	11	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--c5ab24af--2019-08-22--Nicolas Carlo
4	3	src/refactorings/replace-binary-with-assignment/action-provider.ts
24	12	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--3662d366--2019-08-22--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--9d368cef--2019-08-22--Nicolas Carlo
4	0	src/editor/error-reason.ts
40	0	src/refactorings/replace-binary-with-assignment/action-provider.ts
12	0	src/refactorings/replace-binary-with-assignment/command.ts
201	0	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.test.ts
80	0	src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts

--e74b5415--2019-08-21--Nicolas Carlo
1	0	CHANGELOG.md
11	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
2	1	src/refactorings/extract-variable/extract-variable.ts

--02d21f5b--2019-08-21--Nicolas Carlo
4	0	CHANGELOG.md
2	1	src/ast/scope.ts
7	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts

--e88f338f--2019-08-21--Nicolas Carlo
--75b30908--2019-08-21--Nicolas Carlo
18	1	CHANGELOG.md
5	0	README.md
-	-	docs/demo/extract-variable-multiple-occurrences.gif

--53283655--2019-08-21--Nicolas Carlo
30	0	src/ast/domain.ts
24	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--8f194971--2019-08-21--Nicolas Carlo
8	0	src/ast/domain.ts
8	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--5d2eb84f--2019-08-21--Nicolas Carlo
9	0	src/ast/domain.ts
9	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--6805bb12--2019-08-21--Nicolas Carlo
20	0	src/ast/domain.ts
26	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--ac933fff--2019-08-21--Nicolas Carlo
25	6	src/ast/domain.ts
24	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--731a39da--2019-08-21--Nicolas Carlo
26	3	src/ast/domain.ts
42	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
1	1	src/refactorings/extract-variable/extract-variable.ts

--f90f6241--2019-08-20--Nicolas Carlo
3	0	src/ast/domain.ts
16	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts

--855b605c--2019-08-20--Nicolas Carlo
1	5	src/ast/domain.ts
37	1	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
8	3	src/refactorings/extract-variable/extract-variable.ts

--69ebcfe9--2019-08-20--Nicolas Carlo
12	8	src/refactorings/extract-variable/extract-variable.ts

--ea8235ea--2019-08-20--Nicolas Carlo
85	0	src/refactorings/extract-variable/extract-variable.basic-extraction.test.ts
721	0	src/refactorings/extract-variable/extract-variable.extractable.test.ts
131	0	src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts
48	0	src/refactorings/extract-variable/extract-variable.non-extractable.test.ts
0	937	src/refactorings/extract-variable/extract-variable.test.ts

--ed6af202--2019-08-20--Nicolas Carlo
12	0	src/ast/domain.ts
1	6	src/refactorings/extract-variable/extract-variable.ts

--418b602e--2019-08-20--Nicolas Carlo
22	0	src/refactorings/extract-variable/extract-variable.test.ts
39	21	src/refactorings/extract-variable/extract-variable.ts

--952e381e--2019-08-20--Nicolas Carlo
4	0	src/editor/selection.ts
14	1	src/refactorings/extract-variable/extract-variable.test.ts
6	1	src/refactorings/extract-variable/extract-variable.ts

--b66be342--2019-08-20--Nicolas Carlo
21	21	src/refactorings/extract-variable/extract-variable.ts

--b410b685--2019-08-20--Nicolas Carlo
10	8	src/refactorings/extract-variable/extract-variable.ts

--2239fb7f--2019-08-20--Nicolas Carlo
1	20	src/editor/selection.ts
64	41	src/refactorings/extract-variable/extract-variable.ts

--15fcf41f--2019-08-20--Nicolas Carlo
18	16	src/refactorings/extract-variable/extract-variable.ts

--c66faf6d--2019-08-19--Nicolas Carlo
29	0	src/ast/domain.ts
12	31	src/refactorings/extract-variable/extract-variable.ts

--21ffb318--2019-08-19--Nicolas Carlo
15	2	src/refactorings/extract-variable/extract-variable.test.ts
37	25	src/refactorings/extract-variable/extract-variable.ts

--c3f04017--2019-08-19--Nicolas Carlo
14	14	src/refactorings/extract-variable/extract-variable.ts

--aba9d3b9--2019-08-19--Nicolas Carlo
27	1	src/refactorings/extract-variable/extract-variable.test.ts
2	1	src/refactorings/extract-variable/extract-variable.ts

--2abd3aef--2019-08-19--Nicolas Carlo
12	3	src/refactorings/extract-variable/extract-variable.test.ts
18	4	src/refactorings/extract-variable/extract-variable.ts

--c34a8a9f--2019-08-19--Nicolas Carlo
1	1	src/editor/adapters/in-memory-editor.ts
1	2	src/editor/adapters/vscode-editor.ts
1	1	src/editor/editor.ts

--1180deaf--2019-08-19--Nicolas Carlo
29	19	src/refactorings/extract-variable/extract-variable.ts

--4ac5976a--2019-08-19--Nicolas Carlo
6	0	src/editor/selection.ts

--d77b5759--2019-08-19--Nicolas Carlo
20	14	src/refactorings/extract-variable/extract-variable.ts

--94d7804a--2019-08-19--Nicolas Carlo
29	0	src/refactorings/extract-variable/extract-variable.test.ts
33	5	src/refactorings/extract-variable/extract-variable.ts

--d06314d5--2019-08-19--Nicolas Carlo
5	1	src/editor/adapters/in-memory-editor.ts
7	1	src/editor/adapters/vscode-editor.ts
14	1	src/editor/editor.ts

--e02617cb--2019-08-18--Nicolas Carlo
2	2	src/editor/editor.ts

--738ff529--2019-08-18--Nicolas Carlo
5	5	src/editor/editor.ts

--78e7a6b4--2019-08-18--Nicolas Carlo
0	1	CONTRIBUTING.md

--3d13f4aa--2019-08-18--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--e7f3e9bc--2019-08-17--Nicolas Carlo
7	8	src/refactorings/inline-variable-or-function/inline-function.test.ts

--3a282838--2019-08-17--Nicolas Carlo
1	0	CHANGELOG.md
7	3	src/ast/transformation.ts
13	0	src/refactorings/inline-variable-or-function/inline-function.test.ts

--9c9a3301--2019-08-17--Nicolas Carlo
1	0	CHANGELOG.md
15	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
11	0	src/refactorings/inline-variable-or-function/inline-function.ts

--118271d2--2019-08-16--Nicolas Carlo
3	0	README.md

--5677d7e1--2019-08-16--Nicolas Carlo
9	0	.github/ISSUE_TEMPLATE/support.md

--d74ccb0f--2019-08-16--Nicolas Carlo
31	0	.github/ISSUE_TEMPLATE/thank_you.md

--8fba1dcc--2019-08-16--Nicolas Carlo
1	1	CHANGELOG.md

--2ede1560--2019-08-16--Nicolas Carlo
6	8	src/refactorings/inline-variable-or-function/inline-function.ts

--20982189--2019-08-16--Nicolas Carlo
3	1	CHANGELOG.md
9	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
2	1	src/refactorings/inline-variable-or-function/inline-function.ts

--75ff9052--2019-08-16--Nicolas Carlo
14	10	src/editor/error-reason.ts

--55b2402c--2019-08-16--Nicolas Carlo
1	0	CHANGELOG.md
5	1	src/editor/error-reason.ts
10	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
47	11	src/refactorings/inline-variable-or-function/inline-function.ts

--79fa5628--2019-08-15--Nicolas Carlo
5	0	src/ast/domain.ts
5	1	src/ast/scope.ts
31	2	src/ast/transformation.ts
17	60	src/refactorings/inline-variable-or-function/inline-function.ts

--46c7ca6c--2019-08-15--Nicolas Carlo
1	0	CHANGELOG.md
1	1	src/ast/transformation.ts
9	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
56	67	src/refactorings/inline-variable-or-function/inline-function.ts

--ff5d212b--2019-08-15--Nicolas Carlo
42	52	src/refactorings/inline-variable-or-function/inline-function.ts

--2c241312--2019-08-15--Nicolas Carlo
10	15	src/refactorings/inline-variable-or-function/inline-function.ts

--d86d9e46--2019-08-15--Nicolas Carlo
--bfef2ac1--2019-08-15--Nicolas Carlo
45	0	CHANGELOG.md

--3cfb5ba6--2019-08-15--Nicolas Carlo
0	1	src/refactorings/flip-if-else/flip-if-else.test.ts

--c5b8ede4--2019-08-15--Nicolas Carlo
0	5	src/refactorings/flip-if-else/flip-if-else.test.ts

--a3b014d4--2019-08-15--Nicolas Carlo
3	3	src/ast/domain.ts
18	3	src/refactorings/flip-if-else/flip-if-else.test.ts
14	5	src/refactorings/flip-if-else/flip-if-else.ts

--79c88d4b--2019-08-15--Nicolas Carlo
9	0	src/array-helpers.ts
3	2	src/ast/domain.ts
2	1	src/refactorings/inline-variable-or-function/inline-variable.ts
2	1	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--caa9add9--2019-08-15--Nicolas Carlo
17	1	src/ast/domain.ts
61	0	src/refactorings/flip-if-else/flip-if-else.test.ts
13	1	src/refactorings/flip-if-else/flip-if-else.ts

--f8582e46--2019-08-15--Nicolas Carlo
12	0	src/refactorings/flip-if-else/flip-if-else.test.ts
29	6	src/refactorings/flip-if-else/flip-if-else.ts

--73f92c92--2019-08-15--Nicolas Carlo
6	0	src/ast/domain.ts

--4263c822--2019-08-14--Nicolas Carlo
1	0	CHANGELOG.md
4	0	src/editor/selection.ts
4	1	src/refactorings/extract-variable/extract-variable.test.ts
25	17	src/refactorings/extract-variable/extract-variable.ts

--4f09b8d3--2019-08-14--Nicolas Carlo
1	1	src/refactorings/extract-variable/extract-variable.test.ts

--75419c15--2019-08-14--Nicolas Carlo
3	1	src/editor/adapters/in-memory-editor.ts
6	1	src/editor/adapters/vscode-editor.ts
28	0	src/editor/editor-contract-test.ts
2	1	src/editor/editor.ts

--abb1c915--2019-08-14--Nicolas Carlo
18	12	src/refactorings/extract-variable/extract-variable.test.ts

--9abe7ca3--2019-08-14--Nicolas Carlo
4	0	CHANGELOG.md
11	0	src/refactorings/extract-variable/extract-variable.test.ts
19	9	src/refactorings/extract-variable/extract-variable.ts

--934dd906--2019-08-14--Nicolas Carlo
0	1	package.json
0	20	src/editor/adapters/delegate-to-vscode.ts
0	10	src/editor/adapters/show-error-message-in-vscode.ts
0	102	src/editor/adapters/write-code-in-memory.ts
0	95	src/editor/adapters/write-code-in-vscode.ts
0	7	src/editor/i-delegate-to-editor.ts
0	117	src/editor/i-show-error-message.ts
0	295	src/editor/i-write-code-contract-test.ts
0	19	src/editor/i-write-code.ts
0	4	yarn.lock

--033f6bbf--2019-08-14--Nicolas Carlo
9	0	src/editor/adapters/in-memory-editor.test.ts
0	9	src/editor/adapters/write-code-in-memory.test.ts

--0ad9bb8c--2019-08-14--Nicolas Carlo
1	1	_templates/refactoring/new/action-provider.ejs.t
1	1	src/ast/transformation.ts
4	2	src/commands.ts
1	1	src/refactorings/add-braces-to-arrow-function/action-provider.ts
1	1	src/refactorings/convert-if-else-to-ternary/action-provider.ts
1	1	src/refactorings/convert-ternary-to-if-else/action-provider.ts
1	1	src/refactorings/convert-to-template-literal/action-provider.ts
1	1	src/refactorings/flip-if-else/action-provider.ts
1	1	src/refactorings/flip-ternary/action-provider.ts
1	1	src/refactorings/merge-if-statements/action-provider.ts
1	1	src/refactorings/negate-expression/action-provider.ts
1	1	src/refactorings/remove-braces-from-arrow-function/action-provider.ts
1	1	src/refactorings/remove-redundant-else/action-provider.ts
1	1	src/refactorings/split-declaration-and-initialization/action-provider.ts
1	1	src/refactorings/split-if-statement/action-provider.ts

--8be2e484--2019-08-14--Nicolas Carlo
3	36	src/commands.ts
2	2	src/refactorings/add-braces-to-arrow-function/command.ts
2	2	src/refactorings/convert-if-else-to-ternary/command.ts
2	2	src/refactorings/convert-ternary-to-if-else/command.ts
2	2	src/refactorings/convert-to-template-literal/command.ts
2	2	src/refactorings/extract-variable/command.ts
2	2	src/refactorings/flip-if-else/command.ts
2	2	src/refactorings/flip-ternary/command.ts
2	2	src/refactorings/merge-if-statements/command.ts
2	2	src/refactorings/move-statement-down/command.ts
2	2	src/refactorings/move-statement-up/command.ts
2	2	src/refactorings/negate-expression/command.ts
2	2	src/refactorings/remove-braces-from-arrow-function/command.ts
2	2	src/refactorings/remove-redundant-else/command.ts
2	2	src/refactorings/split-declaration-and-initialization/command.ts
2	2	src/refactorings/split-if-statement/command.ts

--8260510b--2019-08-14--Nicolas Carlo
2	2	src/refactorings/split-if-statement/command.ts
9	11	src/refactorings/split-if-statement/split-if-statement.test.ts
4	9	src/refactorings/split-if-statement/split-if-statement.ts

--f9f9b5a0--2019-08-14--Nicolas Carlo
2	2	src/refactorings/split-declaration-and-initialization/command.ts
9	16	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
4	9	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--49c28394--2019-08-14--Nicolas Carlo
2	2	src/refactorings/remove-redundant-else/command.ts
9	11	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
4	9	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--4929c74e--2019-08-14--Nicolas Carlo
2	2	src/refactorings/remove-braces-from-arrow-function/command.ts
9	16	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts
5	10	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--1a75866d--2019-08-14--Nicolas Carlo
2	26	src/refactorings/negate-expression/command.ts
9	11	src/refactorings/negate-expression/negate-expression.test.ts
4	9	src/refactorings/negate-expression/negate-expression.ts

--881094cf--2019-08-14--Nicolas Carlo
2	2	src/refactorings/move-statement-up/command.ts
9	11	src/refactorings/move-statement-up/move-statement-up.test.ts
5	10	src/refactorings/move-statement-up/move-statement-up.ts

--b598d486--2019-08-14--Nicolas Carlo
2	2	src/refactorings/move-statement-down/command.ts
9	11	src/refactorings/move-statement-down/move-statement-down.test.ts
5	10	src/refactorings/move-statement-down/move-statement-down.ts

--6a2e1221--2019-08-14--Nicolas Carlo
2	2	src/refactorings/merge-if-statements/command.ts
9	11	src/refactorings/merge-if-statements/merge-if-statements.test.ts
4	9	src/refactorings/merge-if-statements/merge-if-statements.ts

--83d30cc5--2019-08-14--Nicolas Carlo
20	37	src/refactorings/inline-variable-or-function/command.ts
9	11	src/refactorings/inline-variable-or-function/inline-function.test.ts
8	12	src/refactorings/inline-variable-or-function/inline-function.ts
9	11	src/refactorings/inline-variable-or-function/inline-variable.test.ts
8	12	src/refactorings/inline-variable-or-function/inline-variable.ts

--73d08879--2019-08-14--Nicolas Carlo
1	1	_templates/refactoring/new/error-reason-enum.ejs.t
1	1	_templates/refactoring/new/error-reason-switch.ejs.t
4	9	_templates/refactoring/new/refactoring.ejs.t

--dbf61d4b--2019-08-14--Nicolas Carlo
2	2	src/refactorings/flip-ternary/command.ts
9	11	src/refactorings/flip-ternary/flip-ternary.test.ts
5	13	src/refactorings/flip-ternary/flip-ternary.ts

--2f858f9c--2019-08-14--Nicolas Carlo
2	2	src/refactorings/flip-if-else/command.ts
9	11	src/refactorings/flip-if-else/flip-if-else.test.ts
5	13	src/refactorings/flip-if-else/flip-if-else.ts

--e84af03a--2019-08-14--Nicolas Carlo
2	28	src/refactorings/extract-variable/command.ts
12	23	src/refactorings/extract-variable/extract-variable.test.ts
6	12	src/refactorings/extract-variable/extract-variable.ts
8	4	src/refactorings/rename-symbol/command.ts
7	7	src/refactorings/rename-symbol/rename-symbol.test.ts
3	6	src/refactorings/rename-symbol/rename-symbol.ts

--82080061--2019-08-14--Nicolas Carlo
2	2	src/refactorings/convert-to-template-literal/command.ts
8	10	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
4	9	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--ad9599e8--2019-08-14--Nicolas Carlo
2	2	src/refactorings/convert-ternary-to-if-else/command.ts
9	11	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
4	9	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--b45cb717--2019-08-14--Nicolas Carlo
2	2	src/refactorings/convert-if-else-to-ternary/command.ts
9	11	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
4	9	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--2134cdfc--2019-08-14--Nicolas Carlo
30	3	src/commands.ts
9	11	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts
4	9	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
2	2	src/refactorings/add-braces-to-arrow-function/command.ts

--93579f17--2019-08-14--Nicolas Carlo
8	10	_templates/refactoring/new/test.ejs.t

--28eef663--2019-08-14--Nicolas Carlo
122	0	src/editor/adapters/in-memory-editor.ts
110	0	src/editor/adapters/vscode-editor.ts
7	10	src/editor/adapters/write-code-in-memory.test.ts
301	0	src/editor/editor-contract-test.ts
27	0	src/editor/editor.ts
115	0	src/editor/error-reason.ts

--4dddf27c--2019-08-14--Nicolas Carlo
1	0	src/ast/index.ts
19	0	src/ast/scope.ts
3	59	src/editor/selection.ts

--93b0236d--2019-08-14--Nicolas Carlo
0	2	src/ast/domain.ts

--b7ce1c91--2019-08-11--Nicolas Carlo
11	1	CONTRIBUTING.md

--0e6826a6--2019-08-11--Nicolas Carlo
29	0	docs/adr/0006-create-generator-to-bootstrap-new-refactorings.md

--dd87d004--2019-08-11--Nicolas Carlo
1	1	package.json

--7d95e599--2019-08-11--Nicolas Carlo
2	2	src/extension.ts

--5c4c5819--2019-08-11--Nicolas Carlo
6	0	_templates/refactoring/new/error-reason-enum.ejs.t
11	0	_templates/refactoring/new/error-reason-switch.ejs.t
17	0	_templates/refactoring/new/index.js
2	2	_templates/refactoring/new/refactoring.ejs.t
3	2	_templates/refactoring/new/test.ejs.t

--c275c8aa--2019-08-11--Nicolas Carlo
2	2	_templates/refactoring/new/test.ejs.t

--7ddc3057--2019-08-11--Nicolas Carlo
4	2	_templates/refactoring/new/action-provider.ejs.t
30	0	_templates/refactoring/new/index.js
0	9	_templates/refactoring/new/prompt.js
3	2	_templates/refactoring/new/refactoring.ejs.t

--13ea7a28--2019-08-10--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--0ef93a3e--2019-08-10--Nicolas Carlo
--920fc092--2019-08-10--Nicolas Carlo
1	0	CHANGELOG.md
12	0	README.md
-	-	docs/demo/convert-to-template-literal.gif

--2646f7db--2019-08-09--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--b54d7d2d--2019-08-09--Nicolas Carlo
9	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts

--dd641cbd--2019-08-09--Nicolas Carlo
19	8	src/ast/domain.ts
10	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
3	3	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--f1498ddf--2019-08-09--Nicolas Carlo
0	164	src/ast.ts
48	0	src/ast/domain.ts
3	0	src/ast/index.ts
47	0	src/ast/selection.ts
70	0	src/ast/transformation.ts

--31db5709--2019-08-09--Nicolas Carlo
35	19	src/ast.ts
19	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
55	15	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--e46ce400--2019-08-09--Nicolas Carlo
10	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
6	2	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--575ead00--2019-08-09--Nicolas Carlo
9	9	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--0dfc3092--2019-08-09--Nicolas Carlo
5	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
20	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--1e40bf0c--2019-08-09--Nicolas Carlo
53	41	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--ebf71507--2019-08-08--Nicolas Carlo
2	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--28e0c92a--2019-08-08--Nicolas Carlo
8	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
57	12	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--90347390--2019-08-08--Nicolas Carlo
3	11	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--1234cd75--2019-08-08--Nicolas Carlo
8	9	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--e8ff80c7--2019-08-08--Nicolas Carlo
3	3	src/ast.ts
37	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
38	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--dad2f846--2019-08-08--Nicolas Carlo
2	1	src/tests-helpers.ts

--6dec63ee--2019-08-08--Nicolas Carlo
12	0	src/ast.ts
14	3	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
7	1	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--eee56a84--2019-08-08--Nicolas Carlo
2	1	.vscode/settings.json

--d0050106--2019-08-08--Nicolas Carlo
4	0	src/editor/i-show-error-message.ts
40	0	src/refactorings/convert-to-template-literal/action-provider.ts
12	0	src/refactorings/convert-to-template-literal/command.ts
49	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts
38	0	src/refactorings/convert-to-template-literal/convert-to-template-literal.ts

--4770846c--2019-08-08--Nicolas Carlo
0	3	_templates/refactoring/new/action-provider.ejs.t
0	3	_templates/refactoring/new/command.ejs.t
0	3	_templates/refactoring/new/refactoring.ejs.t

--1d9d2455--2019-08-08--Nicolas Carlo
48	0	_templates/refactoring/new/action-provider.ejs.t
22	0	_templates/refactoring/new/command.ejs.t
9	0	_templates/refactoring/new/prompt.js
51	0	_templates/refactoring/new/refactoring.ejs.t
58	0	_templates/refactoring/new/test.ejs.t
1	0	package.json

--17eedc42--2019-08-08--Nicolas Carlo
5	0	_templates/generator/help/index.ejs.t
18	0	_templates/generator/new/hello.ejs.t
18	0	_templates/generator/with-prompt/hello.ejs.t
14	0	_templates/generator/with-prompt/prompt.ejs.t
2	0	package.json
221	2	yarn.lock

--68c1fb61--2019-08-08--Nicolas Carlo
-	-	docs/adr/0004-refactoring-after-recast.gif
-	-	docs/adr/0004-refactoring-before-recast.gif
2	2	docs/adr/0004-use-recast-for-ast-manipulation.md
-	-	docs/adr/assets/0004-refactoring-after-recast.gif
-	-	docs/adr/assets/0004-refactoring-before-recast.gif

--b089b7dc--2019-08-07--Nicolas Carlo
--61f0d491--2019-08-07--Nicolas Carlo
4	0	CHANGELOG.md
13	0	README.md
-	-	docs/demo/split-declaration-and-initialization.gif

--8c5f524e--2019-08-07--Nicolas Carlo
6	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
2	1	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--90bf3c3b--2019-08-07--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--5354af72--2019-08-07--Nicolas Carlo
1	1	src/refactorings/split-declaration-and-initialization/action-provider.ts

--4d590ed3--2019-08-07--Nicolas Carlo
18	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
15	7	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--34b23b75--2019-08-07--Nicolas Carlo
7	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
19	9	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--7465ba7b--2019-08-07--Nicolas Carlo
4	0	src/editor/i-show-error-message.ts
41	0	src/refactorings/split-declaration-and-initialization/action-provider.ts
12	0	src/refactorings/split-declaration-and-initialization/command.ts
87	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts
52	0	src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts

--52b9dfdf--2019-08-07--Nicolas Carlo
1	0	CHANGELOG.md
16	1	src/refactorings/extract-variable/extract-variable.test.ts
1	1	src/refactorings/extract-variable/extract-variable.ts

--4b7300b6--2019-08-07--Nicolas Carlo
1	0	CHANGELOG.md
11	0	src/refactorings/extract-variable/extract-variable.test.ts
1	1	src/refactorings/extract-variable/extract-variable.ts

--0f8e5f25--2019-08-06--Nicolas Carlo
1	0	CHANGELOG.md
82	0	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
30	0	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--1db03073--2019-08-06--Nicolas Carlo
1	0	CHANGELOG.md
20	0	src/refactorings/flip-ternary/flip-ternary.test.ts
24	1	src/refactorings/flip-ternary/flip-ternary.ts

--01bace4e--2019-08-06--Nicolas Carlo
4	0	CHANGELOG.md
45	4	src/refactorings/flip-if-else/flip-if-else.test.ts
24	1	src/refactorings/flip-if-else/flip-if-else.ts

--fe6d3857--2019-08-06--Nicolas Carlo
37	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts

--c0bae19c--2019-08-05--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--582bce8d--2019-08-05--Nicolas Carlo
0	22	src/refactorings/inline-variable-or-function/inline-function.ts

--ec9b4b68--2019-08-05--Nicolas Carlo
10	0	src/editor/selection.ts
2	4	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts
1	2	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts
2	7	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts
2	7	src/refactorings/extract-variable/extract-variable.ts
1	2	src/refactorings/flip-if-else/flip-if-else.ts
1	2	src/refactorings/flip-ternary/flip-ternary.ts
1	2	src/refactorings/inline-variable-or-function/inline-function.ts
3	4	src/refactorings/inline-variable-or-function/inline-variable.ts
1	2	src/refactorings/merge-if-statements/merge-if-statements.ts
1	2	src/refactorings/negate-expression/negate-expression.ts
2	4	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts
1	2	src/refactorings/remove-redundant-else/remove-redundant-else.ts
3	6	src/refactorings/split-if-statement/split-if-statement.ts

--ba0e25ba--2019-08-05--Nicolas Carlo
64	46	README.md

--505ca2c9--2019-08-05--Nicolas Carlo
--813febc1--2019-08-05--Nicolas Carlo
1	0	CHANGELOG.md
12	0	README.md
-	-	docs/demo/inline-function.gif

--fdf0ca27--2019-08-05--Nicolas Carlo
86	119	src/refactorings/inline-variable-or-function/inline-function.ts

--591289e9--2019-08-05--Nicolas Carlo
55	81	src/refactorings/inline-variable-or-function/inline-function.test.ts

--5971a86b--2019-08-05--Nicolas Carlo
36	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
57	0	src/refactorings/inline-variable-or-function/inline-function.ts

--27e5a9bd--2019-08-04--Nicolas Carlo
9	1	src/editor/i-show-error-message.ts
89	1	src/refactorings/inline-variable-or-function/inline-function.test.ts
125	4	src/refactorings/inline-variable-or-function/inline-function.ts

--dcf1c0e0--2019-08-04--Nicolas Carlo
8	8	package.json
2	2	src/extension.ts
0	12	src/refactorings/inline-function/command.ts
0	271	src/refactorings/inline-function/find-param-matching-id.ts
0	420	src/refactorings/inline-function/inline-function.test.ts
0	227	src/refactorings/inline-function/inline-function.ts
74	0	src/refactorings/inline-variable-or-function/command.ts
37	0	src/refactorings/inline-variable-or-function/find-exported-id-names.ts
271	0	src/refactorings/inline-variable-or-function/find-param-matching-id.ts
420	0	src/refactorings/inline-variable-or-function/inline-function.test.ts
227	0	src/refactorings/inline-variable-or-function/inline-function.ts
358	0	src/refactorings/inline-variable-or-function/inline-variable.test.ts
266	0	src/refactorings/inline-variable-or-function/inline-variable.ts
0	36	src/refactorings/inline-variable/command.ts
0	37	src/refactorings/inline-variable/find-exported-id-names.ts
0	358	src/refactorings/inline-variable/inline-variable.test.ts
0	266	src/refactorings/inline-variable/inline-variable.ts

--0d41a70d--2019-08-04--Nicolas Carlo
17	3	src/refactorings/inline-function/inline-function.test.ts
16	2	src/refactorings/inline-function/inline-function.ts

--909ff784--2019-08-03--Nicolas Carlo
5	1	src/editor/i-show-error-message.ts
17	7	src/refactorings/inline-function/inline-function.test.ts
18	5	src/refactorings/inline-function/inline-function.ts

--19ac5940--2019-08-03--Nicolas Carlo
22	0	src/refactorings/inline-function/inline-function.test.ts
32	17	src/refactorings/inline-function/inline-function.ts
37	0	src/refactorings/inline-variable/find-exported-id-names.ts
1	34	src/refactorings/inline-variable/inline-variable.ts

--7a50c52f--2019-08-03--Nicolas Carlo
21	1	src/refactorings/inline-function/inline-function.test.ts
27	9	src/refactorings/inline-function/inline-function.ts

--a304a8cf--2019-08-03--Nicolas Carlo
25	0	src/refactorings/inline-function/inline-function.test.ts
61	28	src/refactorings/inline-function/inline-function.ts

--fb0aa5d1--2019-08-03--Nicolas Carlo
11	0	src/refactorings/inline-function/inline-function.test.ts
13	0	src/refactorings/inline-function/inline-function.ts

--160dd69a--2019-08-03--Nicolas Carlo
11	0	src/refactorings/inline-function/inline-function.test.ts

--364321b3--2019-08-03--Nicolas Carlo
26	0	src/refactorings/inline-function/find-param-matching-id.ts
18	0	src/refactorings/inline-function/inline-function.test.ts

--72770d58--2019-08-03--Nicolas Carlo
9	0	src/refactorings/inline-function/inline-function.test.ts

--4aa21fdc--2019-08-03--Nicolas Carlo
10	0	src/refactorings/inline-function/find-param-matching-id.ts
14	0	src/refactorings/inline-function/inline-function.test.ts

--6ddd79bb--2019-08-03--Nicolas Carlo
9	0	src/refactorings/inline-function/inline-function.test.ts

--e96cb979--2019-08-03--Nicolas Carlo
7	0	src/ast.ts
33	6	src/refactorings/inline-function/find-param-matching-id.ts
11	0	src/refactorings/inline-function/inline-function.test.ts

--1c121a08--2019-08-03--Nicolas Carlo
29	3	src/refactorings/inline-function/find-param-matching-id.ts
9	0	src/refactorings/inline-function/inline-function.test.ts

--32d0537a--2019-08-03--Nicolas Carlo
7	0	src/ast.ts
29	0	src/refactorings/inline-function/find-param-matching-id.ts
9	0	src/refactorings/inline-function/inline-function.test.ts

--6933c0ed--2019-08-03--Nicolas Carlo
10	10	src/refactorings/inline-function/find-param-matching-id.ts

--cd24cef9--2019-08-03--Nicolas Carlo
11	4	src/refactorings/inline-function/find-param-matching-id.ts

--021b8748--2019-08-03--Nicolas Carlo
146	0	src/refactorings/inline-function/find-param-matching-id.ts
1	107	src/refactorings/inline-function/inline-function.ts

--477b88da--2019-08-02--Nicolas Carlo
3	3	src/refactorings/inline-function/inline-function.ts

--09d21046--2019-07-31--Nicolas Carlo
19	1	src/refactorings/inline-function/inline-function.test.ts
41	1	src/refactorings/inline-function/inline-function.ts

--2e84cc21--2019-07-31--Nicolas Carlo
9	0	src/refactorings/inline-function/inline-function.test.ts
24	0	src/refactorings/inline-function/inline-function.ts

--8ccfbf9a--2019-07-31--Nicolas Carlo
49	5	src/refactorings/inline-function/inline-function.ts

--092e6668--2019-07-31--Nicolas Carlo
9	0	src/refactorings/inline-function/inline-function.test.ts
2	1	src/refactorings/inline-function/inline-function.ts

--3225e6da--2019-07-30--Nicolas Carlo
23	0	src/refactorings/inline-function/inline-function.test.ts
42	2	src/refactorings/inline-function/inline-function.ts

--0f17e1d5--2019-07-28--Nicolas Carlo
22	0	src/refactorings/inline-function/inline-function.test.ts
2	1	src/refactorings/inline-function/inline-function.ts

--eec167bb--2019-07-28--Nicolas Carlo
32	0	src/refactorings/inline-function/inline-function.test.ts
3	2	src/refactorings/inline-function/inline-function.ts

--d8b94b30--2019-07-27--Nicolas Carlo
18	0	src/refactorings/inline-function/inline-function.test.ts
24	0	src/refactorings/inline-function/inline-function.ts

--6fe8acd8--2019-07-25--Nicolas Carlo
12	0	src/refactorings/inline-function/command.ts
97	0	src/refactorings/inline-function/inline-function.test.ts
52	0	src/refactorings/inline-function/inline-function.ts

--fc8c5366--2019-07-24--Nicolas Carlo
--185320e8--2019-07-24--Nicolas Carlo
4	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/merge-if-statements.gif

--76916ece--2019-07-24--Nicolas Carlo
22	0	package.json
4	0	src/extension.ts

--ba426040--2019-07-24--Nicolas Carlo
4	0	src/editor/i-show-error-message.ts
35	0	src/refactorings/merge-if-statements/action-provider.ts
12	0	src/refactorings/merge-if-statements/command.ts
180	0	src/refactorings/merge-if-statements/merge-if-statements.test.ts
95	0	src/refactorings/merge-if-statements/merge-if-statements.ts

--28273cf7--2019-07-23--Nicolas Carlo
4	1	CHANGELOG.md
1	1	package.json

--335d7861--2019-07-23--Nicolas Carlo
--9cfd9fc5--2019-07-23--Nicolas Carlo
1	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/split-if-statement.gif

--d967fac8--2019-07-23--Nicolas Carlo
22	0	package.json
6	2	src/extension.ts

--6c74391b--2019-07-23--Nicolas Carlo
62	0	src/refactorings/split-if-statement/split-if-statement.test.ts
18	3	src/refactorings/split-if-statement/split-if-statement.ts

--97785cf4--2019-07-23--Nicolas Carlo
42	0	src/refactorings/split-if-statement/split-if-statement.test.ts
2	1	src/refactorings/split-if-statement/split-if-statement.ts

--c2c2d92a--2019-07-23--Nicolas Carlo
4	4	src/refactorings/split-if-statement/split-if-statement.test.ts

--32bd1129--2019-07-23--Nicolas Carlo
34	4	src/refactorings/split-if-statement/split-if-statement.test.ts
24	0	src/refactorings/split-if-statement/split-if-statement.ts

--863908dc--2019-07-23--Nicolas Carlo
11	0	src/refactorings/split-if-statement/split-if-statement.test.ts

--e51132dd--2019-07-23--Nicolas Carlo
11	0	src/refactorings/split-if-statement/split-if-statement.test.ts

--9c080ffc--2019-07-23--Nicolas Carlo
11	0	src/refactorings/split-if-statement/split-if-statement.test.ts
10	3	src/refactorings/split-if-statement/split-if-statement.ts

--4f1d7104--2019-07-23--Nicolas Carlo
4	0	src/editor/i-show-error-message.ts
35	0	src/refactorings/split-if-statement/action-provider.ts
12	0	src/refactorings/split-if-statement/command.ts
72	0	src/refactorings/split-if-statement/split-if-statement.test.ts
48	0	src/refactorings/split-if-statement/split-if-statement.ts

--8ada0159--2019-07-23--Nicolas Carlo
1	1	README.md

--3c26ab91--2019-07-23--Nicolas Carlo
33	2	README.md

--aa0e1f3c--2019-07-22--Nicolas Carlo
5	0	README.md
-	-	docs/demo/move-statement-object-property.gif

--3d87e299--2019-07-22--Nicolas Carlo
1	1	CHANGELOG.md
49	0	src/refactorings/move-statement-down/move-statement-down.test.ts
62	54	src/refactorings/move-statement-down/move-statement-down.ts

--effa77ff--2019-07-22--Nicolas Carlo
1	0	CHANGELOG.md
6	0	src/editor/position.ts
4	0	src/editor/selection.ts
49	0	src/refactorings/move-statement-up/move-statement-up.test.ts
51	43	src/refactorings/move-statement-up/move-statement-up.ts

--17166322--2019-07-22--Nicolas Carlo
8	10	src/refactorings/inline-variable/inline-variable.ts

--59e5d735--2019-07-22--Nicolas Carlo
6	0	CHANGELOG.md
11	0	src/refactorings/inline-variable/inline-variable.test.ts
14	3	src/refactorings/inline-variable/inline-variable.ts

--493db39d--2019-07-21--Nicolas Carlo
89	89	package.json
3	3	src/extension.ts

--bb12df3f--2019-07-21--Nicolas Carlo
--44e06f5f--2019-07-21--Nicolas Carlo
1	0	CHANGELOG.md

--19766a63--2019-07-21--Nicolas Carlo
10	0	README.md
-	-	docs/demo/remove-braces-from-arrow-function.gif

--5b1ca7b9--2019-07-21--Nicolas Carlo
22	0	package.json
5	1	src/extension.ts
41	0	src/refactorings/remove-braces-from-arrow-function/action-provider.ts
12	0	src/refactorings/remove-braces-from-arrow-function/command.ts

--eebfc282--2019-07-21--Nicolas Carlo
9	1	src/editor/i-show-error-message.ts
152	0	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts
108	0	src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts

--80548cae--2019-07-21--Nicolas Carlo
294	173	src/refactorings/extract-variable/extract-variable.test.ts

--77d37a87--2019-07-21--Nicolas Carlo
6	8	src/editor/adapters/write-code-in-memory.ts
18	0	src/editor/i-write-code-contract-test.ts
9	9	src/refactorings/inline-variable/inline-variable.test.ts

--581e3299--2019-07-20--Nicolas Carlo
17	5	src/editor/adapters/write-code-in-memory.ts
27	15	src/editor/i-write-code-contract-test.ts

--348c213b--2019-07-20--Nicolas Carlo
45	78	src/refactorings/negate-expression/negate-expression.test.ts

--fa1dcc45--2019-07-20--Nicolas Carlo
133	250	src/refactorings/inline-variable/inline-variable.test.ts

--6745c77f--2019-07-20--Nicolas Carlo
9	2	src/editor/adapters/write-code-in-memory.test.ts
76	2	src/editor/adapters/write-code-in-memory.ts
192	2	src/editor/i-write-code-contract-test.ts

--0b943d56--2019-07-18--Nicolas Carlo
--e7207b88--2019-07-18--Nicolas Carlo
1	0	README.md

--fd6babce--2019-07-18--Nicolas Carlo
9	0	.travis.yml

--506230a7--2019-07-18--Nicolas Carlo
--1cd97343--2019-07-18--Nicolas Carlo
2	0	CHANGELOG.md
12	0	README.md
-	-	docs/demo/add-braces-to-arrow-function.gif

--99cefddc--2019-07-18--Nicolas Carlo
3	1	src/extension.ts
40	0	src/refactorings/add-braces-to-arrow-function/action-provider.ts
8	1	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts

--54f8e02b--2019-07-18--Nicolas Carlo
22	0	package.json
2	0	src/extension.ts
12	0	src/refactorings/add-braces-to-arrow-function/command.ts

--0305295d--2019-07-18--Nicolas Carlo
4	0	src/editor/i-show-error-message.ts
87	0	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts
65	0	src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts

--0668d6ab--2019-07-16--Nicolas Carlo
0	2	README.md

--7a801608--2019-07-16--Nicolas Carlo
10	1	src/commands.ts
0	12	src/refactorings/refactoring.ts

--242d6783--2019-07-16--Nicolas Carlo
3	18	src/commands.ts
3	5	src/extension.ts
0	6	src/refactoring-command.ts
1	1	src/refactorings/extract-variable/extract-variable.ts
0	15	src/refactorings/rename-symbol.test.ts
0	11	src/refactorings/rename-symbol.ts
15	0	src/refactorings/rename-symbol/command.ts
15	0	src/refactorings/rename-symbol/rename-symbol.test.ts
11	0	src/refactorings/rename-symbol/rename-symbol.ts

--fa07f4a6--2019-07-16--Nicolas Carlo
1	40	src/action-providers.ts
0	5	src/commands.ts
6	5	src/extension.ts
1	2	src/refactoring-command.ts
0	179	src/refactorings/remove-redundant-else.test.ts
0	62	src/refactorings/remove-redundant-else.ts
35	0	src/refactorings/remove-redundant-else/action-provider.ts
12	0	src/refactorings/remove-redundant-else/command.ts
182	0	src/refactorings/remove-redundant-else/remove-redundant-else.test.ts
65	0	src/refactorings/remove-redundant-else/remove-redundant-else.ts

--9b715c1b--2019-07-16--Nicolas Carlo
0	31	src/action-providers.ts
0	24	src/commands.ts
6	2	src/extension.ts
0	1	src/refactoring-command.ts
1	1	src/refactorings/flip-if-else/flip-if-else.ts
1	1	src/refactorings/flip-ternary/flip-ternary.ts
0	274	src/refactorings/negate-expression.test.ts
0	192	src/refactorings/negate-expression.ts
41	0	src/refactorings/negate-expression/action-provider.ts
36	0	src/refactorings/negate-expression/command.ts
277	0	src/refactorings/negate-expression/negate-expression.test.ts
195	0	src/refactorings/negate-expression/negate-expression.ts

--f42068ff--2019-07-16--Nicolas Carlo
0	5	src/commands.ts
3	1	src/extension.ts
1	2	src/refactoring-command.ts
0	187	src/refactorings/move-statement-up.test.ts
0	117	src/refactorings/move-statement-up.ts
12	0	src/refactorings/move-statement-up/command.ts
190	0	src/refactorings/move-statement-up/move-statement-up.test.ts
120	0	src/refactorings/move-statement-up/move-statement-up.ts

--565b3775--2019-07-16--Nicolas Carlo
0	5	src/commands.ts
3	1	src/extension.ts
1	2	src/refactoring-command.ts
0	234	src/refactorings/move-statement-down.test.ts
0	147	src/refactorings/move-statement-down.ts
12	0	src/refactorings/move-statement-down/command.ts
237	0	src/refactorings/move-statement-down/move-statement-down.test.ts
150	0	src/refactorings/move-statement-down/move-statement-down.ts

--b346c8fa--2019-07-16--Nicolas Carlo
0	23	src/commands.ts
3	1	src/extension.ts
0	1	src/refactoring-command.ts
0	461	src/refactorings/inline-variable.test.ts
0	287	src/refactorings/inline-variable.ts
36	0	src/refactorings/inline-variable/command.ts
464	0	src/refactorings/inline-variable/inline-variable.test.ts
290	0	src/refactorings/inline-variable/inline-variable.ts

--cef82142--2019-07-16--Nicolas Carlo
1	26	src/action-providers.ts
0	5	src/commands.ts
6	2	src/extension.ts
0	1	src/refactoring-command.ts
0	63	src/refactorings/flip-ternary.test.ts
0	62	src/refactorings/flip-ternary.ts
35	0	src/refactorings/flip-ternary/action-provider.ts
12	0	src/refactorings/flip-ternary/command.ts
66	0	src/refactorings/flip-ternary/flip-ternary.test.ts
65	0	src/refactorings/flip-ternary/flip-ternary.ts

--827adf61--2019-07-16--Nicolas Carlo
0	25	src/action-providers.ts
0	5	src/commands.ts
6	2	src/extension.ts
0	1	src/refactoring-command.ts
0	115	src/refactorings/flip-if-else.test.ts
0	70	src/refactorings/flip-if-else.ts
35	0	src/refactorings/flip-if-else/action-provider.ts
12	0	src/refactorings/flip-if-else/command.ts
118	0	src/refactorings/flip-if-else/flip-if-else.test.ts
73	0	src/refactorings/flip-if-else/flip-if-else.ts

--03d47f86--2019-07-16--Nicolas Carlo
3	25	src/commands.ts
6	2	src/extension.ts
0	1	src/refactoring-command.ts
0	666	src/refactorings/extract-variable.test.ts
0	151	src/refactorings/extract-variable.ts
38	0	src/refactorings/extract-variable/command.ts
669	0	src/refactorings/extract-variable/extract-variable.test.ts
154	0	src/refactorings/extract-variable/extract-variable.ts

--f0daf335--2019-07-16--Nicolas Carlo
1	31	src/action-providers.ts
0	5	src/commands.ts
9	4	src/extension.ts
0	1	src/refactoring-command.ts
0	245	src/refactorings/convert-ternary-to-if-else.test.ts
0	129	src/refactorings/convert-ternary-to-if-else.ts
40	0	src/refactorings/convert-ternary-to-if-else/action-provider.ts
12	0	src/refactorings/convert-ternary-to-if-else/command.ts
248	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts
132	0	src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts

--1c2ef221--2019-07-16--Nicolas Carlo
20	45	src/action-providers.ts
1	6	src/commands.ts
11	4	src/extension.ts
0	1	src/refactoring-command.ts
0	214	src/refactorings/convert-if-else-to-ternary.test.ts
0	130	src/refactorings/convert-if-else-to-ternary.ts
40	0	src/refactorings/convert-if-else-to-ternary/action-provider.ts
12	0	src/refactorings/convert-if-else-to-ternary/command.ts
217	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts
133	0	src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts

--79fab8f1--2019-07-16--Nicolas Carlo
1	0	package.json

--8d924c9b--2019-07-16--Nicolas Carlo
122	0	src/ast.ts
1	1	src/editor/position.ts
2	2	src/editor/selection.ts
0	122	src/refactorings/ast.ts
1	1	src/refactorings/convert-if-else-to-ternary.ts
1	1	src/refactorings/convert-ternary-to-if-else.ts
1	1	src/refactorings/extract-variable.ts
1	1	src/refactorings/flip-if-else.ts
1	1	src/refactorings/flip-ternary.ts
1	1	src/refactorings/inline-variable.ts
1	1	src/refactorings/move-statement-down.ts
1	1	src/refactorings/move-statement-up.ts
1	1	src/refactorings/negate-expression.ts
1	1	src/refactorings/remove-redundant-else.ts

--2ae69dbb--2019-07-16--Nicolas Carlo
1	1	src/action-providers.ts
3	3	src/commands.ts
20	0	src/editor/adapters/delegate-to-vscode.ts
10	0	src/editor/adapters/show-error-message-in-vscode.ts
5	0	src/editor/adapters/write-code-in-memory.test.ts
18	0	src/editor/adapters/write-code-in-memory.ts
95	0	src/editor/adapters/write-code-in-vscode.ts
7	0	src/editor/i-delegate-to-editor.ts
77	0	src/editor/i-show-error-message.ts
75	0	src/editor/i-write-code-contract-test.ts
19	0	src/editor/i-write-code.ts
105	0	src/editor/position.test.ts
59	0	src/editor/position.ts
75	0	src/editor/selection.test.ts
149	0	src/editor/selection.ts
1	1	src/refactorings/ast.ts
4	4	src/refactorings/convert-if-else-to-ternary.test.ts
3	3	src/refactorings/convert-if-else-to-ternary.ts
4	4	src/refactorings/convert-ternary-to-if-else.test.ts
3	3	src/refactorings/convert-ternary-to-if-else.ts
0	20	src/refactorings/editor/adapters/delegate-to-vscode.ts
0	10	src/refactorings/editor/adapters/show-error-message-in-vscode.ts
0	5	src/refactorings/editor/adapters/write-code-in-memory.test.ts
0	18	src/refactorings/editor/adapters/write-code-in-memory.ts
0	95	src/refactorings/editor/adapters/write-code-in-vscode.ts
0	7	src/refactorings/editor/i-delegate-to-editor.ts
0	77	src/refactorings/editor/i-show-error-message.ts
0	75	src/refactorings/editor/i-write-code-contract-test.ts
0	19	src/refactorings/editor/i-write-code.ts
0	105	src/refactorings/editor/position.test.ts
0	59	src/refactorings/editor/position.ts
0	75	src/refactorings/editor/selection.test.ts
0	149	src/refactorings/editor/selection.ts
7	4	src/refactorings/extract-variable.test.ts
4	4	src/refactorings/extract-variable.ts
4	4	src/refactorings/flip-if-else.test.ts
3	3	src/refactorings/flip-if-else.ts
4	4	src/refactorings/flip-ternary.test.ts
3	3	src/refactorings/flip-ternary.ts
3	3	src/refactorings/inline-variable.test.ts
3	3	src/refactorings/inline-variable.ts
5	5	src/refactorings/move-statement-down.test.ts
4	4	src/refactorings/move-statement-down.ts
5	5	src/refactorings/move-statement-up.test.ts
4	4	src/refactorings/move-statement-up.ts
3	3	src/refactorings/negate-expression.test.ts
3	3	src/refactorings/negate-expression.ts
3	3	src/refactorings/refactoring.ts
4	4	src/refactorings/remove-redundant-else.test.ts
3	3	src/refactorings/remove-redundant-else.ts
4	1	src/refactorings/rename-symbol.test.ts
4	1	src/refactorings/rename-symbol.ts

--064f3395--2019-07-16--Nicolas Carlo
1	1	src/action-providers.ts
3	3	src/commands.ts
0	23	src/refactorings/adapters/delegate-to-vscode.ts
0	10	src/refactorings/adapters/show-error-message-in-vscode.ts
0	5	src/refactorings/adapters/write-code-in-memory.test.ts
0	18	src/refactorings/adapters/write-code-in-memory.ts
0	95	src/refactorings/adapters/write-code-in-vscode.ts
1	1	src/refactorings/convert-if-else-to-ternary.test.ts
1	1	src/refactorings/convert-ternary-to-if-else.test.ts
20	0	src/refactorings/editor/adapters/delegate-to-vscode.ts
10	0	src/refactorings/editor/adapters/show-error-message-in-vscode.ts
5	0	src/refactorings/editor/adapters/write-code-in-memory.test.ts
18	0	src/refactorings/editor/adapters/write-code-in-memory.ts
95	0	src/refactorings/editor/adapters/write-code-in-vscode.ts
1	1	src/refactorings/flip-if-else.test.ts
1	1	src/refactorings/flip-ternary.test.ts
1	1	src/refactorings/move-statement-down.test.ts
1	1	src/refactorings/move-statement-up.test.ts
1	1	src/refactorings/remove-redundant-else.test.ts

--0005f771--2019-07-16--Nicolas Carlo
0	3	src/tests-helpers.ts

--677446ef--2019-07-14--Nicolas Carlo
--c20a6757--2019-07-14--Nicolas Carlo
30	0	docs/adr/0005-use-custom-testeach-instead-of-jest-it-each.md

--19803052--2019-07-14--Nicolas Carlo
32	48	src/refactorings/convert-if-else-to-ternary.test.ts
32	50	src/refactorings/convert-ternary-to-if-else.test.ts
219	325	src/refactorings/extract-variable.test.ts
19	24	src/refactorings/flip-if-else.test.ts
14	15	src/refactorings/flip-ternary.test.ts
50	45	src/refactorings/inline-variable.test.ts
28	48	src/refactorings/move-statement-down.test.ts
21	36	src/refactorings/move-statement-up.test.ts
109	73	src/refactorings/negate-expression.test.ts
14	22	src/refactorings/remove-redundant-else.test.ts

--0aaa905a--2019-07-14--Nicolas Carlo
22	0	src/tests-helpers.ts

--e1a0b1dc--2019-07-11--Nicolas Carlo
--69114b25--2019-07-11--dependabot[bot]
3	2	yarn.lock

--82bc3bb1--2019-07-10--Nicolas Carlo
1	1	package.json

--3b2cc352--2019-07-10--Nicolas Carlo
1	1	package.json

--a2f72c27--2019-07-10--Nicolas Carlo
2	2	package.json
54	0	webpack.config.js

--a13c4768--2019-07-10--Nicolas Carlo
4	1	package.json
1377	59	yarn.lock

--d0064d90--2019-07-10--Nicolas Carlo
2	3	package.json

--067c6564--2019-07-10--Nicolas Carlo
0	1	.vscodeignore

--92342fed--2019-07-09--Nicolas Carlo
1	0	src/refactorings/editor/selection.ts
13	0	src/refactorings/extract-variable.test.ts

--dd1c48c1--2019-07-09--Nicolas Carlo
1	0	.vscodeignore

--295859cf--2019-07-09--Nicolas Carlo
1	1	package.json

--e6fe783a--2019-07-09--Nicolas Carlo
16	13	CHANGELOG.md

--54629d43--2019-07-09--Nicolas Carlo
10	3	package.json

--f6e60fdc--2019-07-09--Nicolas Carlo
37	37	package.json

--615aa302--2019-07-09--Nicolas Carlo
9	3	.vscodeignore
1	1	package.json

--06be00d7--2019-07-09--Nicolas Carlo
1	1	README.md

--7f978e31--2019-07-09--Nicolas Carlo
13	0	CONTRIBUTING.md
18	4	README.md

--5c9cc0b3--2019-07-09--Nicolas Carlo
35	1	README.md

--2b03936f--2019-07-09--Nicolas Carlo
2	0	CHANGELOG.md

--cbbef3ef--2019-07-07--Nicolas Carlo
1	1	CONTRIBUTING.md

--9ca6a3a1--2019-07-07--Nicolas Carlo
47	12	CONTRIBUTING.md

--9f4d5fce--2019-07-07--Nicolas Carlo
1	1	.github/ISSUE_TEMPLATE/bug_report.md
1	1	.github/ISSUE_TEMPLATE/feature_request.md

--0cfb448b--2019-07-07--Nicolas Carlo
29	0	.github/ISSUE_TEMPLATE/bug_report.md
19	0	.github/ISSUE_TEMPLATE/feature_request.md

--6e87adca--2019-07-07--Nicolas Carlo
21	1	src/refactorings/inline-variable.test.ts
6	1	src/refactorings/inline-variable.ts

--efe3712c--2019-07-07--Nicolas Carlo
19	0	src/refactorings/inline-variable.test.ts
11	5	src/refactorings/inline-variable.ts

--b9fab030--2019-07-07--Nicolas Carlo
11	7	src/refactorings/inline-variable.ts

--fd950144--2019-07-07--Nicolas Carlo
1	0	src/refactorings/editor/selection.ts
11	0	src/refactorings/extract-variable.test.ts

--73c4bca4--2019-07-07--Nicolas Carlo
3	1	src/refactorings/editor/selection.ts
20	0	src/refactorings/extract-variable.test.ts

--6b577c17--2019-07-07--Nicolas Carlo
1	0	src/refactorings/editor/selection.ts
10	0	src/refactorings/extract-variable.test.ts

--5b137190--2019-07-07--Nicolas Carlo
25	0	src/refactorings/flip-if-else.test.ts
3	1	src/refactorings/flip-if-else.ts

--170d72ff--2019-07-07--Nicolas Carlo
65	112	src/refactorings/move-statement-down.test.ts
25	44	src/refactorings/move-statement-up.test.ts

--7875a5b4--2019-07-07--Nicolas Carlo
80	0	src/refactorings/move-statement-down.test.ts
28	0	src/refactorings/move-statement-down.ts

--a202e545--2019-07-07--Nicolas Carlo
3	1	src/refactorings/ast.ts
56	41	src/refactorings/move-statement-down.test.ts
45	8	src/refactorings/move-statement-down.ts
56	44	src/refactorings/move-statement-up.test.ts
45	8	src/refactorings/move-statement-up.ts

--36ee67ec--2019-07-06--Nicolas Carlo
2	41	src/commands.ts
0	13	src/refactorings/adapters/put-cursor-at-in-vscode.ts
0	5	src/refactorings/editor/i-put-cursor-at.ts
9	18	src/refactorings/move-statement-down.test.ts
2	5	src/refactorings/move-statement-down.ts
9	18	src/refactorings/move-statement-up.test.ts
2	5	src/refactorings/move-statement-up.ts

--823538bf--2019-07-06--Nicolas Carlo
8	3	src/refactorings/adapters/write-code-in-memory.ts
13	4	src/refactorings/adapters/write-code-in-vscode.ts
2	2	src/refactorings/convert-if-else-to-ternary.test.ts
2	2	src/refactorings/convert-ternary-to-if-else.test.ts
39	3	src/refactorings/editor/i-write-code-contract-test.ts
2	1	src/refactorings/editor/i-write-code.ts
2	2	src/refactorings/flip-if-else.test.ts
2	2	src/refactorings/flip-ternary.test.ts
2	2	src/refactorings/move-statement-down.test.ts
2	2	src/refactorings/move-statement-up.test.ts
2	2	src/refactorings/remove-redundant-else.test.ts

--0c82bd3c--2019-07-06--Nicolas Carlo
3	3	src/commands.ts
11	2	src/refactorings/adapters/write-code-in-vscode.ts

--5bcf496c--2019-07-06--Nicolas Carlo
0	1	src/refactorings/adapters/put-cursor-at-in-vscode.ts

--ae671be5--2019-07-06--Nicolas Carlo
1	3	src/refactorings/ast.ts

--8c647e96--2019-07-06--Nicolas Carlo
3	44	src/refactorings/adapters/write-code-in-memory.ts
26	14	src/refactorings/adapters/write-code-in-vscode.ts
2	7	src/refactorings/convert-if-else-to-ternary.ts
2	7	src/refactorings/convert-ternary-to-if-else.ts
5	126	src/refactorings/editor/i-write-code-contract-test.ts
1	1	src/refactorings/editor/i-write-code.ts
8	11	src/refactorings/flip-if-else.ts
2	7	src/refactorings/flip-ternary.ts
2	8	src/refactorings/move-statement-down.ts
2	8	src/refactorings/move-statement-up.ts
2	7	src/refactorings/remove-redundant-else.ts

--f20ea3dc--2019-07-06--Nicolas Carlo
10	26	src/refactorings/ast.ts
5	7	src/refactorings/convert-if-else-to-ternary.ts
4	7	src/refactorings/convert-ternary-to-if-else.ts
4	6	src/refactorings/flip-if-else.ts
4	6	src/refactorings/flip-ternary.ts
4	6	src/refactorings/move-statement-down.ts
4	6	src/refactorings/move-statement-up.ts
2	2	src/refactorings/negate-expression.ts
5	7	src/refactorings/remove-redundant-else.ts

--d19edbf6--2019-07-06--Nicolas Carlo
41	40	src/refactorings/move-statement-down.test.ts
44	40	src/refactorings/move-statement-up.test.ts

--7b1df674--2019-07-06--Nicolas Carlo
2	2	src/refactorings/convert-if-else-to-ternary.ts
3	2	src/refactorings/move-statement-down.ts
3	2	src/refactorings/move-statement-up.ts
8	7	src/refactorings/remove-redundant-else.ts

--4d2debca--2019-07-06--Nicolas Carlo
11	6	src/refactorings/move-statement-down.test.ts
1	1	src/refactorings/move-statement-down.ts
11	6	src/refactorings/move-statement-up.test.ts

--2eb5abc9--2019-07-06--Nicolas Carlo
0	4	README.md

--080bc9bf--2019-07-06--Nicolas Carlo
0	9	README.md

--445f12cd--2019-07-06--Nicolas Carlo
2	8	README.md

--ce83c106--2019-07-06--Nicolas Carlo
-	-	docs/adr/0004-refactoring-after-recast.gif
-	-	docs/adr/0004-refactoring-before-recast.gif
41	0	docs/adr/0004-use-recast-for-ast-manipulation.md

--6095a22b--2019-07-04--Nicolas Carlo
0	1	package.json
40	13	src/refactorings/ast.ts
8	3	src/refactorings/editor/selection.ts
5	1	src/refactorings/flip-if-else.ts
6	0	src/refactorings/inline-variable.ts
6	2	src/refactorings/move-statement-down.ts
6	2	src/refactorings/move-statement-up.ts
2	2	src/refactorings/negate-expression.ts
1	3	src/refactorings/remove-redundant-else.test.ts
1	1	yarn.lock

--8e788696--2019-07-04--Nicolas Carlo
2	1	package.json
21	1	yarn.lock

--fd210ed0--2019-07-04--Nicolas Carlo
1	0	src/refactorings/editor/selection.ts
12	2	src/refactorings/extract-variable.test.ts

--90cae7ec--2019-07-04--Nicolas Carlo
3	0	src/refactorings/editor/selection.ts
12	0	src/refactorings/extract-variable.test.ts

--933270dd--2019-07-04--Nicolas Carlo
1	1	README.md
-	-	docs/logo/abracadabra-logo.png

--076ce974--2019-07-03--Nicolas Carlo
33	0	docs/adr/0003-use-hosted-image-links-in-readme.md

--678b7985--2019-07-03--Nicolas Carlo
1	1	README.md

--5a7926e0--2019-07-03--Nicolas Carlo
1	0	src/refactorings/editor/selection.ts
11	0	src/refactorings/extract-variable.test.ts

--6286b509--2019-07-03--Nicolas Carlo
4	4	src/refactorings/editor/selection.ts

--b8916138--2019-07-03--Nicolas Carlo
2	2	src/refactorings/editor/selection.ts
12	0	src/refactorings/extract-variable.test.ts

--b4fba140--2019-07-03--Nicolas Carlo
-	-	docs/demo/move-down-statement.gif
-	-	docs/demo/move-statement-down.gif
-	-	docs/demo/move-statement-up.gif
-	-	docs/demo/move-up-statement.gif

--d61f592d--2019-07-03--Nicolas Carlo
2	0	CHANGELOG.md

--167a9a4a--2019-07-03--Nicolas Carlo
28	0	README.md
-	-	docs/demo/move-down-statement.gif
-	-	docs/demo/move-up-statement.gif

--de61fe9a--2019-07-03--Nicolas Carlo
20	0	src/refactorings/move-statement-down.test.ts
5	1	src/refactorings/move-statement-down.ts
20	0	src/refactorings/move-statement-up.test.ts
5	1	src/refactorings/move-statement-up.ts

--0bfa7125--2019-07-03--Nicolas Carlo
18	0	src/refactorings/move-statement-down.test.ts
18	0	src/refactorings/move-statement-up.test.ts

--703ca588--2019-07-03--Nicolas Carlo
16	1	README.md

--03983435--2019-07-03--Nicolas Carlo
4	0	src/refactorings/editor/position.ts
2	2	src/refactorings/move-statement-down.test.ts
3	1	src/refactorings/move-statement-down.ts
2	2	src/refactorings/move-statement-up.test.ts
3	1	src/refactorings/move-statement-up.ts

--ac07e7b3--2019-07-03--Nicolas Carlo
20	1	src/commands.ts
24	1	src/refactorings/move-statement-down.test.ts
15	3	src/refactorings/move-statement-down.ts

--92df0a5d--2019-06-28--Nicolas Carlo
1	1	src/action-providers.ts
2	2	src/commands.ts
0	14	src/refactorings/adapters/selection-from-vscode.ts
15	1	src/refactorings/adapters/write-code-in-vscode.ts

--79f1bb43--2019-06-28--Nicolas Carlo
21	1	src/commands.ts
14	0	src/refactorings/adapters/put-cursor-at-in-vscode.ts
1	1	src/refactorings/adapters/write-code-in-vscode.ts
5	0	src/refactorings/editor/i-put-cursor-at.ts
24	1	src/refactorings/move-statement-up.test.ts
15	3	src/refactorings/move-statement-up.ts

--a1fc0393--2019-06-28--Nicolas Carlo
56	0	package.json
10	0	src/commands.ts
3	1	src/refactoring-command.ts

--647a5473--2019-06-28--Nicolas Carlo
16	0	src/refactorings/editor/i-show-error-message.ts
4	0	src/refactorings/editor/selection.ts
148	0	src/refactorings/move-statement-down.test.ts
70	0	src/refactorings/move-statement-down.ts
148	0	src/refactorings/move-statement-up.test.ts
68	0	src/refactorings/move-statement-up.ts

--29e2f7ea--2019-07-03--Nicolas Carlo
--2f1af575--2019-06-29--Fabien BERNARD
6	0	README.md
1	0	docs/logo/abracadabra-logo.svg
-	-	docs/logo/abracadabra-vignette.png
4162	0	docs/logo/logo.ai

--3ac43852--2019-06-27--Nicolas Carlo
8	0	src/refactorings/extract-variable.test.ts

--b4b91a1d--2019-06-27--Nicolas Carlo
3	0	src/refactorings/convert-ternary-to-if-else.ts

--74a9d24f--2019-06-26--Nicolas Carlo
9	28	src/action-providers.ts
11	11	src/commands.ts
3	19	src/extension.ts

--ab5f5b52--2019-06-26--Nicolas Carlo
22	91	src/commands.ts
12	0	src/refactorings/refactoring.ts

--8c929f55--2019-06-26--Nicolas Carlo
7	7	src/action-providers.ts
10	10	src/commands.ts
14	0	src/refactoring-command.ts
0	14	src/refactoring.ts

--e3ec50f2--2019-06-26--Nicolas Carlo
1	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/convert-ternary-to-if-else.gif

--dd388674--2019-06-26--Nicolas Carlo
22	0	package.json
31	0	src/action-providers.ts
23	0	src/commands.ts
4	2	src/extension.ts
2	1	src/refactoring.ts

--cb394855--2019-06-26--Nicolas Carlo
22	0	src/refactorings/convert-ternary-to-if-else.test.ts

--def270e9--2019-06-26--Nicolas Carlo
22	0	src/refactorings/convert-ternary-to-if-else.test.ts

--73b66251--2019-06-26--Nicolas Carlo
36	4	src/refactorings/convert-ternary-to-if-else.test.ts

--d5276c61--2019-06-26--Nicolas Carlo
21	0	src/refactorings/convert-ternary-to-if-else.test.ts
27	47	src/refactorings/convert-ternary-to-if-else.ts

--79c492cd--2019-06-26--Nicolas Carlo
1	1	src/refactorings/convert-ternary-to-if-else.test.ts
47	88	src/refactorings/convert-ternary-to-if-else.ts

--d8ebb12a--2019-06-26--Nicolas Carlo
48	0	src/refactorings/convert-ternary-to-if-else.test.ts
81	12	src/refactorings/convert-ternary-to-if-else.ts

--208f7fd9--2019-06-25--Nicolas Carlo
118	0	src/refactorings/convert-ternary-to-if-else.test.ts
126	0	src/refactorings/convert-ternary-to-if-else.ts
4	0	src/refactorings/editor/i-show-error-message.ts

--53caef8b--2019-06-25--Nicolas Carlo
4	1	src/refactorings/adapters/delegate-to-vscode.ts
1	1	src/refactorings/adapters/selection-from-vscode.ts
1	1	src/refactorings/adapters/show-error-message-in-vscode.ts
1	1	src/refactorings/adapters/write-code-in-memory.test.ts
1	1	src/refactorings/adapters/write-code-in-memory.ts
3	3	src/refactorings/adapters/write-code-in-vscode.ts
1	1	src/refactorings/ast.ts
4	4	src/refactorings/convert-if-else-to-ternary.test.ts
3	3	src/refactorings/convert-if-else-to-ternary.ts
7	0	src/refactorings/editor/i-delegate-to-editor.ts
57	0	src/refactorings/editor/i-show-error-message.ts
160	0	src/refactorings/editor/i-write-code-contract-test.ts
18	0	src/refactorings/editor/i-write-code.ts
105	0	src/refactorings/editor/position.test.ts
55	0	src/refactorings/editor/position.ts
75	0	src/refactorings/editor/selection.test.ts
130	0	src/refactorings/editor/selection.ts
4	4	src/refactorings/extract-variable.test.ts
5	5	src/refactorings/extract-variable.ts
4	4	src/refactorings/flip-if-else.test.ts
3	3	src/refactorings/flip-if-else.ts
4	4	src/refactorings/flip-ternary.test.ts
3	3	src/refactorings/flip-ternary.ts
0	7	src/refactorings/i-delegate-to-editor.ts
0	57	src/refactorings/i-show-error-message.ts
0	160	src/refactorings/i-write-code-contract-test.ts
0	18	src/refactorings/i-write-code.ts
3	3	src/refactorings/inline-variable.test.ts
3	3	src/refactorings/inline-variable.ts
3	3	src/refactorings/negate-expression.test.ts
3	3	src/refactorings/negate-expression.ts
0	105	src/refactorings/position.test.ts
0	55	src/refactorings/position.ts
4	4	src/refactorings/remove-redundant-else.test.ts
3	3	src/refactorings/remove-redundant-else.ts
1	1	src/refactorings/rename-symbol.test.ts
1	1	src/refactorings/rename-symbol.ts
0	75	src/refactorings/selection.test.ts
0	130	src/refactorings/selection.ts

--e65bcab7--2019-06-25--Nicolas Carlo
8	0	src/refactorings/negate-expression.test.ts
5	0	src/refactorings/negate-expression.ts

--5a03af9b--2019-06-24--Nicolas Carlo
11	0	src/refactorings/negate-expression.test.ts
3	0	src/refactorings/negate-expression.ts

--d23f818e--2019-06-24--Nicolas Carlo
51	1	src/refactorings/convert-if-else-to-ternary.test.ts
12	2	src/refactorings/convert-if-else-to-ternary.ts

--a0c93d02--2019-06-24--Nicolas Carlo
1	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/convert-if-else-to-ternary.gif

--230eb0a9--2019-06-24--Nicolas Carlo
22	0	package.json
32	1	src/action-providers.ts
23	0	src/commands.ts
4	2	src/extension.ts
2	1	src/refactoring.ts

--7ccc5aac--2019-06-24--Nicolas Carlo
103	2	src/refactorings/convert-if-else-to-ternary.test.ts
16	0	src/refactorings/convert-if-else-to-ternary.ts

--c7d97cad--2019-06-24--Nicolas Carlo
20	0	src/refactorings/convert-if-else-to-ternary.test.ts
53	13	src/refactorings/convert-if-else-to-ternary.ts

--8fc3b11d--2019-06-23--Nicolas Carlo
1	38	src/refactorings/adapters/show-error-message-in-vscode.ts
42	1	src/refactorings/i-show-error-message.ts

--97f05b70--2019-06-22--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
59	0	src/refactorings/convert-if-else-to-ternary.test.ts
71	0	src/refactorings/convert-if-else-to-ternary.ts
1	0	src/refactorings/i-show-error-message.ts

--4ee1e09c--2019-06-22--Nicolas Carlo
1	1	src/refactorings/flip-ternary.test.ts

--7698177f--2019-06-22--Nicolas Carlo
3	1	README.md

--4394e4c9--2019-06-22--Nicolas Carlo
1	0	CHANGELOG.md
10	0	README.md
-	-	docs/demo/flip-ternary.gif

--15d7bcbc--2019-06-22--Nicolas Carlo
22	0	package.json
27	1	src/action-providers.ts
23	0	src/commands.ts
4	2	src/extension.ts
2	1	src/refactoring.ts

--57bb460f--2019-06-22--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
64	0	src/refactorings/flip-ternary.test.ts
69	0	src/refactorings/flip-ternary.ts
1	0	src/refactorings/i-show-error-message.ts

--06d3f4e3--2019-06-22--Nicolas Carlo
7	0	README.md

--4de25fec--2019-06-22--Nicolas Carlo
1	0	CHANGELOG.md
9	0	README.md
-	-	docs/demo/flip-if-else.gif

--a591b3e5--2019-06-22--Nicolas Carlo
1	0	src/extension.ts

--403ac510--2019-06-22--Nicolas Carlo
22	0	package.json
28	1	src/action-providers.ts
23	0	src/commands.ts
4	2	src/extension.ts
2	1	src/refactoring.ts
5	1	src/refactorings/flip-if-else.ts

--69d4838b--2019-06-22--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
95	0	src/refactorings/flip-if-else.test.ts
65	0	src/refactorings/flip-if-else.ts
1	0	src/refactorings/i-show-error-message.ts
1	0	src/refactorings/negate-expression.ts

--40d3dca0--2019-06-22--Nicolas Carlo
64	0	src/refactorings/extract-variable.test.ts
17	3	src/refactorings/extract-variable.ts

--89295e05--2019-06-22--Nicolas Carlo
3	3	src/refactorings/extract-variable.test.ts
2	0	src/refactorings/extract-variable.ts

--f6eb326d--2019-06-22--Nicolas Carlo
38	9	src/refactorings/remove-redundant-else.test.ts
5	2	src/refactorings/remove-redundant-else.ts

--06f4722f--2019-06-21--Nicolas Carlo
7	0	src/refactorings/negate-expression.test.ts
1	1	src/refactorings/negate-expression.ts

--d5bfee0f--2019-06-21--Nicolas Carlo
21	16	src/refactorings/ast.ts
39	36	src/refactorings/negate-expression.ts
8	7	src/refactorings/remove-redundant-else.ts

--8bc3cba9--2019-06-21--Nicolas Carlo
38	0	src/refactorings/inline-variable.test.ts
10	11	src/refactorings/inline-variable.ts

--d7b7e94d--2019-06-21--Nicolas Carlo
15	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/selection.ts

--5e46432a--2019-06-19--Nicolas Carlo
12	0	src/refactorings/negate-expression.test.ts
5	0	src/refactorings/negate-expression.ts

--b4629db0--2019-06-19--Nicolas Carlo
19	0	src/refactorings/inline-variable.test.ts
1	0	src/refactorings/inline-variable.ts

--f8faddcf--2019-06-19--Nicolas Carlo
12	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/selection.ts

--35366f06--2019-06-19--Nicolas Carlo
21	0	src/refactorings/inline-variable.test.ts
2	1	src/refactorings/inline-variable.ts

--040a0e5e--2019-06-19--Nicolas Carlo
1	1	.vscode/extensions.json

--2640156f--2019-06-19--Nicolas Carlo
1	0	CHANGELOG.md
11	1	README.md
-	-	docs/demo/remove-redundant-else.gif

--ba393841--2019-06-19--Nicolas Carlo
2	2	src/action-providers.ts

--cbd1ab33--2019-06-19--Nicolas Carlo
22	0	package.json
34	5	src/action-providers.ts
27	1	src/commands.ts
4	1	src/extension.ts
2	1	src/refactoring.ts
5	1	src/refactorings/remove-redundant-else.ts

--2b6c0aa1--2019-06-19--Nicolas Carlo
8	1	src/refactorings/remove-redundant-else.test.ts
0	1	src/refactorings/remove-redundant-else.ts

--0dca5214--2019-06-19--Nicolas Carlo
22	0	src/refactorings/remove-redundant-else.test.ts
19	17	src/refactorings/remove-redundant-else.ts

--2eb05873--2019-06-19--Nicolas Carlo
24	0	src/refactorings/remove-redundant-else.test.ts

--6d8cb1f7--2019-06-19--Nicolas Carlo
14	2	src/refactorings/extract-variable.test.ts
2	0	src/refactorings/selection.ts

--d675dcaf--2019-06-18--Nicolas Carlo
4	4	src/commands.ts
0	5	src/refactorings/adapters/update-code-in-memory.test.ts
0	58	src/refactorings/adapters/update-code-in-memory.ts
0	49	src/refactorings/adapters/update-code-in-vscode.ts
5	0	src/refactorings/adapters/write-code-in-memory.test.ts
54	0	src/refactorings/adapters/write-code-in-memory.ts
51	0	src/refactorings/adapters/write-code-in-vscode.ts
1	1	src/refactorings/ast.ts
9	9	src/refactorings/extract-variable.test.ts
3	3	src/refactorings/extract-variable.ts
0	160	src/refactorings/i-update-code-contract-test.ts
0	20	src/refactorings/i-update-code.ts
160	0	src/refactorings/i-write-code-contract-test.ts
18	0	src/refactorings/i-write-code.ts
7	7	src/refactorings/inline-variable.test.ts
3	3	src/refactorings/inline-variable.ts
8	8	src/refactorings/negate-expression.test.ts
3	3	src/refactorings/negate-expression.ts
3	3	src/refactorings/remove-redundant-else.test.ts
1	1	src/refactorings/remove-redundant-else.ts

--3f607c0c--2019-06-18--Nicolas Carlo
38	0	src/refactorings/remove-redundant-else.test.ts
4	3	src/refactorings/remove-redundant-else.ts

--03ee28d4--2019-06-18--Nicolas Carlo
24	19	src/refactorings/remove-redundant-else.test.ts

--ba4bdf82--2019-06-18--Nicolas Carlo
5	0	src/refactorings/adapters/update-code-in-memory.test.ts
58	0	src/refactorings/adapters/update-code-in-memory.ts
160	0	src/refactorings/i-update-code-contract-test.ts

--c58ac7bd--2019-06-18--Nicolas Carlo
12	8	src/refactorings/ast.ts
7	4	src/refactorings/negate-expression.ts
6	13	src/refactorings/remove-redundant-else.ts

--e68ebbcb--2019-06-18--Nicolas Carlo
14	12	src/refactorings/adapters/update-code-in-vscode.ts
5	1	src/refactorings/i-update-code.ts
5	25	src/refactorings/remove-redundant-else.test.ts
13	16	src/refactorings/remove-redundant-else.ts

--1beaac91--2019-06-17--Nicolas Carlo
10	0	src/refactorings/extract-variable.test.ts
2	0	src/refactorings/selection.ts

--d9d67af5--2019-06-17--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
1	0	src/refactorings/i-show-error-message.ts
84	0	src/refactorings/remove-redundant-else.test.ts
68	0	src/refactorings/remove-redundant-else.ts

--5686d96f--2019-06-17--Nicolas Carlo
3	0	src/commands.ts

--56b8f03f--2019-06-17--Nicolas Carlo
20	0	src/refactorings/extract-variable.test.ts
3	1	src/refactorings/selection.ts

--379d8c74--2019-06-16--Nicolas Carlo
4	0	README.md
-	-	docs/demo/magic.gif

--4398faac--2019-06-16--Nicolas Carlo
1	1	CHANGELOG.md
2	2	CONTRIBUTING.md
12	12	README.md
-	-	docs/demo/command-palette.png
35	35	package.json
4	4	src/refactoring.ts

--7ad6958a--2019-06-16--Nicolas Carlo
3	0	README.md

--38b86e22--2019-06-16--Nicolas Carlo
5	1	README.md
-	-	docs/demo/extension.gif
-	-	docs/demo/extract-variable-partial.gif
-	-	docs/demo/extract-variable.gif

--15525b38--2019-06-16--Nicolas Carlo
1	0	CHANGELOG.md
14	0	README.md
-	-	docs/demo/negate-expression-partial.gif
-	-	docs/demo/negate-expression.gif

--bb918301--2019-06-16--Nicolas Carlo
42	1	src/refactorings/negate-expression.test.ts
22	3	src/refactorings/negate-expression.ts

--535cbb3b--2019-06-16--Nicolas Carlo
8	1	README.md

--da5022b7--2019-06-16--Nicolas Carlo
4	11	README.md
-	-	docs/demo/extract-variable-with-lightbulb.gif
-	-	docs/demo/extract-variable-with-shortcut.gif
-	-	docs/demo/extract-variable.gif
0	30	src/action-providers.ts
1	4	src/extension.ts
1	6	src/refactorings/extract-variable.ts

--7e3014e4--2019-06-16--Nicolas Carlo
10	6	src/action-providers.ts
26	13	src/refactorings/negate-expression.ts

--36b91c3e--2019-06-16--Nicolas Carlo
20	102	src/refactorings/negate-expression.test.ts

--aa844173--2019-06-16--Nicolas Carlo
11	0	src/refactorings/negate-expression.test.ts
4	0	src/refactorings/negate-expression.ts

--ae676c83--2019-06-16--Nicolas Carlo
1	1	src/refactorings/negate-expression.test.ts
9	2	src/refactorings/negate-expression.ts

--b49b18ad--2019-06-16--Nicolas Carlo
22	0	package.json
29	1	src/action-providers.ts
23	0	src/commands.ts
6	2	src/extension.ts
2	1	src/refactoring.ts
5	1	src/refactorings/negate-expression.ts

--33784ed2--2019-06-16--Nicolas Carlo
36	0	src/refactorings/negate-expression.test.ts

--c123cfb2--2019-06-16--Nicolas Carlo
16	14	src/refactorings/negate-expression.ts

--e56b1b40--2019-06-16--Nicolas Carlo
11	0	src/refactorings/negate-expression.test.ts
9	0	src/refactorings/negate-expression.ts

--c8a3a14e--2019-06-16--Nicolas Carlo
2	3	package.json
29	1	src/refactorings/ast.ts
60	45	src/refactorings/negate-expression.ts
1	9	yarn.lock

--d1902708--2019-06-15--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
1	0	src/refactorings/i-show-error-message.ts
15	1	src/refactorings/negate-expression.test.ts
4	3	src/refactorings/negate-expression.ts

--25c8b5c8--2019-06-15--Nicolas Carlo
11	0	src/refactorings/negate-expression.test.ts
27	26	src/refactorings/negate-expression.ts

--82bf4a81--2019-06-15--Nicolas Carlo
34	4	src/refactorings/negate-expression.test.ts
32	34	src/refactorings/negate-expression.ts

--844cf8f6--2019-06-15--Nicolas Carlo
3	0	src/refactorings/negate-expression.ts

--c5a81cc6--2019-06-15--Nicolas Carlo
3	1	package.json
128	0	src/refactorings/negate-expression.test.ts
96	0	src/refactorings/negate-expression.ts
8	0	yarn.lock

--8b0cc641--2019-06-15--Nicolas Carlo
1	0	CHANGELOG.md
13	0	README.md
-	-	docs/demo/inline-variable.gif

--131fc3f2--2019-06-15--Nicolas Carlo
10	0	src/refactorings/inline-variable.test.ts
32	7	src/refactorings/inline-variable.ts

--8d40df44--2019-06-14--Nicolas Carlo
1	1	src/refactorings/inline-variable.test.ts

--ebd582cc--2019-06-14--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
2	1	src/refactorings/i-show-error-message.ts
13	0	src/refactorings/inline-variable.test.ts
25	5	src/refactorings/inline-variable.ts

--33e6b5a7--2019-06-14--Nicolas Carlo
12	1	src/refactorings/extract-variable.test.ts

--2ba1129f--2019-06-14--Nicolas Carlo
8	0	src/refactorings/extract-variable.test.ts
5	0	src/refactorings/extract-variable.ts

--6dec1e98--2019-06-14--Nicolas Carlo
1	3	src/refactorings/ast.ts
22	19	src/refactorings/extract-variable.ts

--2d37b326--2019-06-14--Nicolas Carlo
8	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/extract-variable.ts

--6911c212--2019-06-14--Nicolas Carlo
25	0	src/refactorings/inline-variable.test.ts
19	3	src/refactorings/inline-variable.ts

--e36ad98f--2019-06-12--Nicolas Carlo
7	2	src/refactorings/ast.ts
54	54	src/refactorings/inline-variable.ts

--9b5f8bb5--2019-06-12--Nicolas Carlo
48	1	src/refactorings/inline-variable.test.ts
36	27	src/refactorings/inline-variable.ts

--dcd673ea--2019-06-12--Nicolas Carlo
3	0	src/refactorings/adapters/show-error-message-in-vscode.ts
2	1	src/refactorings/i-show-error-message.ts
49	0	src/refactorings/inline-variable.test.ts
38	4	src/refactorings/inline-variable.ts
1	1	tsconfig.json

--1ae2c83a--2019-06-09--Nicolas Carlo
9	0	src/refactorings/ast.ts
75	0	src/refactorings/inline-variable.test.ts
80	17	src/refactorings/inline-variable.ts

--914789ed--2019-06-09--Nicolas Carlo
22	0	src/refactorings/selection.test.ts
6	0	src/refactorings/selection.ts

--b71641ad--2019-06-09--Nicolas Carlo
22	0	src/refactorings/selection.test.ts
6	0	src/refactorings/selection.ts

--f3c967b2--2019-06-09--Nicolas Carlo
7	6	src/refactorings/inline-variable.test.ts

--094c183e--2019-06-09--Nicolas Carlo
14	16	src/refactorings/inline-variable.test.ts

--333be8ae--2019-06-08--Nicolas Carlo
25	1	src/refactorings/inline-variable.test.ts
11	10	src/refactorings/inline-variable.ts

--b91bb6be--2019-06-08--Nicolas Carlo
2	3	src/commands.ts
6	0	src/refactorings/adapters/show-error-message-in-vscode.ts
2	0	src/refactorings/ast.ts
3	1	src/refactorings/i-show-error-message.ts
33	4	src/refactorings/inline-variable.test.ts
16	12	src/refactorings/inline-variable.ts

--a47f29e7--2019-06-08--Nicolas Carlo
1	1	src/refactorings/adapters/show-error-message-in-vscode.ts

--ed32c294--2019-06-08--Nicolas Carlo
28	0	package.json
24	0	src/commands.ts
5	1	src/extension.ts
2	1	src/refactoring.ts
68	0	src/refactorings/inline-variable.test.ts
93	0	src/refactorings/inline-variable.ts

--25f4c009--2019-06-08--Nicolas Carlo
8	0	src/refactorings/position.ts
31	0	src/refactorings/selection.test.ts
11	0	src/refactorings/selection.ts

--ca541ca3--2019-06-07--Nicolas Carlo
17	1	src/refactorings/ast.ts
6	14	src/refactorings/extract-variable.ts

--3d9e48cd--2019-06-07--Nicolas Carlo
1	0	package.json
2	2	src/commands.ts
47	0	src/refactorings/adapters/update-code-in-vscode.ts
0	43	src/refactorings/adapters/writable-vscode.ts
1	1	src/refactorings/ast.ts
30	28	src/refactorings/extract-variable.test.ts
3	4	src/refactorings/extract-variable.ts
16	0	src/refactorings/i-update-code.ts
0	16	src/refactorings/i-write-updates.ts
4	0	yarn.lock

--da80b6e1--2019-06-07--Nicolas Carlo
378	395	src/refactorings/extract-variable.test.ts
4	0	src/refactorings/selection.ts

--b878adcd--2019-06-07--Nicolas Carlo
13	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/extract-variable.ts

--73d4bbb9--2019-06-07--Nicolas Carlo
9	0	src/refactorings/extract-variable.test.ts
5	0	src/refactorings/extract-variable.ts

--c327151d--2019-06-07--Nicolas Carlo
7	3	CHANGELOG.md
6	0	CONTRIBUTING.md
47	16	README.md

--3603a42d--2019-06-07--Nicolas Carlo
-	-	docs/demo/command-palette.png
-	-	docs/demo/extract-variable-with-lightbulb.gif

--15d06a52--2019-06-07--Nicolas Carlo
-	-	docs/demo/extract-variable-with-lightbulb.gif
-	-	docs/demo/extract-variable-with-shortcut.gif

--3f479e25--2019-06-07--Nicolas Carlo
13	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/selection.ts

--6826558f--2019-06-07--Nicolas Carlo
13	0	src/refactorings/extract-variable.test.ts
1	0	src/refactorings/selection.ts

--76b32573--2019-06-07--Nicolas Carlo
15	0	src/refactorings/extract-variable.test.ts
2	1	src/refactorings/extract-variable.ts
2	1	src/refactorings/selection.ts

--93023ff3--2019-06-07--Nicolas Carlo
20	0	src/refactorings/extract-variable.test.ts
3	1	src/refactorings/extract-variable.ts

--1d27d35e--2019-06-07--Nicolas Carlo
12	0	src/refactorings/extract-variable.test.ts

--d990c6b8--2019-06-07--Nicolas Carlo
1	0	src/refactorings/extract-variable.ts

--dd21db43--2019-06-06--Nicolas Carlo
8	17	src/refactorings/selection.ts

--e52f4111--2019-06-06--Nicolas Carlo
22	3	src/refactorings/extract-variable.test.ts
4	2	src/refactorings/selection.ts

--17e56ba6--2019-06-06--Nicolas Carlo
64	0	src/action-providers.ts
59	0	src/commands.ts
13	127	src/extension.ts
7	0	src/refactoring.ts
14	0	src/refactorings/adapters/selection-from-vscode.ts

--65e54a13--2019-06-06--Nicolas Carlo
5	1	package.json
77	6	src/extension.ts
1	1	src/refactorings/adapters/show-error-message-in-vscode.ts
1	1	src/refactorings/extract-variable.test.ts
44	26	src/refactorings/extract-variable.ts
1	1	src/refactorings/i-show-error-message.ts

--2056fc7a--2019-06-05--Nicolas Carlo
211	189	src/refactorings/extract-variable.test.ts

--cf648d5d--2019-06-05--Nicolas Carlo
9	0	src/refactorings/extract-variable.test.ts
5	0	src/refactorings/extract-variable.ts

--c99a7fe9--2019-06-05--Nicolas Carlo
24	32	src/refactorings/extract-variable.ts

--c0bf8f83--2019-06-05--Nicolas Carlo
36	0	src/refactorings/extract-variable.test.ts
11	1	src/refactorings/extract-variable.ts

--f342bcf9--2019-06-05--Nicolas Carlo
27	0	src/refactorings/extract-variable.test.ts
7	1	src/refactorings/extract-variable.ts
6	2	src/refactorings/selection.ts

--a9e3f691--2019-06-05--Nicolas Carlo
3	3	src/refactorings/extract-variable.test.ts

--f67e42f3--2019-06-05--Nicolas Carlo
14	0	src/refactorings/extract-variable.test.ts
4	0	src/refactorings/extract-variable.ts

--e2162518--2019-06-05--Nicolas Carlo
14	0	src/refactorings/extract-variable.test.ts
6	4	src/refactorings/extract-variable.ts

--02a72343--2019-06-05--Nicolas Carlo
7	14	src/refactorings/extract-variable.test.ts

--8f3f0d55--2019-06-05--Nicolas Carlo
10	15	src/refactorings/extract-variable.test.ts

--e8327eba--2019-06-05--Nicolas Carlo
16	0	src/refactorings/extract-variable.test.ts
4	1	src/refactorings/extract-variable.ts

--2ae95365--2019-06-04--Nicolas Carlo
6	6	src/refactorings/extract-variable.test.ts

--94182ce3--2019-06-04--Nicolas Carlo
18	0	src/refactorings/extract-variable.test.ts

--394fc075--2019-06-04--Nicolas Carlo
20	0	src/refactorings/extract-variable.test.ts
30	13	src/refactorings/extract-variable.ts

--6b1fed2f--2019-06-04--Nicolas Carlo
1	10	src/refactorings/extract-variable.ts

--dec02075--2019-06-03--Nicolas Carlo
41	1	src/refactorings/extract-variable.test.ts
16	2	src/refactorings/selection.ts

--015957d5--2019-06-03--Nicolas Carlo
8	0	src/refactorings/extract-variable.test.ts

--72949050--2019-06-02--Nicolas Carlo
5	5	src/refactorings/ast.ts
1	1	src/refactorings/position.ts
1	1	src/refactorings/selection.ts

--bdc43e9c--2019-06-02--Nicolas Carlo
18	0	src/refactorings/extract-variable.test.ts
6	2	src/refactorings/extract-variable.ts

--6630d96d--2019-06-02--Nicolas Carlo
2	3	src/refactorings/extract-variable.ts
20	18	src/refactorings/selection.ts

--6014743a--2019-06-02--Nicolas Carlo
25	19	src/refactorings/selection.ts

--564bbb1c--2019-06-02--Nicolas Carlo
9	1	src/refactorings/extract-variable.test.ts
105	0	src/refactorings/position.test.ts
6	2	src/refactorings/position.ts

--f155c8a9--2019-06-02--Nicolas Carlo
10	10	src/refactorings/extract-variable.ts

--e725fa45--2019-06-02--Nicolas Carlo
1	0	src/refactorings/extract-variable.test.ts
2	1	src/refactorings/extract-variable.ts

--5a08b744--2019-06-02--Nicolas Carlo
2	6	src/extension.ts
43	0	src/refactorings/adapters/writable-vscode.ts
0	37	src/refactorings/adapters/write-updates-to-vscode.ts
2	18	src/refactorings/ast.ts
98	159	src/refactorings/extract-variable.test.ts
9	25	src/refactorings/extract-variable.ts
5	4	src/refactorings/i-write-updates.ts

--4cda380c--2019-06-02--Nicolas Carlo
5	1	src/extension.ts
10	2	src/refactorings/adapters/write-updates-to-vscode.ts
44	2	src/refactorings/extract-variable.test.ts
9	4	src/refactorings/extract-variable.ts
4	1	src/refactorings/i-write-updates.ts

--20787a67--2019-06-02--Nicolas Carlo
34	29	src/refactorings/extract-variable.ts

--6e335073--2019-06-02--Nicolas Carlo
22	78	src/refactorings/extract-variable.test.ts

--adca0860--2019-06-02--Nicolas Carlo
1	1	src/refactorings/ast.ts
21	8	src/refactorings/extract-variable.ts

--75df9240--2019-06-02--Nicolas Carlo
2	1	src/refactorings/extract-variable.ts

--b2f0c057--2019-06-02--Nicolas Carlo
8	1	src/refactorings/ast.ts
21	0	src/refactorings/extract-variable.test.ts
7	2	src/refactorings/extract-variable.ts

--8098d44a--2019-06-02--Nicolas Carlo
21	0	src/refactorings/extract-variable.test.ts
2	0	src/refactorings/extract-variable.ts

--bc2d2976--2019-06-02--Nicolas Carlo
7	11	src/refactorings/extract-variable.ts

--7cafcafc--2019-06-02--Nicolas Carlo
2	12	src/refactorings/ast.ts
21	0	src/refactorings/extract-variable.test.ts
17	4	src/refactorings/extract-variable.ts

--f9ebe5fd--2019-06-02--Nicolas Carlo
13	2	src/refactorings/ast.ts
21	0	src/refactorings/extract-variable.test.ts
2	2	src/refactorings/extract-variable.ts
1	0	src/refactorings/selection.ts

--f679e905--2019-06-02--Nicolas Carlo
53	59	src/refactorings/extract-variable.test.ts

--3f9aeb9c--2019-06-02--Nicolas Carlo
8	0	package.json

--29836d84--2019-06-02--Nicolas Carlo
31	0	CONTRIBUTING.md
4	3	src/refactorings/ast.ts
5	3	src/refactorings/i-write-updates.ts
3	1	src/refactorings/position.ts
3	1	src/refactorings/selection.ts

--8c0736a5--2019-06-02--Nicolas Carlo
3	1	src/extension.ts
20	0	src/refactorings/adapters/show-error-message-in-vscode.ts
71	5	src/refactorings/extract-variable.test.ts
8	1	src/refactorings/extract-variable.ts
7	0	src/refactorings/i-show-error-message.ts

--bc58fecf--2019-06-02--Nicolas Carlo
1	1	src/extension.ts

--3c96cfdc--2019-06-02--Nicolas Carlo
2	1	package.json

--11ffd48a--2019-06-02--Nicolas Carlo
35	48	src/refactorings/extract-variable.test.ts

--858aaa3f--2019-06-01--Nicolas Carlo
10	0	src/refactorings/ast.ts
20	0	src/refactorings/extract-variable.test.ts
5	3	src/refactorings/extract-variable.ts
11	1	src/refactorings/position.ts
21	1	src/refactorings/selection.ts

--be42a0cb--2019-06-01--Nicolas Carlo
6	5	src/refactorings/extract-variable.ts
1	1	tslint.json

--0893b028--2019-06-01--Nicolas Carlo
2	1	src/refactorings/ast.ts
22	0	src/refactorings/extract-variable.test.ts
8	2	src/refactorings/extract-variable.ts
7	1	src/refactorings/position.ts
29	2	src/refactorings/selection.ts

--91193b28--2019-06-01--Nicolas Carlo
40	0	src/refactorings/ast.ts
2	34	src/refactorings/extract-variable.ts

--1fdd3c04--2019-06-01--Nicolas Carlo
2	2	src/refactorings/extract-variable.test.ts
1	1	src/refactorings/position.ts

--81203035--2019-06-01--Nicolas Carlo
9	5	src/extension.ts
5	5	src/refactorings/extract-variable.test.ts
2	6	src/refactorings/extract-variable.ts
19	12	src/refactorings/selection.ts

--ea62d324--2019-06-01--Nicolas Carlo
5	1	src/extension.ts
1	1	src/refactorings/adapters/write-updates-to-vscode.ts
3	5	src/refactorings/extract-variable.ts
25	0	src/refactorings/position.ts
4	7	src/refactorings/selection.ts

--f0d64cba--2019-06-01--Nicolas Carlo
26	4	src/refactorings/extract-variable.test.ts
6	1	src/refactorings/extract-variable.ts

--60e5eca0--2019-06-01--Nicolas Carlo
2	1	src/refactorings/adapters/write-updates-to-vscode.ts
3	9	src/refactorings/extract-variable.test.ts
6	5	src/refactorings/extract-variable.ts
2	10	src/refactorings/i-write-updates.ts
19	0	src/refactorings/selection.ts

--3df331e0--2019-06-01--Nicolas Carlo
31	9	src/refactorings/extract-variable.test.ts
7	1	src/refactorings/extract-variable.ts

--7202cdeb--2019-06-01--Nicolas Carlo
23	6	src/extension.ts
2	2	src/refactorings/adapters/delegate-to-vscode.ts
2	2	src/refactorings/adapters/write-updates-to-vscode.ts
1	1	src/refactorings/extract-variable.ts
1	1	src/refactorings/i-delegate-to-editor.ts
1	1	src/refactorings/i-write-updates.ts
4	4	src/refactorings/rename-symbol.test.ts
2	2	src/refactorings/rename-symbol.ts

--d33fb1cf--2019-06-01--Nicolas Carlo
27	0	package.json
22	0	src/extension.ts
28	0	src/refactorings/adapters/write-updates-to-vscode.ts
28	0	src/refactorings/extract-variable.test.ts
64	0	src/refactorings/extract-variable.ts
18	0	src/refactorings/i-write-updates.ts
4	4	yarn.lock

--b0436d8f--2019-05-30--Nicolas Carlo
0	5	src/index.test.ts

--a3eb4d93--2019-05-30--Nicolas Carlo
0	41	vsc-extension-quickstart.md

--8f318ec7--2019-05-28--Nicolas Carlo
25	4	package.json
8	21	src/extension.ts
20	0	src/refactorings/adapters/delegate-to-vscode.ts
7	0	src/refactorings/i-delegate-to-editor.ts
12	0	src/refactorings/rename-symbol.test.ts
8	0	src/refactorings/rename-symbol.ts

--cdcd0269--2019-05-28--Nicolas Carlo
23	46	README.md

--1268f8d6--2019-05-24--Nicolas Carlo
0	13	.vscode/launch.json
12	5	CONTRIBUTING.md
38	0	docs/adr/0002-no-integration-test.md
5	0	jest.config.js
4	2	package.json
5	0	src/index.test.ts
0	21	src/integration-tests/extension.test.ts
0	23	src/integration-tests/index.ts
1622	31	yarn.lock

--45b624d6--2019-05-23--Nicolas Carlo
11	5	tsconfig.json

--a2dc0fae--2019-05-23--Nicolas Carlo
20	0	CONTRIBUTING.md

--f5ea9ad5--2019-05-23--Nicolas Carlo
2	2	.vscode/launch.json
21	0	src/integration-tests/extension.test.ts
23	0	src/integration-tests/index.ts
0	21	src/test/extension.test.ts
0	23	src/test/index.ts

--3dbaa87c--2019-05-23--Nicolas Carlo
1	1	package.json
1	1	vsc-extension-quickstart.md

--224558fa--2019-05-23--Nicolas Carlo
1	0	.adr-dir
106	0	.gitignore
5	0	.vscode/extensions.json
31	0	.vscode/launch.json
11	0	.vscode/settings.json
20	0	.vscode/tasks.json
10	0	.vscodeignore
9	0	CHANGELOG.md
21	0	LICENSE.md
65	0	README.md
19	0	docs/adr/0001-record-architecture-decisions.md
64	0	package.json
29	0	src/extension.ts
21	0	src/test/extension.test.ts
23	0	src/test/index.ts
16	0	tsconfig.json
12	0	tslint.json
41	0	vsc-extension-quickstart.md
3090	0	yarn.lock
