Need to install the following packages:
  libyear
Ok to proceed? (y) ┌─────────┬────────────────────────────────────────────┬───────┬───────┬──────────┬───────┬───────┬───────┬──────────────────────┐
│ (index) │                 dependency                 │ drift │ pulse │ releases │ major │ minor │ patch │      available       │
├─────────┼────────────────────────────────────────────┼───────┼───────┼──────────┼───────┼───────┼───────┼──────────────────────┤
│    0    │               '@babel/core'                │   0   │ 0.06  │    0     │   0   │   0   │   0   │        'N/A'         │
│    1    │              '@babel/parser'               │   0   │ 0.07  │    0     │   0   │   0   │   0   │        'N/A'         │
│    2    │ '@babel/plugin-proposal-class-properties'  │ 0.56  │ 0.19  │    3     │   0   │   1   │   2   │       '7.16.7'       │
│    3    │        '@babel/plugin-syntax-flow'         │ 0.56  │ 0.19  │    3     │   0   │   1   │   2   │       '7.16.7'       │
│    4    │ '@babel/plugin-transform-flow-strip-types' │ 0.56  │ 0.19  │    3     │   0   │   1   │   2   │       '7.16.7'       │
│    5    │            '@babel/preset-env'             │   0   │ 0.14  │    0     │   0   │   0   │   0   │        'N/A'         │
│    6    │            '@babel/preset-flow'            │ 0.56  │ 0.19  │    3     │   0   │   1   │   2   │       '7.16.7'       │
│    7    │         '@babel/preset-typescript'         │   0   │ 0.19  │    0     │   0   │   0   │   0   │        'N/A'         │
│    8    │             '@babel/register'              │ 0.48  │  0.1  │    6     │   0   │   2   │   4   │       '7.17.0'       │
│    9    │             '@babel/traverse'              │   0   │ 0.07  │    0     │   0   │   0   │   0   │        'N/A'         │
│   10    │               '@babel/types'               │   0   │  0.1  │    0     │   0   │   0   │   0   │        'N/A'         │
│   11    │          '@types/babel__traverse'          │   0   │ 0.67  │    0     │   0   │   0   │   0   │        'N/A'         │
│   12    │               '@types/chai'                │   0   │ 0.25  │    0     │   0   │   0   │   0   │        'N/A'         │
│   13    │               '@types/jest'                │   0   │ 0.04  │    0     │   0   │   0   │   0   │        'N/A'         │
│   14    │            '@types/jscodeshift'            │ 0.34  │ 0.31  │    1     │   0   │   0   │   1   │       '0.11.3'       │
│   15    │               '@types/mocha'               │   0   │ 0.14  │    0     │   0   │   0   │   0   │        'N/A'         │
│   16    │               '@types/node'                │   0   │ 0.01  │    0     │   0   │   0   │   0   │        'N/A'         │
│   17    │          '@types/path-browserify'          │   0   │ 0.37  │    0     │   0   │   0   │   0   │        'N/A'         │
│   18    │             '@types/pluralize'             │   0   │ 3.66  │    0     │   0   │   0   │   0   │        'N/A'         │
│   19    │               '@types/sinon'               │   0   │ 0.08  │    0     │   0   │   0   │   0   │        'N/A'         │
│   20    │              '@types/vscode'               │ 0.08  │ 0.02  │    1     │   0   │   1   │   0   │       '1.65.0'       │
│   21    │     '@typescript-eslint/eslint-plugin'     │ 0.02  │   0   │    1     │   0   │   1   │   0   │       '5.14.0'       │
│   22    │        '@typescript-eslint/parser'         │ 0.02  │   0   │    1     │   0   │   1   │   0   │       '5.14.0'       │
│   23    │             '@typescript/vfs'              │   0   │ 0.46  │    0     │   0   │   0   │   0   │        'N/A'         │
│   24    │          '@vscode/test-electron'           │ 0.07  │ 0.02  │    1     │   0   │   0   │   1   │       '2.1.3'        │
│   25    │           'all-contributors-cli'           │   0   │ 1.07  │    0     │   0   │   0   │   0   │        'N/A'         │
│   26    │                  'assert'                  │   0   │ 2.83  │    0     │   0   │   0   │   0   │        'N/A'         │
│   27    │                'babel-jest'                │   0   │ 0.01  │    0     │   0   │   0   │   0   │   '28.0.0-alpha.7'   │
│   28    │                  'buffer'                  │   0   │  1.3  │    0     │   0   │   0   │   0   │        'N/A'         │
│   29    │                   'chai'                   │   0   │ 0.12  │    0     │   0   │   0   │   0   │        'N/A'         │
│   30    │               'change-case'                │   0   │ 1.27  │    0     │   0   │   0   │   0   │        'N/A'         │
│   31    │          'eslint-config-prettier'          │ 0.03  │ 0.02  │    1     │   0   │   1   │   0   │       '8.5.0'        │
│   32    │                  'eslint'                  │   0   │ 0.04  │    0     │   0   │   0   │   0   │        'N/A'         │
│   33    │                   'glob'                   │   0   │ 0.46  │    0     │   0   │   0   │   0   │        'N/A'         │
│   34    │                  'husky'                   │   0   │ 0.39  │    0     │   0   │   0   │   0   │        'N/A'         │
│   35    │                  'hygen'                   │   0   │ 0.02  │    1     │   0   │   1   │   0   │       '6.2.0'        │
│   36    │                   'jest'                   │   0   │ 0.01  │    0     │   0   │   0   │   0   │   '28.0.0-alpha.7'   │
│   37    │               'jscodeshift'                │ 0.54  │ 0.16  │    1     │   0   │   0   │   1   │       '0.13.1'       │
│   38    │               'lint-staged'                │ 0.05  │ 0.02  │    1     │   0   │   0   │   1   │       '12.3.5'       │
│   39    │                'minimatch'                 │   0   │ 0.04  │    0     │   0   │   0   │   0   │        'N/A'         │
│   40    │                  'mocha'                   │   0   │ 0.05  │    0     │   0   │   0   │   0   │        'N/A'         │
│   41    │               'npm-run-all'                │   0   │ 3.29  │    0     │   0   │   0   │   0   │        'N/A'         │
│   42    │                   'ovsx'                   │   0   │ 0.13  │    0     │   0   │   0   │   0   │        'N/A'         │
│   43    │             'path-browserify'              │   0   │ 2.02  │    0     │   0   │   0   │   0   │        'N/A'         │
│   44    │                'pluralize'                 │   0   │  2.8  │    0     │   0   │   0   │   0   │        'N/A'         │
│   45    │                 'prettier'                 │   0   │ 0.27  │    0     │   0   │   0   │   0   │        'N/A'         │
│   46    │               'pretty-quick'               │   0   │ 0.22  │    0     │   0   │   0   │   0   │        'N/A'         │
│   47    │                 'process'                  │   0   │ 4.87  │    0     │   0   │   0   │   0   │        'N/A'         │
│   48    │              'react-codemod'               │   0   │ 1.39  │    0     │   0   │   0   │   0   │        'N/A'         │
│   49    │                  'recast'                  │ 0.36  │ 0.27  │    1     │   0   │   1   │   0   │       '0.21.0'       │
│   50    │                  'sinon'                   │   0   │  0.1  │    0     │   0   │   0   │   0   │        'N/A'         │
│   51    │                'ts-loader'                 │ 0.47  │   0   │    2     │   0   │   0   │   2   │       '9.2.8'        │
│   52    │                 'ts-morph'                 │ 0.34  │ 0.02  │    6     │   2   │   1   │   3   │       '14.0.0'       │
│   53    │                'typescript'                │   0   │   0   │    0     │   0   │   0   │   0   │ '4.7.0-dev.20220311' │
│   54    │                   'util'                   │   0   │ 0.79  │    0     │   0   │   0   │   0   │        'N/A'         │
│   55    │                   'vsce'                   │   0   │  0.1  │    0     │   0   │   0   │   0   │        'N/A'         │
│   56    │               'webpack-cli'                │   0   │ 0.13  │    0     │   0   │   0   │   0   │        'N/A'         │
│   57    │                 'webpack'                  │ 0.04  │ 0.02  │    1     │   0   │   1   │   0   │       '5.70.0'       │
└─────────┴────────────────────────────────────────────┴───────┴───────┴──────────┴───────┴───────┴───────┴──────────────────────┘

# Collective
drift: package is 5.07 libyears behind.
pulse: dependencies are 31.74 libyears behind.
releases: dependencies are 37 releases behind.
major: dependencies are 2 releases behind.
minor: dependencies are 14 releases behind.
patch: dependencies are 21 releases behind.

