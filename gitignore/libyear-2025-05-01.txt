➤ YN0000: · Yarn 4.4.0
➤ YN0000: ┌ Resolution step
➤ YN0085: │ + libyear@npm:0.8.0, @babel/code-frame@npm:7.27.1, @babel/helper-validator-identifier@npm:7.27.1, @babel/runtime@npm:7.27.1, @types/parse-json@npm:4.0.2, and 36 more.
➤ YN0000: └ Completed in 0s 563ms
➤ YN0000: ┌ Fetch step
➤ YN0013: │ 41 packages were added to the project (+ 9.84 MiB).
➤ YN0000: └ Completed
➤ YN0000: ┌ Link step
➤ YN0000: └ Completed in 0s 965ms
➤ YN0000: · Done in 1s 570ms

┌─────────┬────────────────────────────────────┬───────┬───────┬──────────┬───────┬───────┬───────┬──────────────────────┐
│ (index) │ dependency                         │ drift │ pulse │ releases │ major │ minor │ patch │ available            │
├─────────┼────────────────────────────────────┼───────┼───────┼──────────┼───────┼───────┼───────┼──────────────────────┤
│ 0       │ '@babel/core'                      │ 0.13  │ 0     │ 1        │ 0     │ 1     │ 0     │ '7.27.1'             │
│ 1       │ '@babel/parser'                    │ 0     │ 0     │ 0        │ 0     │ 0     │ 0     │ '8.0.0-alpha.17'     │
│ 2       │ '@babel/preset-env'                │ 0     │ 0     │ 0        │ 0     │ 0     │ 0     │ '8.0.0-alpha.17'     │
│ 3       │ '@babel/preset-typescript'         │ 0.51  │ 0     │ 2        │ 0     │ 1     │ 1     │ '7.27.1'             │
│ 4       │ '@babel/traverse'                  │ 0.67  │ 0     │ 11       │ 0     │ 2     │ 9     │ '7.27.1'             │
│ 5       │ '@babel/types'                     │ 2.35  │ 0     │ 42       │ 0     │ 7     │ 35    │ '7.27.1'             │
│ 6       │ '@eslint/eslintrc'                 │ 0.08  │ 0.11  │ 1        │ 0     │ 0     │ 1     │ '3.3.1'              │
│ 7       │ '@eslint/js'                       │ -0.65 │ 0.03  │ 4        │ 1     │ 2     │ 1     │ '10.0.0'             │
│ 8       │ '@types/babel__traverse'           │ 0.84  │ 0.1   │ 1        │ 0     │ 0     │ 1     │ '7.20.7'             │
│ 9       │ '@types/chai'                      │ 0     │ 0.1   │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 10      │ '@types/glob'                      │ 0     │ 2.18  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 11      │ '@types/jest'                      │ 0     │ 0.52  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 12      │ '@types/jsdom'                     │ 0     │ 0.92  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 13      │ '@types/mocha'                     │ 0.41  │ 0.44  │ 3        │ 0     │ 0     │ 3     │ '10.0.10'            │
│ 14      │ '@types/node'                      │ 0.07  │ 0.01  │ 7        │ 0     │ 2     │ 5     │ '22.15.3'            │
│ 15      │ '@types/pluralize'                 │ 0     │ 1.48  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 16      │ '@types/sinon'                     │ 1.26  │ 0.19  │ 2        │ 0     │ 0     │ 2     │ '17.0.4'             │
│ 17      │ '@types/vscode'                    │ 0     │ 0.06  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 18      │ '@vscode/test-electron'            │ 0.76  │ 0.06  │ 2        │ 0     │ 1     │ 1     │ '2.5.2'              │
│ 19      │ 'all-contributors-cli'             │ 0     │ 1.82  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 20      │ 'babel-jest'                       │ 0     │ 0.25  │ 0        │ 0     │ 0     │ 0     │ '30.0.0-alpha.7'     │
│ 21      │ 'chai'                             │ 0     │ 0.21  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 22      │ 'esbuild'                          │ 0.06  │ 0.02  │ 1        │ 0     │ 0     │ 1     │ '0.25.3'             │
│ 23      │ 'eslint'                           │ 0.35  │ 0.03  │ 10       │ 0     │ 8     │ 2     │ '9.25.1'             │
│ 24      │ 'glob'                             │ 0     │ 0.02  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 25      │ 'globals'                          │ 0.17  │ 0.19  │ 2        │ 1     │ 1     │ 0     │ '16.0.0'             │
│ 26      │ 'husky'                            │ 0     │ 0.45  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 27      │ 'hygen'                            │ 0     │ 2.65  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 28      │ 'jest-environment-jsdom'           │ 0     │ 0.25  │ 0        │ 0     │ 0     │ 0     │ '30.0.0-alpha.7'     │
│ 29      │ 'jest-html-loader'                 │ 0     │ 2.3   │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 30      │ 'jest'                             │ 0     │ 0.25  │ 0        │ 0     │ 0     │ 0     │ '30.0.0-alpha.7'     │
│ 31      │ 'jsdom'                            │ 0.56  │ 0.03  │ 2        │ 1     │ 1     │ 0     │ '26.1.0'             │
│ 32      │ 'lint-staged'                      │ 0     │ 0.05  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 33      │ 'minimatch'                        │ 0     │ 0.81  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 34      │ 'mocha'                            │ 0.72  │ 0.06  │ 11       │ 1     │ 3     │ 7     │ '11.2.2'             │
│ 35      │ 'node-stdlib-browser'              │ 0.2   │ 0.24  │ 1        │ 0     │ 0     │ 1     │ '1.3.1'              │
│ 36      │ 'ovsx'                             │ 0.4   │ 0.05  │ 1        │ 0     │ 0     │ 1     │ '0.10.2'             │
│ 37      │ 'pluralize'                        │ 0     │ 5.94  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 38      │ 'prettier-plugin-organize-imports' │ 0     │ 0.61  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 39      │ 'prettier'                         │ 0     │ 0.15  │ 0        │ 0     │ 0     │ 0     │ '4.0.0-alpha.12'     │
│ 40      │ 'pretty-quick'                     │ 0     │ 0.16  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 41      │ 'recast'                           │ 0.75  │ 0.16  │ 2        │ 0     │ 0     │ 2     │ '0.23.11'            │
│ 42      │ 'sinon'                            │ 0.53  │ 0.1   │ 4        │ 1     │ 0     │ 3     │ '20.0.0'             │
│ 43      │ 'ts-pattern'                       │ 0     │ 0.09  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 44      │ 'tsup'                             │ 0     │ 0.18  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
│ 45      │ 'typescript-eslint'                │ 0     │ 0     │ 0        │ 0     │ 0     │ 0     │ '8.31.2-alpha.4'     │
│ 46      │ 'typescript'                       │ 0     │ 0     │ 0        │ 0     │ 0     │ 0     │ '5.9.0-dev.20250501' │
│ 47      │ 'vsce'                             │ 0     │ 2.41  │ 0        │ 0     │ 0     │ 0     │ 'N/A'                │
└─────────┴────────────────────────────────────┴───────┴───────┴──────────┴───────┴───────┴───────┴──────────────────────┘

# Collective
drift: package is 10.17 libyears behind.
pulse: dependencies are 25.69 libyears behind.
releases: dependencies are 110 releases behind.
major: dependencies are 5 releases behind.
minor: dependencies are 29 releases behind.
patch: dependencies are 76 releases behind.

