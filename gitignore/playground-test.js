// import "./styles.css";
// import { useState, useEffect } from "react";
// import assert from "assert";

// export default function App() {
//   const [secondsWhileBroken, setSecondsWhileBroken] = useState(0);
//   const [brokenTests, setBrokenTests] = useState([]);

//   useEffect(() => {
//     const interval = setInterval(() => {
//       const output = require("./playground");
//       const tests = [
//         {
//           label: "should set spaceship owner",
//           run: () => {
//             const { spaceship } = output();
//             assert(spaceship.owner.firstName === "Martin");
//             assert(spaceship.owner.lastName === "Fowler");
//           }
//         },
//         {
//           label: "should update default owner",
//           run: () => {
//             const { defaultOwner } = output();
//             assert(defaultOwner.firstName === "Rebecca");
//             assert(defaultOwner.lastName === "Parsons");
//           }
//         }
//       ];

//       const result = tests.reduce((memo, test) => {
//         const { label, run } = test;
//         try {
//           run();
//           return memo;
//         } catch (error) {
//           return memo.concat([{ label }]);
//         }
//       }, []);

//       if (result.length !== 0) {
//         setSecondsWhileBroken(secondsWhileBroken + 1);
//       }
//       setBrokenTests(result);
//     }, 1_000);
//     return () => clearInterval(interval);
//   }, [secondsWhileBroken]);

//   const state = brokenTests.length > 0 ? "Broken" : "Working";

//   return (
//     <div className="App">
//       <h1>Refactoring Playground</h1>
//       <h2>Current state: {state}</h2>
//       {brokenTests.length > 0 && (
//         <h3>
//           Tests broken:{" "}
//           <ul>
//             {brokenTests.map(({ label }) => (
//               <li>{label}</li>
//             ))}
//           </ul>
//         </h3>
//       )}
//       <h3>Seconds while broken: {secondsWhileBroken}</h3>
//     </div>
//   );
// }
