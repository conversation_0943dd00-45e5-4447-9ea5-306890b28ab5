entity,soc
src/extension.ts,954
src/action-providers.ts,462
src/commands.ts,455
src/refactorings/flip-if-else/flip-if-else.ts,333
src/refactorings/convert-to-template-literal/convert-to-template-literal.ts,333
src/editor/adapters/vscode-editor.ts,330
src/refactorings/convert-to-template-literal/index.ts,329
src/refactorings/merge-if-statements/index.ts,328
src/refactorings/replace-binary-with-assignment/index.ts,327
src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.ts,327
src/editor/adapters/in-memory-editor.ts,327
src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.ts,318
src/refactorings/split-if-statement/index.ts,316
src/refactorings/split-declaration-and-initialization/index.ts,314
src/refactorings/remove-redundant-else/index.ts,314
src/refactorings/merge-with-previous-if-statement/index.ts,314
src/refactorings/flip-ternary/index.ts,314
src/refactorings/flip-if-else/index.ts,314
src/refactorings/convert-ternary-to-if-else/index.ts,314
src/refactorings/convert-if-else-to-ternary/index.ts,314
src/refactorings/convert-if-else-to-switch/index.ts,314
src/editor/error-reason.ts,312
src/editor/editor.ts,287
src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.ts,286
src/refactorings/flip-ternary/flip-ternary.ts,282
src/refactorings/remove-redundant-else/remove-redundant-else.ts,281
src/refactorings/merge-if-statements/merge-if-statements.ts,281
src/ast/transformation.ts,274
src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.ts,273
src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.ts,272
src/refactorings/remove-dead-code/index.ts,272
src/refactorings/negate-expression/index.ts,267
src/refactorings/extract-interface/extract-interface.ts,266
src/refactorings/extract/extract-variable/occurrence.ts,265
src/refactorings/extract/extract-variable/extract-variable.ts,260
src/refactorings/convert-to-template-literal/convert-to-template-literal.test.ts,260
src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.ts,258
src/refactorings/negate-expression/negate-expression.ts,253
src/refactorings/inline/inline-variable/find-inlinable-code.ts,251
src/refactorings/split-if-statement/split-if-statement.ts,244
src/ast/scope.ts,242
src/refactorings/remove-braces-from-arrow-function/index.ts,238
src/refactorings/add-braces-to-arrow-function/index.ts,238
src/ast/identity.ts,235
src/refactorings/bubble-up-if-statement/index.ts,231
src/refactorings/convert-for-to-foreach/index.ts,230
README.md,230
src/refactorings/extract-variable.ts,229
src/refactorings/remove-dead-code/remove-dead-code.ts,225
src/editor/adapters/attempting-editor.ts,224
src/refactorings/extract-variable.test.ts,223
src/ast/domain.ts,222
src/editor/selection.ts,214
src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.ts,200
src/refactorings/extract-generic-type/extract-generic-type.ts,199
src/refactorings/convert-for-to-foreach/convert-for-to-foreach.ts,197
src/refactorings/ast.ts,197
src/editor/position.ts,196
src/refactorings/extract-variable/extract-variable.ts,195
src/refactorings/move-to-existing-file/move-to-existing-file.ts,190
src/refactorings/inline/inline-function/inline-function.ts,189
src/refactorings/react/convert-to-pure-component/convert-to-pure-component.ts,188
src/refactorings/bubble-up-if-statement/bubble-up-if-statement.ts,187
src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.ts,182
src/refactorings/negate-expression.ts,182
src/refactorings/extract-interface/index.ts,179
src/refactorings/simplify-ternary/simplify-ternary.ts,177
src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.ts,175
src/refactorings/inline/find-exported-id-names.ts,175
src/refactorings/inline-variable-or-function/index.ts,175
src/refactorings/merge-if-statements/merge-if-statements.test.ts,174
src/refactorings/inline/inline-variable/inline-variable.ts,174
src/refactorings/remove-redundant-else.test.ts,173
src/refactorings/move-statement-down/move-statement-down.ts,172
src/refactorings/remove-redundant-else.ts,170
src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.ts,167
src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.ts,166
src/refactorings/inline-variable.ts,166
src/editor/editor-contract-test.ts,164
src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.ts,163
src/refactorings/move-statement-up/move-statement-up.ts,162
src/refactorings/inline/inline-function/find-param-matching-id.ts,157
src/refactorings/extract/extract-variable/variable.ts,153
src/refactorings/inline-variable.test.ts,152
src/refactorings/inline-variable-or-function/inline-variable.ts,148
src/refactorings/flip-if-else.ts,148
src/types.ts,146
src/refactorings/simplify-ternary/index.ts,144
src/refactorings/negate-expression.test.ts,144
src/refactorings/negate-expression/negate-expression.test.ts,142
src/refactorings/extract-class/extract-class.ts,142
src/refactorings/convert-if-else-to-ternary.test.ts,141
src/refactorings/flip-if-else.test.ts,140
src/refactorings/convert-to-arrow-function/convert-to-arrow-function.ts,140
src/refactorings/split-multiple-declarations/split-multiple-declarations.ts,138
src/refactorings/flip-ternary.test.ts,138
src/refactorings/convert-if-else-to-ternary.ts,138
src/refactorings/toggle-braces/toggle-braces.ts,137
src/ast/selection.ts,137
src/refactorings/rename-symbol/rename-symbol.ts,136
src/refactorings/destructure-object/destructure-object.ts,136
src/refactorings/convert-switch-to-if-else/index.ts,136
src/refactorings/convert-for-to-for-each/convert-for-to-for-each.ts,135
src/refactorings/convert-for-to-for-each/convert-for-to-for-each.test.ts,135
src/refactorings/inline-variable-or-function/inline-function.ts,134
src/refactorings/move-statement-up/index.ts,133
src/refactorings/move-statement-down/index.ts,133
src/refactorings/rename-symbol/index.ts,132
src/refactorings/flip-ternary.ts,132
REFACTORINGS.md,131
CONTRIBUTING.md,129
src/refactorings/move-statement-up.ts,128
src/refactorings/move-statement-down.ts,128
src/refactorings/move-statement-up.test.ts,127
src/tests-helpers.ts,126
src/refactorings/move-statement-down.test.ts,125
src/refactorings/convert-let-to-const/convert-let-to-const.ts,124
src/refactorings/extract/extract-type/extract-type.ts,123
src/refactorings/extract/replacement-strategy.ts,120
src/ast/switch.ts,120
src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.ts,119
src/refactorings/inline-variable-or-function/inline-function.test.ts,117
_templates/refactoring/new/refactoring.ejs.t,117
src/editor/path.ts,115
src/refactorings/invert-boolean-logic/invert-boolean-logic.ts,111
src/refactorings/invert-boolean-logic/invert-boolean-logic.test.ts,111
src/refactorings/invert-boolean-logic/index.ts,111
src/refactorings/inline-variable-or-function/inline-variable.test.ts,111
src/refactorings/editor/selection.ts,111
src/refactorings/rename-symbol.ts,109
src/refactorings/rename-symbol.test.ts,109
src/refactorings/lift-up-conditional/lift-up-conditional.ts,109
src/refactorings/extract/extract-variable/variable-declaration-modification.ts,109
src/refactorings/editor/i-write-code.ts,109
src/refactorings/editor/i-write-code-contract-test.ts,109
src/vscode-configuration.ts,108
src/refactorings/lift-up-conditional/index.ts,107
src/refactorings/create-factory-for-constructor/create-factory-for-constructor.ts,107
src/refactorings/react/add-braces-to-jsx-attribute/index.ts,106
src/refactorings/convert-for-to-for-each/index.ts,106
src/refactorings/react/remove-braces-from-jsx-attribute/index.ts,105
src/editor/adapters/create-vscode-editor.ts,105
src/refactorings/add-braces-to-if-statement/index.ts,104
src/refactorings/adapters/write-code-in-vscode.ts,104
src/refactorings/add-numeric-separator/add-numeric-separator.ts,103
src/editor/adapters/vue-vscode-editor.ts,103
src/refactorings/convert-ternary-to-if-else.test.ts,102
src/ast/export-default-workaround.ts,102
src/array.ts,102
src/refactorings/split-multiple-declarations/index.ts,101
src/ast/siblings.ts,101
src/refactorings/extract/extract-variable/parts.ts,99
src/refactorings/extract-interface/extract-interface.test.ts,99
src/ast/template-literal.ts,99
src/refactorings/extract-variable/index.ts,98
src/refactorings/convert-ternary-to-if-else/convert-ternary-to-if-else.test.ts,97
src/refactoring-command.ts,97
src/refactorings/editor/i-show-error-message.ts,96
src/refactorings/convert-ternary-to-if-else.ts,96
src/refactorings/convert-if-else-to-ternary/convert-if-else-to-ternary.test.ts,96
src/refactorings/convert-for-to-foreach/convert-for-to-foreach.test.ts,96
src/refactorings/adapters/write-code-in-memory.ts,95
src/refactorings/adapters/show-error-message-in-vscode.ts,94
src/refactorings/editor/position.ts,93
src/refactorings/split-declaration-and-initialization/split-declaration-and-initialization.test.ts,92
src/refactorings/extract/extract-generic-type/extract-generic-type.ts,90
src/refactorings/extract-variable/extract-variable.basic-extraction.test.ts,90
src/refactorings/convert-if-else-to-switch/convert-if-else-to-switch.test.ts,90
src/refactorings/add-braces-to-if-statement/add-braces-to-if-statement.test.ts,90
src/refactorings/editor/selection.test.ts,89
src/refactorings/editor/position.test.ts,89
src/refactorings/editor/i-delegate-to-editor.ts,89
src/refactorings/bubble-up-if-statement/bubble-up-if-statement.test.ts,89
src/refactorings/flip-if-else/flip-if-else.test.ts,87
src/refactorings/react/add-braces-to-jsx-attribute/add-braces-to-jsx-attribute.test.ts,86
src/refactorings/extract/index.ts,86
src/refactorings/selection.ts,85
src/refactorings/react/remove-braces-from-jsx-attribute/remove-braces-from-jsx-attribute.test.ts,84
src/refactorings/add-braces-to-arrow-function/add-braces-to-arrow-function.test.ts,84
src/editor/i-show-error-message.ts,84
src/refactorings/remove-braces-from-arrow-function/remove-braces-from-arrow-function.test.ts,83
src/refactorings/react/convert-to-pure-component/convert-to-pure-component.test.ts,83
src/refactorings/inline/index.ts,82
src/refactorings/inline-variable-or-function/find-inlinable-code.ts,81
src/refactorings/remove-redundant-else/remove-redundant-else.test.ts,79
src/refactorings/remove-dead-code/remove-dead-code.test.ts,79
src/refactorings/extract/extract-variable/extract-variable.extractable-objects.test.ts,78
tsconfig.json,76
src/refactorings/split-if-statement/split-if-statement.test.ts,76
src/refactorings/i-show-error-message.ts,76
src/refactorings/flip-ternary/flip-ternary.test.ts,75
src/refactorings/adapters/write-code-in-memory.test.ts,75
src/refactorings/negate-expression/command.ts,74
src/refactorings/extract-variable/command.ts,73
src/refactorings/change-signature/change-signature.ts,73
src/refactorings/remove-redundant-else/command.ts,72
src/refactorings/merge-with-previous-if-statement/merge-with-previous-if-statement.test.ts,72
src/refactorings/flip-ternary/command.ts,72
src/refactorings/flip-if-else/command.ts,72
src/refactorings/convert-ternary-to-if-else/command.ts,72
src/refactorings/convert-if-else-to-ternary/command.ts,72
src/refactorings/extract/extract-variable/extract-variable.extractable.test.ts,71
src/refactorings/move-statement-up/command.ts,70
src/refactorings/move-statement-down/command.ts,70
src/refactorings/extract/extract-variable/extract-variable.multiple-occurrences.test.ts,70
src/refactorings/editor/adapters/write-code-in-vscode.ts,69
src/refactorings/editor/adapters/write-code-in-memory.ts,69
src/refactorings/editor/adapters/write-code-in-memory.test.ts,69
src/refactorings/editor/adapters/show-error-message-in-vscode.ts,69
src/refactorings/editor/adapters/delegate-to-vscode.ts,69
src/refactorings/adapters/delegate-to-vscode.ts,68
src/refactorings/split-if-statement/command.ts,67
src/refactorings/split-declaration-and-initialization/command.ts,67
src/refactorings/move-to-existing-file/move-to-existing-file.test.ts,67
src/refactorings/merge-if-statements/command.ts,67
src/refactorings/convert-to-template-literal/command.ts,67
src/refactorings/remove-braces-from-arrow-function/command.ts,66
src/refactorings/inline-variable-or-function/command.ts,66
src/refactorings/add-braces-to-arrow-function/command.ts,66
_templates/refactoring/new/index.js,66
src/refactorings/simplify-ternary/simplify-ternary.test.ts,65
src/refactorings/replace-binary-with-assignment/replace-binary-with-assignment.test.ts,65
src/editor/i-write-code-contract-test.ts,65
src/editor/adapters/write-code-in-memory.ts,65
src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.ts,64
src/refactorings/remove-braces-from-if-statement/index.ts,63
src/refactorings/extract-class/extract-class-command.ts,63
_templates/refactoring/new/error-reason-switch.ejs.t,63
src/refactorings/extract-variable/extract-variable.extractable.test.ts,62
src/refactorings/position.ts,61
src/refactorings/inline/inline-variable/inline-variable.test.ts,61
src/editor/i-write-code.ts,60
src/editor/i-delegate-to-editor.ts,60
src/editor/adapters/write-code-in-vscode.ts,60
src/editor/adapters/show-error-message-in-vscode.ts,60
src/editor/adapters/delegate-to-vscode.ts,60
src/refactorings/rename-symbol/command.ts,59
src/editor/adapters/write-code-in-memory.test.ts,59
src/refactorings/i-write-code.ts,57
src/refactorings/i-write-code-contract-test.ts,57
src/editor/selection.test.ts,57
src/refactorings/extract-class/ClassRefactor.test.ts,56
src/refactorings/react/convert-to-pure-component/index.ts,55
.vscode/launch.json,55
src/refactorings/extract/extract-generic-type/extract-generic-type.test.ts,54
src/refactorings/extract-class/UmlNotation.ts,54
src/refactorings/extract-class/UmlNotation.test.ts,54
src/refactorings/extract-class/ClassNode.ts,54
src/refactorings/refactoring.ts,53
src/refactorings/extract-class/ExtractClassActionProvider.ts,53
src/refactorings/extract-class/extract-class-action-provider.ts,52
src/editor/position.test.ts,52
src/refactorings/extract-class/class-refactor.ts,51
src/refactorings/extract-class/class-refactor.test.ts,51
src/refactorings/extract-class/TypescriptClassNode.ts,51
src/refactorings/replace-binary-with-assignment/command.ts,50
src/refactorings/remove-dead-code/command.ts,50
src/refactorings/merge-with-previous-if-statement/command.ts,50
src/refactorings/inline-variable-or-function/find-exported-id-names.ts,50
src/refactorings/i-delegate-to-editor.ts,50
src/refactorings/extract-generic-type/extract-generic-type.test.ts,50
src/refactorings/extract-class/parseClassDeclaration.ts,50
src/refactorings/extract-class/formatTs.ts,50
src/refactorings/extract-class/classNameMatcher.ts,50
src/refactorings/extract-class/TextMatcher.ts,50
src/refactorings/extract-class/TextMatcher.test.ts,50
src/refactorings/extract-class/ExtractClassCommand.ts,50
src/refactorings/extract-class/ClassRefactor.ts,50
src/refactorings/convert-if-else-to-switch/command.ts,50
src/refactorings/convert-for-to-foreach/command.ts,50
src/refactorings/bubble-up-if-statement/command.ts,50
src/refactorings/extract-class/uml-notation.ts,49
src/refactorings/extract-class/uml-notation.test.ts,49
src/refactorings/extract-class/typescript-class-node.ts,49
src/refactorings/extract-class/typescript-class-node.test.ts,49
src/refactorings/extract-class/text-matcher.ts,49
src/refactorings/extract-class/text-matcher.test.ts,49
src/refactorings/extract-class/parse-class-declaration.ts,49
src/refactorings/extract-class/format-ts.ts,49
src/refactorings/extract-class/class-node.ts,49
src/refactorings/extract-class/class-name-matcher.ts,49
src/refactorings/move-statement-up/move-statement-up.test.ts,48
src/refactorings/convert-function-declaration-to-arrow-function/index.ts,48
src/refactorings/extract-variable/extract-variable.multiple-occurrences.test.ts,47
src/refactorings/convert-let-to-const/index.ts,47
src/refactorings/change-signature/change-signature.test.ts,47
src/refactorings/move-statement-down/move-statement-down.test.ts,46
src/refactorings/extract-class/index.ts,46
src/refactorings/extract-class/EXTRACT_CLASS_COMMAND.ts,46
src/refactorings/adapters/selection-from-vscode.ts,45
src/refactorings/extract-class/extract-class.test.ts,44
src/refactoring.ts,44
src/highlights/toggle-highlight/toggle-highlight.test.ts,44
src/refactorings/inline-variable-or-function/find-param-matching-id.ts,43
src/refactorings/extract/extract-variable/extract-variable.non-extractable.test.ts,43
src/refactorings/selection.test.ts,42
src/refactorings/inline-variable-or-function/find-inlinable-code.test.ts,42
src/refactorings/inline-function/inline-function.test.ts,41
src/refactorings/position.test.ts,40
src/refactorings/i-write-updates.ts,40
src/refactorings/extract/extract-variable/extract-variable.basic-extraction.test.ts,40
src/type-checker/type-checker.ts,39
src/refactorings/extract/extract-variable/extract-variable.variable-name.test.ts,38
src/ast/switch.test.ts,37
src/refactorings/inline/inline-variable/find-inlinable-code.test.ts,36
src/refactorings/inline/inline-function/inline-function.test.ts,36
src/refactorings/inline-variable-or-function/inline-variable.object-pattern.test.ts,36
src/refactorings/extract/extract-type/extract-type.test.ts,36
src/refactorings/convert-to-arrow-function/convert-to-arrow-function.test.ts,36
src/refactorings/inline-function/inline-function.ts,35
src/refactorings/extract/extract-generic-type/extract-generic-type.interface.test.ts,35
src/refactorings/extract/extract-generic-type/extract-generic-type.function.test.ts,35
src/playground/playground.ts,35
src/playground/maths.ts,35
.vscode/settings.json,35
src/ast/index.ts,33
src/refactorings/inline/inline-variable/inline-variable.object-pattern.test.ts,32
src/refactorings/extract-variable/extract-variable.test.ts,32
src/refactorings/i-update-code.ts,31
src/refactorings/extract-class/utils/assert.ts,31
src/refactorings/adapters/write-updates-to-vscode.ts,31
src/refactorings/adapters/update-code-in-vscode.ts,31
src/playground/trivia-kata.ts,31
src/playground/gilded-rose.ts,31
src/refactorings/inline/inline-variable/inline-variable.array-pattern.test.ts,30
src/refactorings/extract-variable/extract-variable.variable-name.test.ts,30
src/refactorings/convert-switch-to-if-else/convert-switch-to-if-else.test.ts,30
src/refactorings/remove-braces-from-if-statement/remove-braces-from-if-statement.test.ts,29
src/refactorings/extract-variable/extract-variable.non-extractable.test.ts,29
src/refactorings/extract-class/TypescriptClassNode.test.ts,29
src/ast/scope.test.ts,29
.gitignore,29
src/refactorings/inline-variable/inline-variable.ts,28
src/playground/fileOne.ts,28
src/playground/calculator.ts,28
src/refactorings/negate-expression/action-provider.ts,27
src/refactorings/inline-variable/inline-variable.test.ts,27
src/ast.ts,27
_templates/refactoring/new/action-provider.ejs.t,27
src/refactorings/inline-variable-or-function/inline-variable.array-pattern.test.ts,26
src/refactorings/extract-variable/extract-variable.extractable-objects.test.ts,26
src/refactorings/destructure-object/type-checker.ts,26
src/refactorings/convert-ternary-to-if-else/action-provider.ts,26
src/refactorings/remove-redundant-else/action-provider.ts,25
src/refactorings/flip-ternary/action-provider.ts,25
src/refactorings/flip-if-else/action-provider.ts,25
src/refactorings/extract.ts,25
src/refactorings/convert-if-else-to-ternary/action-provider.ts,25
src/highlight/toggle-highlight.ts,25
src/editor/attempting-editor.ts,25
.vscode/tasks.json,25
src/type-checker/index.ts,24
src/refactorings/toggle-braces/toggle-braces.test.ts,24
src/refactorings/move-to-existing-file/movable-node.ts,24
src/refactorings/inline-function/find-param-matching-id.ts,24
src/refactorings/extract/extract-variable/extract-variable.extractable-template-literals.test.ts,24
src/refactorings/extract-variable/variable.ts,24
src/refactorings/extract-variable/occurrence.ts,24
src/refactorings/destructure-object/destructure-object.test.ts,24
src/refactorings/convert-for-each-to-for-of/convert-for-each-to-for-of.test.ts,24
src/refactorings/split-multiple-declarations/split-multiple-declarations.test.ts,23
src/refactorings/inline-variable/command.ts,23
docs/demo/extract-class.gif,23
.vscodeignore,23
webpack.config.js,22
tslint.json,22
src/test/index.ts,22
src/test/extension.test.ts,22
src/refactorings/split-if-statement/action-provider.ts,22
src/refactorings/react/extract-use-callback/extract-use-callback.test.ts,22
src/refactorings/extract/extract-variable/extract-variable.extractable-string-literals.test.ts,22
src/refactorings/destructure-object/type-checker.test.ts,22
src/highlights/highlights-repository.ts,22
src/refactorings/react/extract-use-callback/extract-use-callback.ts,21
src/refactorings/merge-if-statements/action-provider.ts,21
src/refactorings/i-update-code-contract-test.ts,21
src/refactorings/convert-to-template-literal/action-provider.ts,21
src/refactorings/adapters/update-code-in-memory.ts,21
src/refactorings/adapters/update-code-in-memory.test.ts,21
src/playground/fileTwo.ts,21
src/editor/source-change.ts,21
jest.config.js,21
src/type-checker/ts-position.ts,20
src/type-checker/ts-position.test.ts,20
src/type-checker/logger.ts,20
src/type-checker/adapters/noop-logger.ts,20
src/type-checker/adapters/console-logger.ts,20
src/test/custom-matchers.ts,20
src/refactorings/split-declaration-and-initialization/action-provider.ts,20
src/refactorings/extract/extract-variable/extract-variable.extractable-jsx.test.ts,20
src/refactorings/destructure-object/ts-position.ts,20
src/refactorings/destructure-object/ts-position.test.ts,20
src/refactorings/convert-let-to-const/convert-let-to-const.test.ts,20
vsc-extension-quickstart.md,19
src/type-checker/type-checker.test.ts,19
src/refactorings/remove-braces-from-arrow-function/action-provider.ts,19
src/refactorings/inline-variable/find-exported-id-names.ts,19
src/refactorings/destructure-object/logger.ts,19
src/refactorings/destructure-object/adapters/noop-logger.ts,19
src/refactorings/destructure-object/adapters/console-logger.ts,19
src/playground/maths/calculator.ts,19
docs/demo/negate-expression.gif,19
docs/demo/negate-expression-partial.gif,19
docs/demo/convert-to-template-literal.gif,19
src/refactorings/rename-symbol/rename-symbol.test.ts,18
src/refactorings/inline-function/command.ts,18
src/refactorings/add-braces-to-arrow-function/action-provider.ts,18
src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.test.ts,18
docs/adr/0001-record-architecture-decisions.md,18
LICENSE.md,18
.vscode/extensions.json,18
.github/workflows/vscode-deploy.yml,18
.adr-dir,18
src/editor/path.test.ts,17
docs/demo/bubble-up-if-statement.gif,17
src/refactorings/flip-yoda-condition/flip-yoda-condition.ts,16
src/refactorings/flip-yoda-condition/flip-yoda-condition.test.ts,16
src/refactorings/flip-operator/flip-operator.test.ts,16
src/highlights/toggle-highlight/toggle-highlight.ts,16
src/editor/adapters/change-signature-webview/change-signature.html,16
docs/demo/invert-boolean-logic.gif,16
docs/demo/invert-boolean-logic-partial.gif,16
docs/adr/0008-don-t-propose-quick-fix-for-react-convert-to-pure-component.md,16
.github/workflows/ovsx-deploy.yml,16
src/refactorings/flip-yoda-condition/index.ts,15
src/refactorings/convert-for-each-to-for-of/index.ts,15
src/refactorings/change-signature/index.ts,15
src/refactorings/adapters/writable-vscode.ts,15
src/refactorings/move-to-existing-file/export-declaration.ts,14
src/refactorings/lift-up-conditional/lift-up-conditional.test.ts,14
src/highlight/toggle-highlight.test.ts,14
_templates/refactoring/new/test.ejs.t,14
src/refactorings/move-to-existing-file/index.ts,13
src/refactorings/extract-generic-type/index.ts,13
src/highlights/highlights.ts,13
src/editor/adapters/in-memory-editor.test.ts,13
src/array-helpers.ts,13
docs/demo/convert-for-to-foreach.gif,13
src/refactorings/extract/extract-variable/destructure-strategy.ts,12
src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.ts,12
src/refactorings/convert-function-declaration-to-arrow-function/convert-function-declaration-to-arrow-function.test.ts,12
src/playground/trivia-kata.js,12
src/playground/playground.vue,12
src/playground/playground.tsx,12
src/playground/playground.jsx,12
src/playground/playground.js,12
src/playground/gilded-rose.js,12
src/playground/Map.vue,12
src/playground/Get.tsx,12
src/integration-tests/index.ts,12
src/integration-tests/extension.test.ts,12
src/editor/colors.ts,12
docs/demo/lift-up-conditional.gif,12
docs/adr/0004-use-recast-for-ast-manipulation.md,12
src/ts-server-plugin.ts,11
src/refactorings/editor/i-put-cursor-at.ts,11
src/refactorings/destructure-object/index.ts,11
src/refactorings/adapters/put-cursor-at-in-vscode.ts,11
src/editor/highlights.ts,11
src/ast/transformation.test.ts,11
docs/demo/move-to-existing-file.gif,11
docs/demo/convert-for-to-for-each.gif,11
src/refactorings/turn-into-getter/turn-into-getter.ts,10
src/refactorings/turn-into-getter/turn-into-getter.test.ts,10
src/refactorings/react/extract-use-callback/index.ts,10
src/refactorings/flip-operator/flip-operator.ts,10
src/highlights/refresh-highlights/refresh-highlights.ts,10
src/highlights/refresh-highlights/refresh-highlights.test.ts,10
src/highlights/refresh-highlights/index.ts,10
src/editor/adapters/change-signature-webview/createChangeSignatureWebviewTemplate.ts,10
src/ast/if.ts,10
esbuild.config.js,10
docs/demo/destructure-object.gif,10
_templates/refactoring/new/command.ejs.t,10
src/refactorings/flip-operator/index.ts,9
src/refactorings/extract-generic-type/extract-generic-type.function.test.ts,9
src/highlight/index.ts,9
src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.ts,9
src/editor/adapters/change-signature-webview/getParamsPositionWebViewContent.test.ts,9
docs/demo/extract-variable.gif,9
docs/demo/add-braces-to-if-statement.gif,9
docs/demo/add-braces-to-arrow-function.gif,9
docs/adr/0002-no-integration-test.md,9
src/refactorings/convert-for-to-foreach/action-provider.ts,8
src/refactorings/add-numeric-separator/index.ts,8
src/refactorings/add-numeric-separator/add-numeric-separator.test.ts,8
src/playground/maths/maths.ts,8
src/index.test.ts,8
src/editor/attempting-editor.test.ts,8
docs/demo/remove-braces-from-jsx-attribute.gif,8
docs/demo/remove-braces-from-if-statement.gif,8
docs/demo/remove-braces-from-arrow-function.gif,8
docs/demo/extract-variable-with-lightbulb.gif,8
docs/demo/extract-use-callback.gif,8
docs/demo/convert-to-pure-component.gif,8
docs/demo/add-numeric-separator.gif,8
docs/demo/add-braces-to-jsx-attribute.gif,8
_templates/refactoring/new/prompt.js,8
src/ts-server-plugin/index.ts,7
src/ts-server-plugin/index.js,7
src/replacement-strategy.ts,7
src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.ts,7
src/refactorings/replace-binary-with-assignment/action-provider.ts,7
src/refactorings/extract-generic-type/extract-generic-type.interface.test.ts,7
src/refactorings/create-factory-for-constructor/create-factory-for-constructor.test.ts,7
src/refactorings/change-signature/webView.html,7
src/editor/adapters/attempting-editor.test.ts,7
src/ast/discriminant.ts,7
src/ast/discriminant.test.ts,7
docs/demo/toggle-braces.gif,7
docs/demo/extract-variable-with-shortcut.gif,7
src/test/run-contract-tests.ts,6
src/refactorings/wrap-in-jsx-fragment/wrap-in-jsx-fragment.test.ts,6
src/refactorings/wrap-in-jsx-fragment/index.ts,6
src/refactorings/toggle-braces/index.ts,6
src/refactorings/remove-jsx-fragment/remove-jsx-fragment.ts,6
src/refactorings/remove-jsx-fragment/remove-jsx-fragment.test.ts,6
src/refactorings/remove-jsx-fragment/index.ts,6
src/refactorings/remove-dead-code/action-provider.ts,6
src/refactorings/merge-with-previous-if-statement/action-provider.ts,6
src/refactorings/extract/extract-variable/parts.test.ts,6
src/refactorings/convert-to-arrow-function/index.ts,6
src/refactorings/convert-if-else-to-switch/action-provider.ts,6
src/refactorings/bubble-up-if-statement/action-provider.ts,6
src/highlights/toggle-highlight/index.ts,6
src/highlights/remove-all-highlights.ts,6
src/editor/adapters/change-signature-webview/change-signature.webview.html,6
src/app.css,6
docs/demo/merge-if-with-previous-if-statement.gif,6
docs/demo/command-palette.png,6
docs/adr/assets/0008-flame-chart.png,6
docs/adr/0004-refactoring-before-recast.gif,6
docs/adr/0004-refactoring-after-recast.gif,6
_templates/refactoring/new/error-reason-enum.ejs.t,6
.husky/pre-commit,6
.github/ISSUE_TEMPLATE/thank_you.md,6
