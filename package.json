{"name": "abracadabra", "version": "9.9.0", "displayName": "Abracadabra, refactor this!", "publisher": "nicoespeon", "description": "Automated refactorings for VS Code, in JavaScript and TypeScript.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nicoespeon/abracadabra.git"}, "homepage": "https://github.com/nicoespeon/abracadabra", "bugs": "https://github.com/nicoespeon/abracadabra/issues", "sponsor": {"url": "https://github.com/sponsors/nicoespeon"}, "keywords": ["refactor", "legacy code", "javascript", "typescript", "react", "vue", "svelte"], "categories": ["Formatters", "Programming Languages", "Other"], "icon": "docs/logo/abracadabra-vignette.png", "galleryBanner": {"color": "#34223A", "theme": "dark"}, "engines": {"vscode": ">=1.100.0", "node": ">=22.0.0"}, "main": "./out/extension.js", "browser": "./out/extension-browser.js", "scripts": {"contrib": "all-contributors", "prepare": "husky", "hygen": "hygen", "new": "hygen refactoring new", "test": "jest", "pretest:contract": "tsup-node 'src/**/*.ts' --loader '.html=text' --out-dir out", "test:contract": "yarn pretest:contract && node ./out/test/run-contract-tests.js", "test:ci": "yarn typecheck && yarn test && yarn test:contract", "typecheck": "tsc -p tsconfig.json --noEmit", "lint": "eslint src --ignore-pattern '**/playground/**'", "build": "node esbuild.config.js --node --browser", "build:node": "node esbuild.config.js --node", "build:browser": "node esbuild.config.js --browser", "prepackage": "rm -rf dist/*.vsix && rm -rf out/", "vscode:prepublish": "yarn build --production", "package": "vsce package --no-dependencies && yarn postpackage", "postpackage": "mkdir -p dist && mv *.vsix dist", "package:install": "code --install-extension dist/*.vsix", "deploy": "yarn deploy:vscode && yarn deploy:ovsx", "deploy:vscode": "vsce publish --no-dependencies", "deploy:ovsx": "ovsx publish --no-dependencies"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/preset-env": "7.28.3", "@babel/preset-typescript": "7.27.1", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.36.0", "@types/babel__traverse": "7.20.6", "@types/chai": "5.2.2", "@types/glob": "9.0.0", "@types/jest": "30.0.0", "@types/jsdom": "27.0.0", "@types/mocha": "10.0.7", "@types/node": "24.3.0", "@types/pluralize": "0.0.33", "@types/sinon": "17.0.2", "@types/vscode": "1.100.0", "@vscode/test-electron": "2.5.2", "all-contributors-cli": "6.26.1", "babel-jest": "30.1.2", "chai": "6.2.0", "esbuild": "0.25.10", "eslint": "9.34.0", "glob": "11.0.3", "globals": "16.3.0", "husky": "9.1.7", "hygen": "6.2.11", "jest": "30.1.2", "jest-environment-jsdom": "30.2.0", "jest-html-loader": "1.0.0", "jsdom": "27.0.0", "lint-staged": "16.1.5", "mocha": "11.7.3", "node-stdlib-browser": "1.3.1", "ovsx": "0.10.5", "prettier": "3.6.2", "prettier-plugin-organize-imports": "4.3.0", "pretty-quick": "4.2.2", "sinon": "21.0.0", "tsup": "8.5.0", "typescript": "5.9.2", "typescript-eslint": "8.41.0", "vsce": "2.15.0"}, "dependencies": {"@babel/parser": "7.28.3", "@babel/traverse": "7.27.1", "@babel/types": "7.21.5", "minimatch": "10.0.3", "pluralize": "8.0.0", "recast": "0.23.11", "ts-pattern": "5.8.0"}, "activationEvents": ["onLanguage:javascript", "onLanguage:javascriptreact", "onLanguage:typescript", "onLanguage:typescriptreact", "onLanguage:vue", "onLanguage:svelte"], "contributes": {"commands": [{"command": "abracadabra.addNumericSeparator", "title": "Add Numeric Separator", "category": "Abracadabra"}, {"command": "abracadabra.changeSignature", "title": "Change Signature", "category": "Abracadabra"}, {"command": "abracadabra.convertForToForEach", "title": "Convert For-Loop to ForEach", "category": "Abracadabra"}, {"command": "abracadabra.convertForEachToForOf", "title": "Convert ForEach to For-Of", "category": "Abracadabra"}, {"command": "abracadabra.convertFunctionDeclarationToArrowFunction", "title": "Convert to Arrow Function", "category": "Abracadabra"}, {"command": "abracadabra.convertGuardToIf", "title": "Convert Guard to If", "category": "Abracadabra"}, {"command": "abracadabra.convertIfElseToTernary", "title": "Convert If/Else to Ternary", "category": "Abracadabra"}, {"command": "abracadabra.convertIfElseToSwitch", "title": "Convert If/Else to Switch", "category": "Abracadabra"}, {"command": "abracadabra.convertSwitchToIfElse", "title": "Convert Switch to If/Else", "category": "Abracadabra"}, {"command": "abracadabra.convertTernaryToIfElse", "title": "Convert Ternary to If/Else", "category": "Abracadabra"}, {"command": "abracadabra.convertToTemplateLiteral", "title": "Convert to Template Literal", "category": "Abracadabra"}, {"command": "abracadabra.convertLetToConst", "title": "Convert let to const", "category": "Abracadabra"}, {"command": "abracadabra.createFactoryForConstructor", "title": "Create Factory for Constructor", "category": "Abracadabra"}, {"command": "abracadabra.extract", "title": "Extract", "category": "Abracadabra"}, {"command": "abracadabra.extractFunction", "title": "Extract Function", "category": "Abracadabra"}, {"command": "abracadabra.extractGenericType", "title": "Extract Generic Type", "category": "Abracadabra"}, {"command": "abracadabra.extractInterface", "title": "Extract Interface", "category": "Abracadabra"}, {"command": "abracadabra.extractParameter", "title": "Extract Parameter", "category": "Abracadabra"}, {"command": "abracadabra.flipIfElse", "title": "Flip If/Else", "category": "Abracadabra"}, {"command": "abracadabra.flipTernary", "title": "<PERSON><PERSON>", "category": "Abracadabra"}, {"command": "abracadabra.flipOperator", "title": "Flip Operator", "category": "Abracadabra"}, {"command": "abracadabra.inline", "title": "Inline", "category": "Abracadabra"}, {"command": "abracadabra.liftUpConditional", "title": "Lift Up Conditional", "category": "Abracadabra"}, {"command": "abracadabra.mergeIfStatements", "title": "Merge If Statements", "category": "Abracadabra"}, {"command": "abracadabra.mergeWithPreviousIfStatement", "title": "Merge With Previous If Statement", "category": "Abracadabra"}, {"command": "abracadabra.moveLastStatementOutOfIfElse", "title": "Move Last Statement Out Of If/Else", "category": "Abracadabra"}, {"command": "abracadabra.moveStatementDown", "title": "Move Statement Down", "category": "Abracadabra"}, {"command": "abracadabra.moveStatementUp", "title": "Move Statement Up", "category": "Abracadabra"}, {"command": "abracadabra.invertBooleanLogic", "title": "Invert Boolean Logic", "category": "Abracadabra"}, {"command": "abracadabra.removeDeadCode", "title": "Remove Dead Code", "category": "Abracadabra"}, {"command": "abracadabra.removeJsxFragment", "title": "Remove JSX Fragment", "category": "Abracadabra"}, {"command": "abracadabra.removeRedundantElse", "title": "Remove Redundant Else", "category": "Abracadabra"}, {"command": "abracadabra.renameSymbol", "title": "Rename Symbol", "category": "Abracadabra"}, {"command": "abracadabra.replaceBinaryWithAssignment", "title": "Replace Binary with Expression", "category": "Abracadabra"}, {"command": "abracadabra.simplifyBoolean", "title": "Simplify Boolean", "category": "Abracadabra"}, {"command": "abracadabra.simplifyTernary", "title": "Simplify Ternary", "category": "Abracadabra"}, {"command": "abracadabra.splitDeclarationAndInitialization", "title": "Split Declaration and Initialization", "category": "Abracadabra"}, {"command": "abracadabra.splitIfStatement", "title": "Split If Statement", "category": "Abracadabra"}, {"command": "abracadabra.splitMultipleDeclarations", "title": "Split Multiple Declarations", "category": "Abracadabra"}, {"command": "abracadabra.toggleBraces", "title": "Toggle Braces", "category": "Abracadabra"}, {"command": "abracadabra.toggleHighlight", "title": "Toggle Highlight", "category": "Abracadabra"}, {"command": "abracadabra.refreshHighlights", "title": "Refresh Highlights", "category": "Abracadabra"}, {"command": "abracadabra.removeAllHighlights", "title": "Remove All Highlights", "category": "Abracadabra"}, {"command": "abracadabra.wrapInJsxFragment", "title": "Wrap in JSX Fragment", "category": "Abracadabra"}], "keybindings": [{"command": "abracadabra.quickFix", "key": "alt+enter", "when": "editorTextFocus"}, {"command": "abracadabra.renameSymbol", "key": "f2", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.toggleHighlight", "key": "ctrl+alt+h", "mac": "ctrl+h", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.refreshHighlights", "key": "shift+alt+h", "mac": "ctrl+alt+h", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.removeAllHighlights", "key": "shift+ctrl+alt+h", "mac": "shift+ctrl+h", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.extract", "key": "ctrl+alt+v", "mac": "cmd+alt+v", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.extractFunction", "key": "ctrl+alt+m", "mac": "cmd+alt+m", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.inline", "key": "ctrl+alt+n", "mac": "cmd+alt+n", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.moveStatementDown", "key": "alt+shift+d", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}, {"command": "abracadabra.moveStatementUp", "key": "alt+shift+u", "when": "editorTextFocus && (editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte)"}], "configuration": {"title": "Abracadabra", "properties": {"abracadabra.ignoredFolders": {"type": "array", "default": ["node_modules"], "description": "Folders where it won't propose Quick Fixes (defaults: `node_modules`)."}, "abracadabra.ignoredPatterns": {"type": "array", "default": ["dist/*", "build/*"], "description": "Glob patterns where it won't propose Quick Fixes (defaults: `dist/*`, `build/*`)."}, "abracadabra.maxFileLinesCount": {"type": "number", "default": 10000, "description": "Don't propose refactorings on files that have more lines of code than this threshold."}, "abracadabra.maxFileSizeInKb": {"type": "number", "default": 250, "description": "Don't propose refactorings on files that are bigger than this threshold (kB)."}, "abracadabra.autoConvertToTemplateLiteral": {"type": "boolean", "default": true, "description": "Automatically convert a string into a template literal when you insert `${}`."}, "abracadabra.addNumericSeparator.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.changeSignature.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertForToForEach.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertForEachToForOf.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertFunctionDeclarationToArrowFunction.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertGuardToIf.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertIfElseToTernary.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertIfElseToSwitch.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertSwitchToIfElse.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertTernaryToIfElse.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertToTemplateLiteral.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.convertLetToConst.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.createFactoryForConstructor.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.extractGenericType.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.extractInterface.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.extractParameter.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.flipIfElse.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.flipTernary.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.flipOperator.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.inline.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.liftUpConditional.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.mergeIfStatements.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.mergeWithPreviousIfStatement.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.moveLastStatementOutOfIfElse.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.invertBooleanLogic.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.removeDeadCode.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.removeJsxFragment.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.removeRedundantElse.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.replaceBinaryWithAssignment.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.simplifyBoolean.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.simplifyTernary.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.splitDeclarationAndInitialization.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.splitIfStatement.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.splitMultipleDeclarations.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.toggleBraces.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}, "abracadabra.wrapInJsxFragment.showInQuickFix": {"type": "boolean", "default": true, "description": "Check if it should appear in the Quick Fix suggestions when it can be executed"}}}, "menus": {"commandPalette": [{"command": "abracadabra.addNumericSeparator", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.changeSignature", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertForToForEach", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertForEachToForOf", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertFunctionDeclarationToArrowFunction", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertGuardToIf", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertIfElseToTernary", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertIfElseToSwitch", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertSwitchToIfElse", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertTernaryToIfElse", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertToTemplateLiteral", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.convertLetToConst", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.createFactoryForConstructor", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.extract", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.extractFunction", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.extractGenericType", "when": "editorLangId == typescript || editorLangId == typescriptreact"}, {"command": "abracadabra.extractInterface", "when": "editorLangId == typescript || editorLangId == typescriptreact"}, {"command": "abracadabra.extractParameter", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.flipIfElse", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.flipTernary", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.flipOperator", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.inline", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.liftUpConditional", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.mergeIfStatements", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.mergeWithPreviousIfStatement", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.moveLastStatementOutOfIfElse", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.moveStatementDown", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.moveStatementUp", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.invertBooleanLogic", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.removeDeadCode", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.removeJsxFragment", "when": "editorLangId == javascriptreact || editorLangId == typescriptreact"}, {"command": "abracadabra.removeRedundantElse", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.renameSymbol", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.replaceBinaryWithAssignment", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.simplifyBoolean", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.simplifyTernary", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.splitDeclarationAndInitialization", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.splitIfStatement", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.splitMultipleDeclarations", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.toggleBraces", "when": "editorLangId == javascript || editorLangId == javascriptreact || editorLangId == typescript || editorLangId == typescriptreact || editorLangId == vue || editorLangId == svelte"}, {"command": "abracadabra.wrapInJsxFragment", "when": "editorLangId == javascriptreact || editorLangId == typescriptreact"}]}}, "packageManager": "yarn@4.4.0"}