# Package.json Generation Scripts

This directory contains scripts that automatically generate the `contributes` section of `package.json` from the refactoring configurations in the codebase. This eliminates duplication and ensures consistency between the code and the VS Code extension manifest.

## Overview

Previously, the VS Code extension configuration required manual maintenance of:
- Command definitions in `package.json`
- Menu entries with language constraints
- Keeping titles and keys synchronized between code and manifest

Now, this is all generated automatically from the refactoring configuration files.

## Scripts

### `extract-refactoring-metadata.js`
Extracts metadata from individual refactoring configuration files (`src/refactorings/*/index.ts`).

**Usage:**
```bash
node scripts/extract-refactoring-metadata.js
```

**What it does:**
- Scans all refactoring directories
- Parses TypeScript config files to extract:
  - Command keys
  - Titles
  - Action provider information
- Returns structured metadata for use by other scripts

### `generate-package-contributes.js`
Generates the `contributes.commands` and `contributes.menus.commandPalette` sections of `package.json`.

**Usage:**
```bash
yarn generate:package
# or
node scripts/generate-package-contributes.js
```

**What it does:**
- Uses metadata extraction to get refactoring information
- Maps refactorings to language groups (TypeScript-only, React-only, all languages)
- Generates VS Code command definitions
- Generates menu entries with appropriate language constraints
- Updates `package.json` in place

### `validate-package-contributes.js`
Validates that the `package.json` contributes section is up-to-date.

**Usage:**
```bash
yarn validate:package
# or
node scripts/validate-package-contributes.js
```

**What it does:**
- Generates expected configuration from current refactoring files
- Compares with actual `package.json` content
- Exits with error code 1 if out of sync
- Used in CI to ensure package.json stays current

## Integration

### Build Process
The generation script runs automatically before builds:
```json
{
  "scripts": {
    "prebuild": "yarn generate:package",
    "build": "node esbuild.config.js --node --browser"
  }
}
```

### CI Validation
The validation script runs in CI to catch inconsistencies:
```json
{
  "scripts": {
    "test:ci": "yarn typecheck && yarn validate:package && yarn test && yarn test:contract"
  }
}
```

## Language Groups

Refactorings are organized into language groups:

### TypeScript Only
- `extractGenericType`
- `extractInterface`

**Languages:** `typescript`, `typescriptreact`

### React Only
- `wrapInJsxFragment`
- `removeJsxFragment`

**Languages:** `javascriptreact`, `typescriptreact`

### All Languages
All other refactorings support the full language set:

**Languages:** `javascript`, `javascriptreact`, `typescript`, `typescriptreact`, `vue`, `svelte`

## Adding New Refactorings

When adding a new refactoring:

1. **Create the refactoring** using the existing template system
2. **Add to extension.ts** in the appropriate language group
3. **Run generation** - the package.json will be updated automatically

No manual package.json editing required!

## Testing

The generation system includes comprehensive tests:

```bash
npx jest scripts/generate-package-contributes.test.js
```

Tests cover:
- Metadata extraction accuracy
- Package.json structure validation
- Language constraint correctness
- Script integration verification

## Benefits

✅ **Single source of truth** - All command metadata lives in code
✅ **Automatic consistency** - No risk of forgetting to update package.json
✅ **Easier maintenance** - Adding refactorings only requires code changes
✅ **Type safety** - Command keys and titles validated at build time
✅ **Reduced errors** - Eliminates manual copy-paste mistakes
✅ **CI validation** - Ensures package.json stays up-to-date

## Troubleshooting

### "Commands section is out of date" error
Run `yarn generate:package` to update the package.json.

### Missing refactoring in package.json
1. Check that the refactoring config file has a `title` field
2. Verify it's imported and added to the appropriate language group in `extension.ts`
3. Run `yarn generate:package`

### Language constraints not working
1. Check the language group assignment in the generation script
2. Verify the `when` clause in the generated menu entries
3. Test with `yarn validate:package`
