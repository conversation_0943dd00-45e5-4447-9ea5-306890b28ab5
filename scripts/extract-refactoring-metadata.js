#!/usr/bin/env node

/**
 * Extract refactoring metadata from TypeScript configuration files
 * This reads the actual refactoring config files to get titles and other metadata
 */

import { readFileSync, existsSync, readdirSync } from 'fs';
import { join } from 'path';

/**
 * Extract refactoring metadata from a TypeScript config file
 * @param {string} filePath - Path to the refactoring config file
 * @returns {Object|null} - Extracted metadata or null if not found
 */
export function extractRefactoringMetadata(filePath) {
  try {
    const content = readFileSync(filePath, 'utf8');

    // Look for the config object definition
    const configMatch = content.match(/const config: RefactoringWith.*Config.*= \{([\s\S]*?)\};/);
    if (!configMatch) {
      return null;
    }

    const configContent = configMatch[1];

    // Extract command metadata
    const commandMatch = configContent.match(/command:\s*\{([\s\S]*?)\}/);
    if (!commandMatch) {
      return null;
    }

    const commandContent = commandMatch[1];

    // Extract key
    const keyMatch = commandContent.match(/key:\s*["']([^"']+)["']/);
    const key = keyMatch ? keyMatch[1] : null;

    // Extract title
    const titleMatch = commandContent.match(/title:\s*["']([^"']+)["']/);
    const title = titleMatch ? titleMatch[1] : null;

    // Extract action provider message if present
    const actionProviderMatch = configContent.match(/actionProvider:\s*\{([\s\S]*?)\}/);
    let message = null;
    if (actionProviderMatch) {
      const actionProviderContent = actionProviderMatch[1];
      const messageMatch = actionProviderContent.match(/message:\s*["']([^"']+)["']/);
      message = messageMatch ? messageMatch[1] : null;
    }

    return {
      key,
      title,
      message,
      hasActionProvider: !!actionProviderMatch
    };
  } catch (error) {
    console.warn(`Warning: Could not parse ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Scan all refactoring directories and extract metadata
 * @returns {Object} - Map of refactoring key to metadata
 */
export function scanRefactoringMetadata() {
  const refactoringsDir = join(__dirname, '../src/refactorings');
  const metadata = {};

  if (!existsSync(refactoringsDir)) {
    throw new Error(`Refactorings directory not found: ${refactoringsDir}`);
  }

  const entries = readdirSync(refactoringsDir, { withFileTypes: true });

  for (const entry of entries) {
    if (entry.isDirectory()) {
      const indexPath = join(refactoringsDir, entry.name, 'index.ts');
      if (existsSync(indexPath)) {
        const refactoringMetadata = extractRefactoringMetadata(indexPath);
        if (refactoringMetadata && refactoringMetadata.key) {
          metadata[refactoringMetadata.key] = refactoringMetadata;
        }
      }
    }
  }

  return metadata;
}

/**
 * Get special commands metadata (highlights, quickFix, etc.)
 * @returns {Array} - Array of special command metadata
 */
export function getSpecialCommandsMetadata() {
  return [
    {
      key: "quickFix",
      title: "Quick Fix",
      languages: [], // No menu entry
      hasActionProvider: false
    },
    {
      key: "toggleHighlight",
      title: "Toggle Highlight",
      languages: ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "svelte"],
      hasActionProvider: false
    },
    {
      key: "refreshHighlights",
      title: "Refresh Highlights",
      languages: ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "svelte"],
      hasActionProvider: false
    },
    {
      key: "removeAllHighlights",
      title: "Remove All Highlights",
      languages: ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "svelte"],
      hasActionProvider: false
    }
  ];
}

if (require.main === module) {
  // Test the metadata extraction
  try {
    console.log('🔍 Scanning refactoring metadata...');
    const metadata = scanRefactoringMetadata();
    const specialCommands = getSpecialCommandsMetadata();

    console.log(`✅ Found ${Object.keys(metadata).length} refactorings`);
    console.log(`✅ Found ${specialCommands.length} special commands`);

    // Show a few examples
    const examples = Object.entries(metadata).slice(0, 3);
    console.log('\n📋 Examples:');
    examples.forEach(([key, data]) => {
      console.log(`  ${key}: "${data.title}" (hasActionProvider: ${data.hasActionProvider})`);
    });

  } catch (error) {
    console.error('❌ Error scanning metadata:', error);
    process.exit(1);
  }
}
