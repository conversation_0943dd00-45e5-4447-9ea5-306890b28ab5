#!/usr/bin/env node

/**
 * Generate the "contributes" section of package.json from refactoring configurations
 * This eliminates duplication between code and package.json
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { scanRefactoringMetadata, getSpecialCommandsMetadata } from './extract-refactoring-metadata';

// Parse the extension.ts file to extract refactoring configurations
async function loadRefactorings() {
  // Get all refactoring metadata
  const refactoringMetadata = scanRefactoringMetadata();

  // Define language groups based on the extension.ts structure
  // We'll map the actual refactoring keys to their language groups
  const languageGroups = {
    typescriptOnly: {
      languages: ["typescript", "typescriptreact"],
      refactorings: ["extractGenericType", "extractInterface"]
    },
    reactOnly: {
      languages: ["javascriptreact", "typescriptreact"],
      refactorings: ["wrapInJsxFragment", "removeJsxFragment"]
    },
    allLanguages: {
      languages: ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "svelte"],
      refactorings: []
    }
  };

  // Add all other refactorings to allLanguages
  const specialGroupKeys = new Set([
    ...languageGroups.typescriptOnly.refactorings,
    ...languageGroups.reactOnly.refactorings
  ]);

  Object.keys(refactoringMetadata).forEach(key => {
    if (!specialGroupKeys.has(key)) {
      languageGroups.allLanguages.refactorings.push(key);
    }
  });

  // Special commands that are handled separately
  const specialCommands = getSpecialCommandsMetadata();

  return { languageGroups, specialCommands };
}

// Load refactoring titles from individual config files
async function loadRefactoringTitles() {
  // Use the metadata extraction to get actual titles from config files
  const refactoringMetadata = scanRefactoringMetadata();
  const refactoringTitles = {};

  // Extract titles from metadata
  Object.entries(refactoringMetadata).forEach(([key, metadata]) => {
    if (metadata.title) {
      refactoringTitles[key] = metadata.title;
    }
  });

  return refactoringTitles;
}

function generateCommands(languageGroups, specialCommands, refactoringTitles) {
  const commands = [];

  // Add special commands
  specialCommands.forEach(({ key, title }) => {
    commands.push({
      command: `abracadabra.${key}`,
      title,
      category: "Abracadabra"
    });
  });

  // Add refactoring commands
  Object.values(languageGroups).forEach(({ refactorings }) => {
    refactorings.forEach(refactoringKey => {
      const title = refactoringTitles[refactoringKey];
      if (title) {
        commands.push({
          command: `abracadabra.${refactoringKey}`,
          title,
          category: "Abracadabra"
        });
      }
    });
  });

  // Remove duplicates and sort
  const uniqueCommands = commands.filter((command, index, self) =>
    index === self.findIndex(c => c.command === command.command)
  );

  return uniqueCommands.sort((a, b) => a.command.localeCompare(b.command));
}

function generateMenus(languageGroups, specialCommands) {
  const commandPalette = [];

  // Create a map of command to languages
  const commandToLanguages = {};

  // Add special commands
  specialCommands.forEach(({ key, languages }) => {
    if (languages.length > 0) {
      commandToLanguages[`abracadabra.${key}`] = languages;
    }
  });

  // Add refactoring commands
  Object.values(languageGroups).forEach(({ languages, refactorings }) => {
    refactorings.forEach(refactoringKey => {
      const commandKey = `abracadabra.${refactoringKey}`;
      commandToLanguages[commandKey] = languages;
    });
  });

  // Generate menu entries
  Object.entries(commandToLanguages).forEach(([command, languages]) => {
    if (languages.length > 0) {
      const whenClause = languages.map(lang => `editorLangId == ${lang}`).join(' || ');
      commandPalette.push({
        command,
        when: whenClause
      });
    }
  });

  return commandPalette.sort((a, b) => a.command.localeCompare(b.command));
}

async function main() {
  try {
    console.log('🔄 Generating package.json contributes section...');

    const { languageGroups, specialCommands } = await loadRefactorings();
    const refactoringTitles = await loadRefactoringTitles();

    const commands = generateCommands(languageGroups, specialCommands, refactoringTitles);
    const menus = generateMenus(languageGroups, specialCommands);

    const contributes = {
      commands,
      menus: {
        commandPalette: menus
      }
    };

    // Read current package.json
    const packageJsonPath = join(__dirname, '../package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

    // Update only the commands and menus.commandPalette sections
    packageJson.contributes.commands = contributes.commands;
    packageJson.contributes.menus.commandPalette = contributes.menus.commandPalette;

    // Write back to package.json
    writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');

    console.log(`✅ Generated ${commands.length} commands and ${menus.length} menu entries`);
    console.log('📦 Updated package.json successfully');

  } catch (error) {
    console.error('❌ Error generating package.json contributes:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export default { loadRefactorings, loadRefactoringTitles, generateCommands, generateMenus };
