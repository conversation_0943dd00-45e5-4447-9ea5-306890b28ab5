/**
 * Tests for the package.json generation system
 */

import { existsSync, readFileSync, statSync } from "fs";
import { join } from "path";
import {
  scanRefactoringMetadata, getSpecialCommandsMetadata
} from "./extract-refactoring-metadata";

describe("Package.json Generation System", () => {
  describe("Metadata Extraction", () => {
    test("should extract refactoring metadata from config files", () => {
      const metadata = scanRefactoringMetadata();

      expect(metadata).toBeDefined();
      expect(typeof metadata).toBe("object");
      expect(Object.keys(metadata).length).toBeGreaterThan(0);

      // Check that we have some expected refactorings
      expect(metadata.addNumericSeparator).toBeDefined();
      expect(metadata.addNumericSeparator.title).toBe("Add Numeric Separator");
      expect(metadata.addNumericSeparator.key).toBe("addNumericSeparator");
      expect(metadata.addNumericSeparator.hasActionProvider).toBe(true);
    });

    test("should get special commands metadata", () => {
      const specialCommands = getSpecialCommandsMetadata();

      expect(Array.isArray(specialCommands)).toBe(true);
      expect(specialCommands.length).toBeGreaterThan(0);

      // Check for expected special commands
      const quickFix = specialCommands.find((cmd) => cmd.key === "quickFix");
      expect(quickFix).toBeDefined();
      expect(quickFix.title).toBe("Quick Fix");
      expect(quickFix.languages).toEqual([]);

      const toggleHighlight = specialCommands.find(
        (cmd) => cmd.key === "toggleHighlight"
      );
      expect(toggleHighlight).toBeDefined();
      expect(toggleHighlight.title).toBe("Toggle Highlight");
      expect(toggleHighlight.languages).toContain("javascript");
    });
  });

  describe("Package.json Structure", () => {
    test("should have valid package.json structure", () => {
      const packageJsonPath = join(__dirname, "../package.json");
      expect(existsSync(packageJsonPath)).toBe(true);

      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

      // Check that contributes section exists
      expect(packageJson.contributes).toBeDefined();
      expect(packageJson.contributes.commands).toBeDefined();
      expect(packageJson.contributes.menus).toBeDefined();
      expect(packageJson.contributes.menus.commandPalette).toBeDefined();

      // Check that commands are properly structured
      const commands = packageJson.contributes.commands;
      expect(Array.isArray(commands)).toBe(true);
      expect(commands.length).toBeGreaterThan(0);

      commands.forEach((command) => {
        expect(command.command).toBeDefined();
        expect(command.title).toBeDefined();
        expect(command.category).toBe("Abracadabra");
        expect(command.command).toMatch(/^abracadabra\./);
      });

      // Check that menus are properly structured
      const menus = packageJson.contributes.menus.commandPalette;
      expect(Array.isArray(menus)).toBe(true);
      expect(menus.length).toBeGreaterThan(0);

      menus.forEach((menu) => {
        expect(menu.command).toBeDefined();
        expect(menu.when).toBeDefined();
        expect(menu.command).toMatch(/^abracadabra\./);
        expect(menu.when).toMatch(/editorLangId ==/);
      });
    });

    test("should have all refactorings represented in package.json", () => {
      const metadata = scanRefactoringMetadata();
      const packageJsonPath = join(__dirname, "../package.json");
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

      const commands = packageJson.contributes.commands;
      const commandKeys = commands.map((cmd) =>
        cmd.command.replace("abracadabra.", "")
      );

      // Check that all refactorings with titles are in the commands
      Object.entries(metadata).forEach(([key, data]) => {
        if (data.title) {
          expect(commandKeys).toContain(key);
        }
      });
    });

    test("should have consistent command and menu entries", () => {
      const packageJsonPath = join(__dirname, "../package.json");
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

      const commands = packageJson.contributes.commands;
      const menus = packageJson.contributes.menus.commandPalette;

      const commandKeys = new Set(commands.map((cmd) => cmd.command));
      const menuKeys = new Set(menus.map((menu) => menu.command));

      // Every menu entry should have a corresponding command
      menus.forEach((menu) => {
        expect(commandKeys.has(menu.command)).toBe(true);
      });

      // Commands that should have menu entries (excluding quickFix)
      const commandsWithoutMenus = [...commandKeys].filter(
        (cmd) => !menuKeys.has(cmd)
      );
      expect(commandsWithoutMenus).toEqual(["abracadabra.quickFix"]);
    });
  });

  describe("Language Support", () => {
    test("should have correct language constraints", () => {
      const packageJsonPath = join(__dirname, "../package.json");
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

      const menus = packageJson.contributes.menus.commandPalette;

      // Check TypeScript-only commands
      const typescriptOnlyCommands = ["extractGenericType", "extractInterface"];
      typescriptOnlyCommands.forEach((cmdKey) => {
        const menu = menus.find((m) => m.command === `abracadabra.${cmdKey}`);
        if (menu) {
          expect(menu.when).toMatch(/typescript/);
          expect(menu.when).not.toMatch(/javascript[^r]/); // Should not match plain javascript
        }
      });

      // Check React-only commands
      const reactOnlyCommands = ["wrapInJsxFragment", "removeJsxFragment"];
      reactOnlyCommands.forEach((cmdKey) => {
        const menu = menus.find((m) => m.command === `abracadabra.${cmdKey}`);
        if (menu) {
          expect(menu.when).toMatch(/javascriptreact|typescriptreact/);
        }
      });

      // Check all-language commands
      const allLanguageCommands = ["addNumericSeparator", "changeSignature"];
      allLanguageCommands.forEach((cmdKey) => {
        const menu = menus.find((m) => m.command === `abracadabra.${cmdKey}`);
        if (menu) {
          expect(menu.when).toMatch(/javascript/);
          expect(menu.when).toMatch(/typescript/);
          expect(menu.when).toMatch(/vue/);
          expect(menu.when).toMatch(/svelte/);
        }
      });
    });
  });

  describe("Generation Scripts", () => {
    test("should have generation and validation scripts", () => {
      const generateScript = join(
        __dirname,
        "generate-package-contributes.js"
      );
      const validateScript = join(
        __dirname,
        "validate-package-contributes.js"
      );
      const extractScript = join(
        __dirname,
        "extract-refactoring-metadata.js"
      );

      expect(existsSync(generateScript)).toBe(true);
      expect(existsSync(validateScript)).toBe(true);
      expect(existsSync(extractScript)).toBe(true);

      // Check that scripts are executable (on Unix systems)
      if (process.platform !== 'win32') {
        const generateStats = statSync(generateScript);
        const validateStats = statSync(validateScript);
        const extractStats = statSync(extractScript);

        expect(generateStats.mode & parseInt("111", 8)).toBeTruthy();
        expect(validateStats.mode & parseInt("111", 8)).toBeTruthy();
        expect(extractStats.mode & parseInt("111", 8)).toBeTruthy();
      }
    });

    test("should have npm scripts configured", () => {
      const packageJsonPath = join(__dirname, "../package.json");
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

      expect(packageJson.scripts["generate:package"]).toBeDefined();
      expect(packageJson.scripts["validate:package"]).toBeDefined();
      expect(packageJson.scripts["prebuild"]).toContain("generate:package");
      expect(packageJson.scripts["test:ci"]).toContain("validate:package");
    });
  });
});
