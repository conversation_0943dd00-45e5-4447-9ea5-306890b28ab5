import { RefactoringWithActionProviderConfig__DEPRECATED } from "../../refactorings";
import {
  createVisitor,
  getNegatedOperator,
  invertBooleanLogic
} from "./invert-boolean-logic";

const config: RefactoringWithActionProviderConfig__DEPRECATED = {
  command: {
    key: "invertBooleanLogic",
    operation: invertBooleanLogic,
    title: "Invert Boolean Logic (De <PERSON>'s Law)"
  },
  actionProvider: {
    message: "Invert boolean logic (<PERSON>'s law)",
    createVisitor,
    updateMessage(path) {
      const operator = getNegatedOperator(path.node);
      return operator
        ? `Invert boolean logic (use ${operator} instead) (<PERSON>'s law)`
        : "Invert boolean logic (<PERSON>'s law)";
    }
  }
};

export default config;
