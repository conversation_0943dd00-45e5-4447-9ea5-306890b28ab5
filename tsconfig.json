{"compilerOptions": {"module": "commonjs", "target": "es2021", "outDir": "out", "lib": ["es6", "es2016", "es2021", "ESNext.Array"], "sourceMap": true, "rootDir": "src", "strict": true, "declaration": false, "removeComments": true, "esModuleInterop": true, "noImplicitReturns": true, "noImplicitAny": true, "noImplicitThis": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": true, "noUnusedLocals": true, "resolveJsonModule": true, "skipLibCheck": true, "isolatedModules": true, "incremental": true, "tsBuildInfoFile": "out/tsconfig.tsbuildinfo"}, "exclude": ["node_modules", "out", ".vscode-test", "gitignore", "src/playground"]}